#!/usr/bin/env python3
"""
Database initialization script for production deployment
数据库初始化脚本 - 用于生产部署
"""

import sys
import os
import logging
from pathlib import Path

# Add project root to Python path
sys.path.insert(0, '/workspace')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_database_connection():
    """检查数据库连接"""
    try:
        from app.database import check_database_connection
        return check_database_connection()
    except Exception as e:
        logger.error(f"Database connection check failed: {e}")
        return False

def check_tables_exist():
    """检查必要的表是否存在"""
    try:
        from sqlalchemy import create_engine, text
        from app.config import settings
        
        engine = create_engine(settings.database_url)
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = DATABASE() 
                AND table_name IN ('auth_sessions', 'users', 'pdf_tasks', 'system_logs')
            """))
            
            existing_tables = [row[0] for row in result.fetchall()]
            required_tables = ['auth_sessions', 'users', 'pdf_tasks', 'system_logs']
            
            return existing_tables, required_tables
            
    except Exception as e:
        logger.error(f"Failed to check tables: {e}")
        return [], []

def init_with_sqlalchemy():
    """使用 SQLAlchemy 初始化数据库"""
    try:
        from app.database import init_db
        init_db()
        logger.info("✅ Database initialized successfully with SQLAlchemy")
        return True
    except Exception as e:
        logger.error(f"SQLAlchemy initialization failed: {e}")
        return False

def init_with_sql_script():
    """使用 SQL 脚本初始化数据库"""
    try:
        import subprocess
        from urllib.parse import urlparse
        from app.config import settings
        
        sql_file = '/workspace/init_database.sql'
        if not os.path.exists(sql_file):
            logger.error("SQL initialization script not found")
            return False
            
        logger.info("📄 Executing SQL initialization script...")
        
        # 解析数据库连接参数
        parsed_url = urlparse(settings.database_url)
        
        # 构建 mysql 命令
        mysql_env = os.environ.copy()
        mysql_env['MYSQL_PWD'] = parsed_url.password  # 使用环境变量传递密码，更安全
        
        mysql_cmd = [
            'mysql',
            f'--host={parsed_url.hostname}',
            f'--port={parsed_url.port or 3306}',
            f'--user={parsed_url.username}',
            parsed_url.path.lstrip('/'),
            f'--execute=source {sql_file}'
        ]
        
        result = subprocess.run(
            mysql_cmd, 
            capture_output=True, 
            text=True, 
            env=mysql_env
        )
        
        if result.returncode == 0:
            logger.info("✅ SQL initialization script executed successfully")
            return True
        else:
            logger.warning(f"SQL script execution had warnings: {result.stderr}")
            # 检查是否是严重错误还是警告
            if "ERROR" in result.stderr.upper():
                logger.error("SQL script execution failed with errors")
                return False
            else:
                logger.info("SQL script completed with warnings (likely non-critical)")
                return True
                
    except Exception as e:
        logger.error(f"SQL script initialization failed: {e}")
        return False

def verify_initialization():
    """验证初始化是否成功"""
    try:
        existing_tables, required_tables = check_tables_exist()
        
        if len(existing_tables) == len(required_tables):
            logger.info(f"✅ All required tables exist: {existing_tables}")
            return True
        else:
            missing_tables = set(required_tables) - set(existing_tables)
            logger.warning(f"⚠️  Missing tables: {missing_tables}")
            logger.info(f"Existing tables: {existing_tables}")
            return False
            
    except Exception as e:
        logger.error(f"Verification failed: {e}")
        return False

def main():
    """主初始化流程"""
    logger.info("🔧 Starting database initialization...")
    
    # 1. 检查数据库连接
    if not check_database_connection():
        logger.error("❌ Database connection failed, cannot proceed")
        sys.exit(1)
    
    logger.info("✅ Database connection successful")
    
    # 2. 检查现有表
    existing_tables, required_tables = check_tables_exist()
    
    if len(existing_tables) == len(required_tables):
        logger.info(f"✅ All required tables already exist ({len(existing_tables)}/{len(required_tables)})")
        logger.info("Database initialization not needed")
        return
    
    if len(existing_tables) > 0:
        logger.info(f"⚠️  Found {len(existing_tables)}/{len(required_tables)} tables")
        logger.info("Missing tables will be created")
    else:
        logger.info("📋 No tables found, performing full initialization")
    
    # 3. 尝试初始化
    success = False
    
    # 优先使用 SQLAlchemy 初始化
    logger.info("Attempting SQLAlchemy initialization...")
    if init_with_sqlalchemy():
        success = True
    else:
        # 如果 SQLAlchemy 失败，尝试 SQL 脚本
        logger.info("Attempting SQL script initialization...")
        if init_with_sql_script():
            success = True
    
    if not success:
        logger.error("❌ Both initialization methods failed")
        sys.exit(1)
    
    # 4. 验证初始化结果
    if verify_initialization():
        logger.info("🎉 Database initialization completed successfully")
    else:
        logger.error("❌ Database initialization verification failed")
        sys.exit(1)

if __name__ == "__main__":
    main()