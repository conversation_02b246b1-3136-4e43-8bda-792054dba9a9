# ===========================================
# 必然中医医案导出系统 - 后端开发环境配置
# ===========================================

# 应用基础配置
APP_NAME=必然中医 - 医案导出系统
APP_VERSION=1.0.0
DEBUG=true
LOG_LEVEL=DEBUG

# 安全密钥 (开发环境使用固定密钥，生产环境必须更换)
SECRET_KEY=dev-secret-key-for-development-only
JWT_SECRET_KEY=dev-jwt-secret-key-for-development-only
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# ===========================================
# 数据库配置 - MySQL 8.0
# ===========================================

# MySQL 连接信息 (开发环境)
DATABASE_URL=mysql+pymysql://brzy_dev:brzy_dev_pass@localhost:3306/brzy_medical_dev

# 数据库连接池配置
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=5

# ===========================================
# Redis 配置 - Redis 7 (开发环境)
# ===========================================

# Redis 连接信息
REDIS_URL=redis://:dev_redis_pass@localhost:6379/0
REDIS_PASSWORD=dev_redis_pass

# ===========================================
# 必然中医原始 API 配置
# ===========================================

# 原始 API 地址
ORIGINAL_API_URL=https://api.haoniuzhongyi.top:9090

# API 超时设置
ORIGINAL_API_TIMEOUT=30

# ===========================================
# CORS 跨域配置 (开发环境)
# ===========================================

# 允许的域名
CORS_ORIGINS=http://localhost:5173,http://127.0.0.1:5173,http://localhost:3000

# ===========================================
# 文件存储配置
# ===========================================

# 上传目录
UPLOAD_DIR=./uploads
MAX_UPLOAD_SIZE=10485760

# PDF 生成配置
PDF_FONT_DIR=./fonts
PDF_TEMP_DIR=./temp

# ===========================================
# 日志配置
# ===========================================

# 日志文件路径
LOG_FILE=./logs/app.log

# ===========================================
# WebSocket 配置
# ===========================================

# WebSocket 心跳间隔 (秒)
WS_HEARTBEAT_INTERVAL=30

# ===========================================
# 服务器配置
# ===========================================

# 服务器端口
PORT=8000

# 允许的主机
ALLOWED_HOSTS=*
