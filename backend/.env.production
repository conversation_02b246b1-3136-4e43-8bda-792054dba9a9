# ===========================================
# 必然中医医案导出系统 - 后端生产环境配置
# ===========================================

# 应用基础配置
APP_NAME=必然中医 - 医案导出系统
APP_VERSION=1.0.0
DEBUG=false
LOG_LEVEL=INFO

# 安全密钥 (生产环境必须更换为强密钥)
SECRET_KEY=CHANGE-THIS-TO-STRONG-SECRET-KEY-IN-PRODUCTION
JWT_SECRET_KEY=CHANGE-THIS-TO-STRONG-JWT-SECRET-KEY-IN-PRODUCTION
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# ===========================================
# 数据库配置 - MySQL 8.0 (生产环境)
# ===========================================

# MySQL 连接信息 (生产环境 - 请修改为实际配置)
DATABASE_URL=mysql+pymysql://brzy_prod:CHANGE_PROD_PASSWORD@your-mysql-host:3306/brzy_medical_prod

# 数据库连接池配置
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=10

# ===========================================
# Redis 配置 - Redis 7 (生产环境)
# ===========================================

# Redis 连接信息 (生产环境 - 请修改为实际配置)
REDIS_URL=redis://:CHANGE_REDIS_PASSWORD@your-redis-host:6379/0
REDIS_PASSWORD=CHANGE_REDIS_PASSWORD

# ===========================================
# 必然中医原始 API 配置
# ===========================================

# 原始 API 地址
ORIGINAL_API_URL=https://api.haoniuzhongyi.top:9090

# API 超时设置
ORIGINAL_API_TIMEOUT=30

# ===========================================
# CORS 跨域配置 (生产环境)
# ===========================================

# 允许的域名 (请修改为实际前端域名)
CORS_ORIGINS=https://your-frontend-domain.com,https://www.your-frontend-domain.com

# ===========================================
# 文件存储配置
# ===========================================

# 上传目录
UPLOAD_DIR=./uploads
MAX_UPLOAD_SIZE=10485760

# PDF 生成配置
PDF_FONT_DIR=./fonts
PDF_TEMP_DIR=./temp

# ===========================================
# 日志配置
# ===========================================

# 日志文件路径
LOG_FILE=./logs/app.log

# ===========================================
# WebSocket 配置
# ===========================================

# WebSocket 心跳间隔 (秒)
WS_HEARTBEAT_INTERVAL=30

# ===========================================
# 服务器配置
# ===========================================

# 服务器端口
PORT=8000

# 允许的主机 (生产环境请修改为实际域名)
ALLOWED_HOSTS=your-backend-domain.com,www.your-backend-domain.com

# ===========================================
# 域名绑定配置 (生产环境)
# ===========================================

# 后端服务域名
BACKEND_DOMAIN=your-backend-domain.com

# SSL 配置 (如果使用 HTTPS)
SSL_ENABLED=true
SSL_CERT_PATH=/path/to/ssl/cert.pem
SSL_KEY_PATH=/path/to/ssl/key.pem
