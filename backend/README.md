# 必然中医医案导出系统 - 后端服务

## 📋 项目概述

这是必然中医医案导出系统的后端服务，基于 FastAPI 构建，提供医案数据处理、PDF 生成、用户认证等功能。

## 🚀 快速启动

### 环境要求

- **Python 3.8+**
- **MySQL 8.0+**
- **Redis 7.0+** (可选，用于缓存)

### 1. 安装依赖

```bash
# 创建虚拟环境
python3 -m venv venv

# 激活虚拟环境
# Linux/macOS:
source venv/bin/activate
# Windows:
# venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境

#### 开发环境启动

```bash
# 设置环境变量
export ENVIRONMENT=development

# 启动开发服务器
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

#### 生产环境启动

```bash
# 设置环境变量
export ENVIRONMENT=production

# 启动生产服务器
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
```

### 3. 环境配置文件

#### 开发环境配置 (.env.development)

开发环境使用 `.env.development` 文件，主要配置：

```env
# 数据库配置
DATABASE_URL=mysql+pymysql://brzy_dev:brzy_dev_pass@localhost:3306/brzy_medical_dev

# Redis配置
REDIS_URL=redis://:dev_redis_pass@localhost:6379/0

# CORS配置
CORS_ORIGINS=http://localhost:5173,http://127.0.0.1:5173,http://localhost:3000
```

#### 生产环境配置 (.env.production)

生产环境使用 `.env.production` 文件，需要修改的关键配置：

```env
# 安全密钥 (必须修改)
SECRET_KEY=CHANGE-THIS-TO-STRONG-SECRET-KEY-IN-PRODUCTION
JWT_SECRET_KEY=CHANGE-THIS-TO-STRONG-JWT-SECRET-KEY-IN-PRODUCTION

# 数据库配置 (必须修改)
DATABASE_URL=mysql+pymysql://brzy_prod:CHANGE_PROD_PASSWORD@your-mysql-host:3306/brzy_medical_prod

# Redis配置 (必须修改)
REDIS_URL=redis://:CHANGE_REDIS_PASSWORD@your-redis-host:6379/0

# CORS配置 (必须修改为实际前端域名)
CORS_ORIGINS=https://your-frontend-domain.com,https://www.your-frontend-domain.com

# 域名配置
BACKEND_DOMAIN=your-backend-domain.com
ALLOWED_HOSTS=your-backend-domain.com,www.your-backend-domain.com
```

## 🗄️ 数据库配置

### MySQL 数据库设置

1. **创建数据库用户和数据库**：

```sql
-- 开发环境
CREATE DATABASE brzy_medical_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'brzy_dev'@'%' IDENTIFIED BY 'brzy_dev_pass';
GRANT ALL PRIVILEGES ON brzy_medical_dev.* TO 'brzy_dev'@'%';

-- 生产环境
CREATE DATABASE brzy_medical_prod CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'brzy_prod'@'%' IDENTIFIED BY 'CHANGE_PROD_PASSWORD';
GRANT ALL PRIVILEGES ON brzy_medical_prod.* TO 'brzy_prod'@'%';

FLUSH PRIVILEGES;
```

2. **初始化数据库表**：

```bash
# 使用提供的SQL脚本
mysql -u brzy_dev -p brzy_medical_dev < init_database.sql
```

### Redis 配置

```bash
# 开发环境 Redis 配置
redis-cli
CONFIG SET requirepass "dev_redis_pass"

# 生产环境 Redis 配置
redis-cli
CONFIG SET requirepass "CHANGE_REDIS_PASSWORD"
```

## 🌐 服务器部署

### 使用 Gunicorn (推荐生产环境)

```bash
# 安装 Gunicorn
pip install gunicorn

# 启动服务
export ENVIRONMENT=production
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000
```

### 使用 Docker

```bash
# 构建镜像
docker build -t brzy-backend .

# 运行容器
docker run -d \
  --name brzy-backend \
  -p 8000:8000 \
  -e ENVIRONMENT=production \
  -v $(pwd)/.env.production:/app/.env.production \
  brzy-backend
```

## 📝 日志管理

日志文件位置：`./logs/app.log`

日志级别配置：
- 开发环境：`DEBUG`
- 生产环境：`INFO`

查看日志：
```bash
# 实时查看日志
tail -f logs/app.log

# 查看错误日志
grep "ERROR" logs/app.log
```

## 🔧 常用命令

```bash
# 检查服务状态
curl http://localhost:8000/health

# 查看API文档
# 浏览器访问: http://localhost:8000/docs

# 测试数据库连接
python -c "from app.database import check_database_connection; print(check_database_connection())"

# 清理临时文件
rm -rf temp/* uploads/* generated_pdfs/*
```

## 🚨 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否启动
   - 验证数据库连接字符串
   - 确认用户权限

2. **Redis 连接失败**
   - 检查 Redis 服务状态
   - 验证密码配置
   - Redis 是可选的，连接失败不会阻止服务启动

3. **端口占用**
   ```bash
   # 查看端口占用
   lsof -i :8000
   
   # 杀死占用进程
   kill -9 <PID>
   ```

4. **权限问题**
   ```bash
   # 确保目录权限
   chmod 755 logs uploads temp generated_pdfs fonts
   ```

## 📊 性能监控

- **健康检查端点**: `/health`
- **指标端点**: `/metrics` (如果启用)
- **日志监控**: 查看 `logs/app.log`

## 🔒 安全配置

生产环境必须修改的安全配置：

1. **更换密钥**: `SECRET_KEY` 和 `JWT_SECRET_KEY`
2. **配置 HTTPS**: 启用 SSL 证书
3. **限制 CORS**: 只允许信任的域名
4. **数据库安全**: 使用强密码，限制访问权限
5. **防火墙**: 只开放必要端口
