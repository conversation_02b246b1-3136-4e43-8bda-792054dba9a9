"""
Authentication utilities
"""
from fastapi import Depends, HTTPException, status, Header
from sqlalchemy.orm import Session
from typing import Optional
import logging

from app.database import get_db
from app.services.auth_service import AuthService
from app.models.auth import AuthSession

logger = logging.getLogger(__name__)


async def get_current_session(
    x_session_id: str = Header(..., description="Session ID"),
    db: Session = Depends(get_db)
) -> AuthSession:
    """
    Get current authenticated session from session ID header
    
    This dependency validates the session ID and returns the
    associated authentication session.
    """
    auth_service = AuthService(db)
    session = await auth_service.get_session_by_session_id(x_session_id)
    
    if not session or not session.is_valid():
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired session",
            headers={"WWW-Authenticate": "X-Session-Id"},
        )
    
    return session


async def get_optional_session(
    x_session_id: Optional[str] = Header(None, description="Session ID"),
    db: Session = Depends(get_db)
) -> Optional[AuthSession]:
    """
    Get optional authenticated session
    
    This dependency returns the session if a valid session ID is provided,
    otherwise returns None.
    """
    if not x_session_id:
        return None
    
    try:
        auth_service = AuthService(db)
        session = await auth_service.get_session_by_session_id(x_session_id)
        return session if session and session.is_valid() else None
    except Exception:
        return None