"""
Authentication related database models
"""
from sqlalchemy import Column, String, BigInteger, Enum, DateTime, Text, Index
from sqlalchemy.sql import func
from datetime import datetime
import enum

from app.database import Base


class AuthStatus(str, enum.Enum):
    """Authentication session status"""
    PENDING = "pending"
    AUTHORIZED = "authorized"
    EXPIRED = "expired"


class AuthSession(Base):
    """
    Authentication session model for storing iOS session tokens
    """
    __tablename__ = "auth_sessions"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    auth_code = Column(String(100), unique=True, nullable=False, comment="授权码")
    session_id = Column(String(100), unique=True, nullable=False, comment="Web会话ID")
    user_id = Column(String(50), nullable=False, comment="医生用户ID")
    session_token = Column(String(500), nullable=False, comment="iOS sessionToken(加密存储)")
    status = Column(
        Enum(AuthStatus),
        default=AuthStatus.PENDING,
        nullable=False,
        comment="状态"
    )
    created_at = Column(
        DateTime,
        default=func.now(),
        nullable=False,
        comment="创建时间"
    )
    expires_at = Column(DateTime, comment="过期时间")
    web_user_agent = Column(Text, comment="Web端设备信息")
    ip_address = Column(String(45), comment="IP地址")
    
    # Additional fields for tracking
    device_info = Column(Text, comment="iOS设备信息JSON")
    current_patient_id = Column(String(50), comment="当前患者ID")
    patient_name = Column(String(100), comment="当前患者姓名")
    authorized_at = Column(DateTime, comment="授权时间")
    last_accessed_at = Column(DateTime, comment="最后访问时间")
    
    # Indexes
    __table_args__ = (
        Index("idx_user_id", "user_id"),
        Index("idx_auth_code", "auth_code"),
        Index("idx_session_id", "session_id"),
        Index("idx_status_expires", "status", "expires_at"),
        Index("idx_created_at", "created_at"),
    )
    
    def __repr__(self):
        return f"<AuthSession(id={self.id}, user_id={self.user_id}, status={self.status})>"
    
    def is_expired(self) -> bool:
        """Check if the session is expired"""
        if self.expires_at is None:
            return False
        return datetime.utcnow() > self.expires_at
    
    def is_valid(self) -> bool:
        """Check if the session is valid for use"""
        return (
            self.status == AuthStatus.AUTHORIZED and
            not self.is_expired() and
            self.session_token is not None
        )