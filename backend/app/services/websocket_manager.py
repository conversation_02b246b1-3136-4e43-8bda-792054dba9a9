"""
WebSocket connection manager for real-time communication
"""
from typing import Dict, Set, Optional, Any
from fastapi import WebSocket, WebSocketDisconnect
import json
import logging
import asyncio
from datetime import datetime

logger = logging.getLogger(__name__)


class ConnectionManager:
    """Manages WebSocket connections"""
    
    def __init__(self):
        # Map of auth_code to WebSocket connections
        self.active_connections: Dict[str, WebSocket] = {}
        # Map of connection to auth_code for reverse lookup
        self.connection_map: Dict[WebSocket, str] = {}
        # Background tasks
        self.background_tasks: Set[asyncio.Task] = set()
    
    async def startup(self):
        """Initialize manager on startup"""
        logger.info("WebSocket manager started")
    
    async def shutdown(self):
        """Cleanup on shutdown"""
        # Cancel all background tasks
        for task in self.background_tasks:
            task.cancel()
        
        # Close all connections
        for websocket in list(self.active_connections.values()):
            await self.disconnect(websocket)
        
        logger.info("WebSocket manager shutdown")
    
    async def connect(self, websocket: WebSocket, auth_code: str):
        """Accept and register a new connection"""
        await websocket.accept()
        
        # Close existing connection for this auth_code if any
        if auth_code in self.active_connections:
            old_ws = self.active_connections[auth_code]
            await self.disconnect(old_ws)
        
        # Register new connection
        self.active_connections[auth_code] = websocket
        self.connection_map[websocket] = auth_code
        
        # Start heartbeat
        task = asyncio.create_task(self._heartbeat(websocket, auth_code))
        self.background_tasks.add(task)
        task.add_done_callback(self.background_tasks.discard)
        
        logger.info(f"WebSocket connected for auth_code: {auth_code}")
        
        # Send initial connection success message
        await self.send_json(websocket, {
            "type": "connection",
            "status": "connected",
            "auth_code": auth_code,
            "timestamp": datetime.utcnow().isoformat()
        })
    
    async def disconnect(self, websocket: WebSocket):
        """Disconnect and unregister a connection"""
        # Find and remove from maps
        auth_code = self.connection_map.get(websocket)
        if auth_code:
            del self.connection_map[websocket]
            if self.active_connections.get(auth_code) == websocket:
                del self.active_connections[auth_code]
            logger.info(f"WebSocket disconnected for auth_code: {auth_code}")
        
        try:
            await websocket.close()
        except Exception:
            pass
    
    async def send_json(self, websocket: WebSocket, data: dict):
        """Send JSON data to a specific connection"""
        try:
            await websocket.send_json(data)
        except Exception as e:
            logger.error(f"Error sending message: {e}")
            await self.disconnect(websocket)
    
    async def broadcast_to_auth_code(self, auth_code: str, data: dict):
        """Send message to a specific auth_code"""
        websocket = self.active_connections.get(auth_code)
        if websocket:
            await self.send_json(websocket, data)
    
    async def notify_auth_success(self, auth_code: str, auth_data: dict):
        """Notify web client of successful authentication"""
        await self.broadcast_to_auth_code(auth_code, {
            "type": "auth_success",
            "data": auth_data,
            "timestamp": datetime.utcnow().isoformat()
        })
    
    async def notify_auth_scanned(self, auth_code: str):
        """Notify web client that QR code was scanned"""
        await self.broadcast_to_auth_code(auth_code, {
            "type": "auth_scanned",
            "timestamp": datetime.utcnow().isoformat()
        })
    
    async def notify_auth_expired(self, auth_code: str):
        """Notify web client that auth session expired"""
        await self.broadcast_to_auth_code(auth_code, {
            "type": "auth_expired",
            "timestamp": datetime.utcnow().isoformat()
        })
    
    async def _heartbeat(self, websocket: WebSocket, auth_code: str):
        """Send periodic heartbeat to keep connection alive"""
        try:
            while True:
                await asyncio.sleep(30)  # Send heartbeat every 30 seconds
                
                # Check if still connected
                if self.active_connections.get(auth_code) != websocket:
                    break
                
                await self.send_json(websocket, {
                    "type": "heartbeat",
                    "timestamp": datetime.utcnow().isoformat()
                })
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.error(f"Heartbeat error for {auth_code}: {e}")
    
    async def websocket_endpoint(self, websocket: WebSocket, auth_code: str):
        """WebSocket endpoint handler"""
        await self.connect(websocket, auth_code)
        
        try:
            while True:
                # Wait for messages
                data = await websocket.receive_text()
                
                try:
                    message = json.loads(data)
                    
                    # Handle different message types
                    if message.get("type") == "ping":
                        await self.send_json(websocket, {
                            "type": "pong",
                            "timestamp": datetime.utcnow().isoformat()
                        })
                    elif message.get("type") == "auth_check":
                        # Client checking auth status
                        pass
                    else:
                        logger.warning(f"Unknown message type: {message.get('type')}")
                        
                except json.JSONDecodeError:
                    logger.error(f"Invalid JSON received: {data}")
                    
        except WebSocketDisconnect:
            await self.disconnect(websocket)
        except Exception as e:
            logger.error(f"WebSocket error: {e}")
            await self.disconnect(websocket)


# Global manager instance
manager = ConnectionManager()