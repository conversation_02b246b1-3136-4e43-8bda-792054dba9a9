"""
Common schemas used across the application
"""
from typing import Optional, Any, Dict, List
from pydantic import BaseModel, Field
from datetime import datetime


class ResponseBase(BaseModel):
    """Base response model"""
    code: int = Field(default=200, description="Response code")
    message: str = Field(default="Success", description="Response message")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")


class SuccessResponse(ResponseBase):
    """Success response model"""
    data: Optional[Any] = Field(default=None, description="Response data")


class ErrorResponse(ResponseBase):
    """Error response model"""
    code: int = Field(default=400, description="Error code")
    message: str = Field(default="Error", description="Error message")
    errors: Optional[Dict[str, Any]] = Field(default=None, description="Detailed errors")


class PaginationParams(BaseModel):
    """Pagination parameters"""
    page: int = Field(default=1, ge=1, description="Page number")
    size: int = Field(default=20, ge=1, le=100, description="Page size")
    
    @property
    def skip(self) -> int:
        """Calculate skip value for database query"""
        return (self.page - 1) * self.size


class PaginatedResponse(ResponseBase):
    """Paginated response model"""
    data: List[Any] = Field(default_factory=list, description="Data items")
    total: int = Field(default=0, description="Total count")
    page: int = Field(default=1, description="Current page")
    size: int = Field(default=20, description="Page size")
    pages: int = Field(default=1, description="Total pages")
    
    def calculate_pages(self):
        """Calculate total pages"""
        self.pages = (self.total + self.size - 1) // self.size if self.size > 0 else 0
        return self