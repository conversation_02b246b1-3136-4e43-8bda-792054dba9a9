"""
Patient management API endpoints
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Optional, List
import logging

from app.database import get_db
from app.schemas.common import PaginationParams
from app.schemas.patient import (
    PatientListResponse,
    PatientDetailResponse,
    PatientSearchParams,
    PatientListItem,
    PatientInfo
)
from app.utils.auth import get_current_session
from app.models.auth import AuthSession
from app.services.api_proxy import APIProxyService
from app.services.cache_service import CacheService
from app.services.auth_service import AuthService

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("", response_model=PatientListResponse)
async def get_patient_list(
    pagination: PaginationParams = Depends(),
    search: Optional[str] = Query(None, description="Search by name or mobile"),
    session: AuthSession = Depends(get_current_session),
    db: Session = Depends(get_db)
):
    """
    Get patient list for the authenticated doctor
    
    Since we focus only on medical record export functionality,
    this endpoint returns an empty patient list.
    """
    try:
        # Return empty patient list since we only focus on medical record export
        return PatientListResponse(
            code=200,
            message="Patient list functionality disabled - only medical record export is available",
            data=[],
            total=0,
            page=pagination.page,
            size=pagination.size
        )
        
    except Exception as e:
        logger.error(f"Error getting patient list: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{patient_id}", response_model=PatientDetailResponse)
async def get_patient_detail(
    patient_id: str,
    session: AuthSession = Depends(get_current_session),
    db: Session = Depends(get_db)
):
    """
    Get detailed information for a specific patient
    
    Since we only focus on medical record export functionality,
    this endpoint returns minimal patient information.
    """
    try:
        # Return basic patient info based on the patient_id
        # This is sufficient for medical record export functionality
        patient_info = PatientInfo(
            patient_id=patient_id,
            name="患者",  # Generic name since we don't have patient list
            age="",
            sex="",
            mobile="",
            tags=[],
            birthday=None,
            is_pregnant=False,
            remark="",
            avatar_url="",
            last_visit_date=None,
            visit_count=0,
            created_at=None
        )
        
        return PatientDetailResponse(
            code=200,
            message="Success",
            data=patient_info
        )
        
    except Exception as e:
        logger.error(f"Error getting patient detail: {e}")
        raise HTTPException(status_code=500, detail=str(e))