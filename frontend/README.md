# 必然中医医案导出系统 - 前端应用

## 📋 项目概述

这是必然中医医案导出系统的前端应用，基于 Vue 3 + Vite 构建，提供用户界面、扫码登录、医案管理等功能。

## 🚀 快速启动

### 环境要求

- **Node.js 16+**
- **npm 8+** 或 **yarn 1.22+**

### 1. 安装依赖

```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install
```

### 2. 启动应用

#### 开发环境启动

```bash
# 启动开发服务器 (使用 .env.development 配置)
npm run dev

# 或指定端口
npm run dev -- --port 5173
```

访问地址：http://localhost:5173

#### 生产环境预览

```bash
# 构建生产版本
npm run build

# 预览生产版本 (使用 .env.production 配置)
npm run preview
```

访问地址：http://localhost:3000

### 3. 环境配置文件

#### 开发环境配置 (.env.development)

开发环境使用 `.env.development` 文件，主要配置：

```env
# 后端API地址
VITE_API_URL=http://localhost:8000

# WebSocket地址
VITE_WS_URL=ws://localhost:8000

# 开发服务器配置
VITE_DEV_PORT=5173
VITE_DEV_HOST=localhost

# 调试配置
VITE_DEBUG=true
VITE_LOG_LEVEL=debug
```

#### 生产环境配置 (.env.production)

生产环境使用 `.env.production` 文件，需要修改的关键配置：

```env
# 后端API地址 (必须修改为实际后端地址)
VITE_API_URL=https://your-backend-domain.com

# WebSocket地址 (必须修改为实际后端地址)
VITE_WS_URL=wss://your-backend-domain.com

# 生产服务器配置
VITE_PROD_PORT=3000
VITE_PROD_HOST=0.0.0.0

# 性能优化配置
VITE_DEBUG=false
VITE_LOG_LEVEL=error
VITE_SOURCE_MAP=false

# 域名配置
FRONTEND_DOMAIN=your-frontend-domain.com
```

## 🛠️ 构建和部署

### 开发环境构建

```bash
# 构建开发版本 (包含调试信息)
npm run build:dev
```

### 生产环境构建

```bash
# 构建生产版本 (优化后的版本)
npm run build
```

构建产物位于 `dist/` 目录。

### 静态文件服务

#### 使用内置服务器

```bash
# 启动生产环境预览服务器
npm run start

# 启动开发环境预览服务器
npm run start:dev
```

#### 使用 Nginx

```nginx
server {
    listen 80;
    server_name your-frontend-domain.com;
    
    root /path/to/frontend/dist;
    index index.html;
    
    # 处理 Vue Router 的 history 模式
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 静态资源缓存
    location /assets/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # API 代理 (可选)
    location /api/ {
        proxy_pass https://your-backend-domain.com;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

#### 使用 Apache

```apache
<VirtualHost *:80>
    ServerName your-frontend-domain.com
    DocumentRoot /path/to/frontend/dist
    
    # 处理 Vue Router 的 history 模式
    <Directory "/path/to/frontend/dist">
        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.html$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.html [L]
    </Directory>
    
    # 静态资源缓存
    <LocationMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 year"
    </LocationMatch>
</VirtualHost>
```

### Docker 部署

```dockerfile
# Dockerfile
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

```bash
# 构建和运行
docker build -t brzy-frontend .
docker run -d -p 80:80 --name brzy-frontend brzy-frontend
```

## 📝 日志管理

### 浏览器日志

前端日志会自动保存到浏览器的 localStorage 中：

- **存储位置**: `localStorage`
- **保存周期**: 7天
- **日志级别**: 根据环境配置

### 查看和导出日志

```javascript
// 在浏览器控制台中执行

// 查看当前日志缓冲区
import { getLogBuffer } from '@/utils/logger'
console.log(getLogBuffer())

// 下载日志文件
import { downloadLogs } from '@/utils/logger'
downloadLogs()

// 清空日志缓冲区
import { clearLogBuffer } from '@/utils/logger'
clearLogBuffer()
```

## 🔧 开发工具

### 可用脚本

```bash
# 开发相关
npm run dev              # 启动开发服务器 (development 模式)
npm run dev:prod         # 启动开发服务器 (production 模式)

# 构建相关
npm run build            # 构建生产版本
npm run build:dev        # 构建开发版本
npm run preview          # 预览生产构建
npm run preview:dev      # 预览开发构建

# 服务相关
npm run start            # 启动生产服务器 (端口 3000)
npm run start:dev        # 启动开发服务器 (端口 5173)

# 代码质量
npm run lint             # 代码检查和修复
```

### 开发环境配置

```bash
# 设置开发环境变量
export NODE_ENV=development

# 启用详细日志
export VITE_DEBUG=true
export VITE_LOG_LEVEL=debug

# 自定义端口
export VITE_DEV_PORT=5174
```

### 生产环境配置

```bash
# 设置生产环境变量
export NODE_ENV=production

# 禁用调试
export VITE_DEBUG=false
export VITE_LOG_LEVEL=error

# 自定义端口
export VITE_PROD_PORT=3001
```

## 🚨 故障排除

### 常见问题

1. **API 连接失败**
   - 检查后端服务是否启动
   - 验证 `VITE_API_URL` 配置
   - 检查网络连接和防火墙

2. **WebSocket 连接失败**
   - 检查 `VITE_WS_URL` 配置
   - 确认后端 WebSocket 服务正常
   - 检查代理配置

3. **构建失败**
   ```bash
   # 清理缓存
   rm -rf node_modules package-lock.json
   npm install
   
   # 清理构建缓存
   rm -rf dist .vite
   ```

4. **端口占用**
   ```bash
   # 查看端口占用
   lsof -i :5173
   
   # 使用其他端口
   npm run dev -- --port 5174
   ```

5. **内存不足**
   ```bash
   # 增加 Node.js 内存限制
   export NODE_OPTIONS="--max-old-space-size=4096"
   npm run build
   ```

## 📊 性能优化

### 构建优化

- **代码分割**: 自动分割 vendor 和 element-plus
- **资源压缩**: 生产环境自动压缩
- **Tree Shaking**: 移除未使用的代码
- **缓存策略**: 静态资源长期缓存

### 运行时优化

- **懒加载**: 路由和组件按需加载
- **图片优化**: 支持 WebP 格式
- **缓存策略**: API 响应缓存
- **日志管理**: 生产环境减少日志输出

## 🔒 安全配置

生产环境安全检查清单：

1. **API 地址**: 确保使用 HTTPS
2. **WebSocket**: 确保使用 WSS
3. **CSP 策略**: 配置内容安全策略
4. **CORS 配置**: 后端正确配置跨域
5. **敏感信息**: 不在前端暴露敏感数据
6. **版本信息**: 生产环境隐藏调试信息
