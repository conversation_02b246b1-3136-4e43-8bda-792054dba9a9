import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // 根据环境加载对应的环境变量文件
  const env = loadEnv(mode, process.cwd(), '')

  console.log(`🚀 前端构建模式: ${mode}`)
  console.log(`📝 API地址: ${env.VITE_API_URL}`)
  console.log(`🔗 WebSocket地址: ${env.VITE_WS_URL}`)

  return {
  plugins: [
    vue(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
      imports: ['vue', 'vue-router', 'pinia'],
      dts: 'src/auto-imports.d.ts'
    }),
    Components({
      resolvers: [ElementPlusResolver()],
      dts: 'src/components.d.ts'
    })
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },
  server: {
    port: parseInt(env.VITE_DEV_PORT) || 5173,
    host: env.VITE_DEV_HOST || true,
    proxy: {
      '/api': {
        target: env.VITE_API_URL || 'http://127.0.0.1:8000',
        changeOrigin: true,
        secure: false
      },
      '/ws': {
        target: env.VITE_WS_URL || 'ws://127.0.0.1:8000',
        changeOrigin: true,
        ws: true
      }
    }
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: env.VITE_SOURCE_MAP === 'true',
    rollupOptions: {
      output: {
        manualChunks: {
          'element-plus': ['element-plus'],
          'vendor': ['vue', 'vue-router', 'pinia', 'axios']
        }
      }
    }
  },

  // 环境变量配置
  envDir: './',
  envPrefix: 'VITE_'
  }
})