{"name": "brzy-medical-web", "version": "1.0.0", "description": "BRZY Medical Export Web Interface", "private": true, "type": "module", "scripts": {"dev": "vite --mode development", "dev:prod": "vite --mode production", "build": "vite build --mode production", "build:dev": "vite build --mode development", "preview": "vite preview --mode production", "preview:dev": "vite preview --mode development", "start": "serve -s dist -l 3000", "start:dev": "serve -s dist -l 5173", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix"}, "dependencies": {"vue": "^3.3.11", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.6.2", "element-plus": "^2.4.3", "@element-plus/icons-vue": "^2.3.1", "dayjs": "^1.11.10", "qrcode.vue": "^3.4.1", "lodash-es": "^4.17.21", "js-cookie": "^3.0.5", "serve": "^14.2.1"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "vite": "^5.0.8", "sass": "^1.69.5", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.2", "@vue/eslint-config-prettier": "^8.0.0"}}