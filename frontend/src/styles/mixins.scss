// Mixins
@use './variables' as vars;

// Clearfix
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// Text ellipsis
@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// Multi-line ellipsis
@mixin multi-line-ellipsis($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

// Flex center
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// Absolute center
@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// Box shadow
@mixin box-shadow($shadow: vars.$box-shadow-base) {
  box-shadow: $shadow;
}

// Transition
@mixin transition($property: all, $duration: vars.$transition-duration, $timing: ease) {
  transition: $property $duration $timing;
}

// Scrollbar
@mixin scrollbar($width: 8px, $track-color: #f5f7fa, $thumb-color: #dcdfe6) {
  &::-webkit-scrollbar {
    width: $width;
    height: $width;
  }
  
  &::-webkit-scrollbar-track {
    background-color: $track-color;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: $thumb-color;
    border-radius: $width / 2;
    
    &:hover {
      background-color: darken($thumb-color, 10%);
    }
  }
}