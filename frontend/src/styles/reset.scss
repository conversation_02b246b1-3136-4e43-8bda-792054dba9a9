// Reset styles

// Remove default margins and paddings
html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed,
figure, figcaption, footer, header, hgroup,
menu, nav, output, ruby, section, summary,
time, mark, audio, video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
}

// HTML5 display-role reset
article, aside, details, figcaption, figure,
footer, header, hgroup, menu, nav, section {
  display: block;
}

// List styles
ol, ul {
  list-style: none;
}

// Quotes
blockquote, q {
  quotes: none;
  
  &:before, &:after {
    content: '';
    content: none;
  }
}

// Tables
table {
  border-collapse: collapse;
  border-spacing: 0;
}

// Links
a {
  color: inherit;
  text-decoration: none;
  
  &:hover, &:focus {
    text-decoration: none;
  }
}

// Forms
button, input, optgroup, select, textarea {
  margin: 0;
  font: inherit;
  color: inherit;
}

button {
  overflow: visible;
  text-transform: none;
  cursor: pointer;
  
  &::-moz-focus-inner {
    border: 0;
    padding: 0;
  }
}

// Images
img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
  border: 0;
}