/**
 * Frontend logging utility
 * Provides unified logging for the frontend application
 */

// Log levels
const LOG_LEVELS = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
  OFF: 4
}

// Get log level from environment
const getLogLevel = () => {
  const envLevel = import.meta.env.VITE_LOG_LEVEL?.toUpperCase() || 'INFO'
  return LOG_LEVELS[envLevel] !== undefined ? LOG_LEVELS[envLevel] : LOG_LEVELS.INFO
}

// Current log level
const currentLogLevel = getLogLevel()

// Development mode check
const isDevelopment = import.meta.env.MODE === 'development'

// Log file buffer for potential server sending
const logBuffer = []
const MAX_BUFFER_SIZE = 100

/**
 * Format log message with timestamp and context
 */
const formatMessage = (level, component, message, data = null) => {
  const timestamp = new Date().toISOString()
  const formattedMessage = `[${timestamp}] [${level}] [${component}] ${message}`
  
  if (data) {
    return { message: formattedMessage, data }
  }
  return formattedMessage
}

/**
 * Add log to buffer (for potential server sending)
 */
const addToBuffer = (level, component, message, data = null) => {
  const logEntry = {
    timestamp: new Date().toISOString(),
    level,
    component,
    message,
    data,
    url: window.location.href,
    userAgent: navigator.userAgent
  }
  
  logBuffer.push(logEntry)
  
  // Keep buffer size manageable
  if (logBuffer.length > MAX_BUFFER_SIZE) {
    logBuffer.shift()
  }
}

/**
 * Logger class
 */
class Logger {
  constructor(component = 'App') {
    this.component = component
  }

  /**
   * Debug level logging
   */
  debug(message, data = null) {
    if (currentLogLevel <= LOG_LEVELS.DEBUG) {
      const formatted = formatMessage('DEBUG', this.component, message, data)
      if (isDevelopment) {
        if (data) {
          console.debug(formatted.message, formatted.data)
        } else {
          console.debug(formatted)
        }
      }
      addToBuffer('DEBUG', this.component, message, data)
    }
  }

  /**
   * Info level logging
   */
  info(message, data = null) {
    if (currentLogLevel <= LOG_LEVELS.INFO) {
      const formatted = formatMessage('INFO', this.component, message, data)
      if (isDevelopment) {
        if (data) {
          console.info(formatted.message, formatted.data)
        } else {
          console.info(formatted)
        }
      }
      addToBuffer('INFO', this.component, message, data)
    }
  }

  /**
   * Warning level logging
   */
  warn(message, data = null) {
    if (currentLogLevel <= LOG_LEVELS.WARN) {
      const formatted = formatMessage('WARN', this.component, message, data)
      if (data) {
        console.warn(formatted.message, formatted.data)
      } else {
        console.warn(formatted)
      }
      addToBuffer('WARN', this.component, message, data)
    }
  }

  /**
   * Error level logging
   */
  error(message, error = null) {
    if (currentLogLevel <= LOG_LEVELS.ERROR) {
      const errorData = error ? {
        message: error.message,
        stack: error.stack,
        name: error.name
      } : null
      
      const formatted = formatMessage('ERROR', this.component, message, errorData)
      if (errorData) {
        console.error(formatted.message, formatted.data)
      } else {
        console.error(formatted)
      }
      addToBuffer('ERROR', this.component, message, errorData)
    }
  }

  /**
   * Group logging for related operations
   */
  group(label, collapsed = false) {
    if (isDevelopment) {
      if (collapsed) {
        console.groupCollapsed(`[${this.component}] ${label}`)
      } else {
        console.group(`[${this.component}] ${label}`)
      }
    }
  }

  groupEnd() {
    if (isDevelopment) {
      console.groupEnd()
    }
  }

  /**
   * Time tracking
   */
  time(label) {
    if (isDevelopment) {
      console.time(`[${this.component}] ${label}`)
    }
  }

  timeEnd(label) {
    if (isDevelopment) {
      console.timeEnd(`[${this.component}] ${label}`)
    }
  }
}

/**
 * Get logger instance for a component
 */
export const createLogger = (component) => {
  return new Logger(component)
}

/**
 * Default logger instance
 */
export const logger = new Logger('App')

/**
 * Get current log buffer (for sending to server if needed)
 */
export const getLogBuffer = () => {
  return [...logBuffer]
}

/**
 * Clear log buffer
 */
export const clearLogBuffer = () => {
  logBuffer.length = 0
}

/**
 * Save logs to localStorage for persistence
 */
export const saveLogsToStorage = () => {
  try {
    const logs = getLogBuffer()
    const timestamp = new Date().toISOString().split('T')[0] // YYYY-MM-DD
    const storageKey = `brzy_frontend_logs_${timestamp}`

    // Keep only last 7 days of logs
    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)

    // Clean old logs
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith('brzy_frontend_logs_')) {
        const logDate = key.replace('brzy_frontend_logs_', '')
        if (new Date(logDate) < sevenDaysAgo) {
          localStorage.removeItem(key)
        }
      }
    }

    // Save current logs
    localStorage.setItem(storageKey, JSON.stringify(logs))
  } catch (error) {
    console.error('Failed to save logs to storage:', error)
  }
}

/**
 * Download logs as file
 */
export const downloadLogs = () => {
  try {
    const logs = getLogBuffer()
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const filename = `brzy_frontend_logs_${timestamp}.json`

    const dataStr = JSON.stringify(logs, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })

    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    link.click()

    URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Failed to download logs:', error)
  }
}

/**
 * Send logs to server (optional feature)
 */
export const sendLogsToServer = async (logs = null) => {
  if (!logs) {
    logs = getLogBuffer()
  }

  if (logs.length === 0) {
    return
  }

  try {
    // This would require a backend endpoint to receive logs
    // await fetch('/api/logs', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify({ logs })
    // })

    // For now, save to localStorage and provide download option
    saveLogsToStorage()

    if (isDevelopment) {
      console.log('Logs saved to localStorage. Use downloadLogs() to export.')
    }
  } catch (error) {
    console.error('Failed to send logs to server:', error)
  }
}

// Global error handler
window.addEventListener('error', (event) => {
  const errorLogger = new Logger('GlobalError')
  errorLogger.error('Unhandled JavaScript error', {
    message: event.message,
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno,
    stack: event.error?.stack
  })
})

// Global unhandled promise rejection handler
window.addEventListener('unhandledrejection', (event) => {
  const errorLogger = new Logger('GlobalError')
  errorLogger.error('Unhandled promise rejection', {
    reason: event.reason,
    stack: event.reason?.stack
  })
})

// Auto-save logs periodically in development
if (isDevelopment) {
  setInterval(() => {
    saveLogsToStorage()
  }, 60000) // Save every minute
}

// Save logs before page unload
window.addEventListener('beforeunload', () => {
  saveLogsToStorage()
})

export default logger