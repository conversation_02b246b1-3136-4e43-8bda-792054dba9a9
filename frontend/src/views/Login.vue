<template>
  <div class="login-page">
    <div class="login-container">
      <div class="login-header">
        <h1>必然中医医案导出系统</h1>
      </div>
      
      <div class="login-content">
        <el-tabs v-model="activeTab" class="login-tabs">
          <el-tab-pane label="扫码登录" name="qrcode">
            <QRCodeLogin />
          </el-tab-pane>
          
          <el-tab-pane label="调试登录" name="debug" v-if="enableDebug">
            <div class="debug-login">
              <el-form @submit.prevent="debugLogin">
                <el-form-item label="用户ID">
                  <el-input v-model="debugUserId" placeholder="请输入用户ID" />
                </el-form-item>
                <el-form-item label="Session Token">
                  <el-input
                    v-model="debugToken"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入Session Token"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="debugLogin" :loading="debugLoading">
                    登录
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      
      <div class="login-footer">
        <p>&copy; 2024 必然中医. All rights reserved.</p>
      </div>
    </div>
    
    <div class="login-bg"></div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import QRCodeLogin from '@/components/QRCodeLogin.vue'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// State
const activeTab = ref('qrcode')
const debugUserId = ref('')
const debugToken = ref('')
const debugLoading = ref(false)

// Reset auth status when login page is mounted
onMounted(() => {
  // Reset auth status to ensure QR code is regenerated
  authStore.setAuthStatus('pending')
})

// Enable debug mode in development
const enableDebug = computed(() => {
  return import.meta.env.VITE_ENABLE_DEBUG === 'true' || import.meta.env.DEV
})

// Debug login
const debugLogin = async () => {
  if (!debugUserId.value || !debugToken.value) {
    ElMessage.warning('请填写完整信息')
    return
  }
  
  debugLoading.value = true
  
  try {
    // In debug mode, directly set auth data
    await authStore.login(debugToken.value, {
      user_id: debugUserId.value,
      debug_mode: true
    })
    
    ElMessage.success('调试登录成功')
    router.push('/dashboard')
  } catch (error) {
    console.error('Debug login error:', error)
    ElMessage.error('登录失败')
  } finally {
    debugLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
.login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  
  .login-container {
    width: 480px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    position: relative;
    z-index: 10;
    
    .login-header {
      text-align: center;
      padding: 40px 20px 30px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      
      h1 {
        font-size: 24px;
        margin: 0;
        font-weight: 500;
      }
    }
    
    .login-content {
      padding: 30px 40px 40px;
      
      .login-tabs {
        :deep(.el-tabs__header) {
          margin-bottom: 30px;
        }
        
        :deep(.el-tabs__nav-wrap::after) {
          display: none;
        }
        
        :deep(.el-tabs__active-bar) {
          background-color: #667eea;
        }
        
        :deep(.el-tabs__item) {
          font-size: 16px;
          
          &.is-active {
            color: #667eea;
          }
        }
      }
      
      .debug-login {
        .el-form-item {
          margin-bottom: 20px;
        }
        
        .el-button {
          width: 100%;
        }
      }
    }
    
    .login-footer {
      text-align: center;
      padding: 20px;
      border-top: 1px solid #f0f0f0;
      
      p {
        margin: 0;
        color: #909399;
        font-size: 12px;
      }
    }
  }
  
  .login-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    
    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
      animation: rotate 30s linear infinite;
    }
  }
}

@keyframes rotate {
  to {
    transform: rotate(360deg);
  }
}

// Responsive
@media (max-width: 640px) {
  .login-page {
    .login-container {
      width: 100%;
      max-width: 400px;
      margin: 20px;
      
      .login-content {
        padding: 20px;
      }
    }
  }
}
</style>