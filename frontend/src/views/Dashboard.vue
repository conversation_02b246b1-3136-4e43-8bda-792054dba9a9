<template>
  <div class="dashboard-container">
    <el-container>
      <!-- Header -->
      <el-header class="dashboard-header">
        <div class="header-left">
          <h2>必然中医医案导出系统</h2>
        </div>
        <div class="header-right">
          <span class="user-info">
            <el-icon><User /></el-icon>
            医生ID: {{ userId }}
          </span>
          <el-button type="danger" size="small" @click="handleLogout">
            退出登录
          </el-button>
        </div>
      </el-header>
      
      <!-- Main Content -->
      <el-container>
        <!-- Sidebar -->
        <el-aside width="200px" class="dashboard-sidebar">
          <!-- Current Patient Info -->
          <div v-if="currentPatientName" class="current-patient">
            <div class="patient-label">当前患者</div>
            <div class="patient-name">{{ currentPatientName }}</div>
            <el-button
              type="primary"
              size="small"
              @click="viewPatientRecords"
              style="width: 100%; margin-bottom: 10px;"
            >
              查看医案
            </el-button>
          </div>
          
          <el-menu
            :default-active="activeMenu"
            router
            :collapse="false"
          >
            <el-menu-item index="/dashboard/export">
              <el-icon><Download /></el-icon>
              <span>导出医案</span>
            </el-menu-item>
            
            <el-menu-item v-if="currentPatientId" :index="`/dashboard/medical-records/${currentPatientId}`">
              <el-icon><Document /></el-icon>
              <span>医案记录</span>
            </el-menu-item>
          </el-menu>
        </el-aside>
        
        <!-- Content Area -->
        <el-main class="dashboard-main">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { useAuthStore } from '@/stores/auth'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// Computed
const activeMenu = computed(() => route.path)
const userId = computed(() => authStore.userId)
const currentPatientId = computed(() => authStore.currentPatientId)
const currentPatientName = computed(() => authStore.currentPatientName)

// Methods
const handleLogout = () => {
  ElMessageBox.confirm(
    '确定要退出登录吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    authStore.logout()
    router.push('/login')
  })
}

const viewPatientRecords = () => {
  if (currentPatientId.value) {
    router.push(`/dashboard/medical-records/${currentPatientId.value}`)
  }
}
</script>

<style lang="scss" scoped>
.dashboard-container {
  height: 100vh;
  
  .el-container {
    height: 100%;
  }
}

.dashboard-header {
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  
  .header-left {
    h2 {
      margin: 0;
      font-size: 20px;
      color: #303133;
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 20px;
    
    .user-info {
      color: #606266;
      font-size: 14px;
      display: flex;
      align-items: center;
      gap: 5px;
    }
  }
}

.dashboard-sidebar {
  background-color: #fff;
  box-shadow: 2px 0 8px rgba(0, 21, 41, 0.08);
  display: flex;
  flex-direction: column;
  
  .current-patient {
    padding: 20px;
    border-bottom: 1px solid #e4e7ed;
    background: #f5f7fa;
    flex-shrink: 0;
    
    .patient-label {
      font-size: 12px;
      color: #909399;
      margin-bottom: 5px;
    }
    
    .patient-name {
      font-size: 16px;
      color: #303133;
      font-weight: 500;
      margin-bottom: 10px;
    }
  }
  
  .el-menu {
    border: none;
    flex: 1;
  }
}

.dashboard-main {
  background-color: #f5f7fa;
  padding: 20px;
}
</style>