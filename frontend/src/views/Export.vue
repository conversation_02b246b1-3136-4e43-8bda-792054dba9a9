<template>
  <div class="export-page">
    <div class="page-header">
      <div class="header-left">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>医案系统</el-breadcrumb-item>
          <el-breadcrumb-item>导出医案</el-breadcrumb-item>
        </el-breadcrumb>
        <h3>导出医案</h3>
      </div>
      <div class="header-right" v-if="currentPatientId">
        <el-button type="primary" @click="goToMedicalRecords">
          <el-icon><View /></el-icon>
          查看医案记录
        </el-button>
      </div>
    </div>
    
    <!-- Quick Export -->
    <el-card class="quick-export" v-if="currentPatientId">
      <template #header>
        <div class="card-header">
          <span>快速导出 - {{ currentPatientName }}</span>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <el-button type="primary" size="large" @click="quickExport('all')" style="width: 100%">
            <el-icon><Document /></el-icon>
            导出全部医案
          </el-button>
        </el-col>
        <el-col :span="8">
          <el-button type="success" size="large" @click="quickExport('recent')" style="width: 100%">
            <el-icon><Clock /></el-icon>
            导出最近3个月
          </el-button>
        </el-col>
        <el-col :span="8">
          <el-button type="warning" size="large" @click="quickExport('prescription')" style="width: 100%">
            <el-icon><Tickets /></el-icon>
            仅导出处方
          </el-button>
        </el-col>
      </el-row>
    </el-card>
    
    <!-- Custom Export -->
    <el-card class="custom-export">
      <template #header>
        <div class="card-header">
          <span>自定义导出</span>
        </div>
      </template>
      
      <el-form :model="exportForm" label-width="120px">
        <el-form-item label="选择患者" v-if="!currentPatientId">
          <el-select
            v-model="exportForm.patientId"
            placeholder="请选择患者"
            filterable
            remote
            :remote-method="searchPatients"
            :loading="searchingPatients"
            style="width: 100%"
          >
            <el-option
              v-for="patient in patientOptions"
              :key="patient.patient_id"
              :label="`${patient.name} - ${patient.mobile || '无手机号'}`"
              :value="patient.patient_id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="导出内容">
          <el-checkbox-group v-model="exportForm.recordTypes">
            <el-checkbox label="prescription">处方</el-checkbox>
            <el-checkbox label="wzd">问诊单</el-checkbox>
            <el-checkbox label="fzd">复诊单</el-checkbox>
            <el-checkbox label="message">聊天记录</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="exportForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            :shortcuts="dateShortcuts"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="导出选项">
          <el-checkbox v-model="exportForm.includeImages">包含图片</el-checkbox>
          <el-checkbox v-model="exportForm.includePrescriptions">包含处方详情</el-checkbox>
          <el-checkbox v-model="exportForm.includeMessages">包含聊天消息</el-checkbox>
        </el-form-item>
        
        <el-form-item label="导出格式">
          <el-radio-group v-model="exportForm.format">
            <el-radio label="pdf">PDF文档</el-radio>
            <!-- <el-radio label="word">Word文档</el-radio> -->
          </el-radio-group>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="startExport" :loading="exporting">
            开始导出
          </el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- Export History -->
    <el-card class="export-history">
      <template #header>
        <div class="card-header">
          <span>导出历史</span>
          <el-button type="text" @click="refreshHistory">刷新</el-button>
        </div>
      </template>
      
      <el-table :data="exportHistory" v-loading="loadingHistory">
        <el-table-column prop="created_at" label="导出时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="patient_name" label="患者" />
        <el-table-column prop="record_count" label="记录数" />
        <el-table-column prop="status" label="状态">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button
              v-if="scope.row.status === 'completed'"
              type="primary"
              link
              @click="downloadFile(scope.row)"
            >
              下载
            </el-button>
            <el-button
              v-else-if="scope.row.status === 'processing'"
              type="text"
              :loading="true"
            >
              处理中
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import { medicalApi } from '@/api/medical'
import { patientApi } from '@/api/patients'
import { useAuthStore } from '@/stores/auth'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// Computed
const currentPatientId = computed(() => authStore.currentPatientId || route.query.patient_id)
const currentPatientName = computed(() => authStore.currentPatientName || route.query.patient_name || '')

// Data
const exporting = ref(false)
const searchingPatients = ref(false)
const loadingHistory = ref(false)
const patientOptions = ref([])
const exportHistory = ref([])

// Export form
const exportForm = reactive({
  patientId: currentPatientId.value || '',
  recordTypes: ['prescription', 'wzd'],
  dateRange: null,
  includeImages: true,
  includePrescriptions: true,
  includeMessages: false,
  format: 'pdf'
})

// Date shortcuts
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    }
  },
  {
    text: '最近一年',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 365)
      return [start, end]
    }
  },
  {
    text: '全部',
    value: () => null
  }
]

// Methods
const quickExport = async (type) => {
  const patientId = currentPatientId.value
  if (!patientId) {
    ElMessage.warning('请先选择患者')
    return
  }
  
  let params = {
    patient_id: patientId,
    include_images: true,
    include_prescriptions: true,
    format: 'pdf'
  }
  
  switch (type) {
    case 'all':
      params.record_types = ['prescription', 'wzd', 'fzd']
      break
    case 'recent':
      params.record_types = ['prescription', 'wzd', 'fzd']
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      params.date_range = {
        start: dayjs(start).format('YYYY-MM-DD'),
        end: dayjs(end).format('YYYY-MM-DD')
      }
      break
    case 'prescription':
      params.record_types = ['prescription']
      break
  }
  
  await doExport(params)
}

const startExport = async () => {
  const patientId = exportForm.patientId || currentPatientId.value
  if (!patientId) {
    ElMessage.warning('请选择患者')
    return
  }
  
  if (exportForm.recordTypes.length === 0) {
    ElMessage.warning('请选择导出内容')
    return
  }
  
  const params = {
    patient_id: patientId,
    record_types: exportForm.recordTypes,
    include_images: exportForm.includeImages,
    include_prescriptions: exportForm.includePrescriptions,
    format: exportForm.format
  }
  
  if (exportForm.dateRange) {
    params.date_range = {
      start: exportForm.dateRange[0],
      end: exportForm.dateRange[1]
    }
  }
  
  if (exportForm.includeMessages && exportForm.recordTypes.includes('message')) {
    params.include_messages = true
  }
  
  await doExport(params)
}

const doExport = async (params) => {
  exporting.value = true
  try {
    const response = await medicalApi.exportMedicalRecords(params)
    
    if (response.task_id) {
      ElMessage.success('导出任务已创建，请稍后查看')
      
      // Add to history
      exportHistory.value.unshift({
        task_id: response.task_id,
        created_at: new Date(),
        patient_name: currentPatientName.value || '未知患者',
        record_count: '-',
        status: 'processing'
      })
      
      // Check status
      checkExportStatus(response.task_id)
    }
  } catch (error) {
    ElMessage.error('导出失败，请重试')
    console.error('Export failed:', error)
  } finally {
    exporting.value = false
  }
}

const checkExportStatus = async (taskId) => {
  const checkInterval = setInterval(async () => {
    try {
      const response = await medicalApi.checkExportStatus(taskId)
      
      // Update history
      const item = exportHistory.value.find(h => h.task_id === taskId)
      if (item) {
        item.status = response.status
        
        if (response.status === 'completed') {
          clearInterval(checkInterval)
          item.download_url = response.download_url
          ElMessage.success('导出完成，可以下载了')
        } else if (response.status === 'failed') {
          clearInterval(checkInterval)
          ElMessage.error('导出失败')
        }
      }
    } catch (error) {
      clearInterval(checkInterval)
      console.error('Check status failed:', error)
    }
  }, 3000)
  
  // Stop checking after 5 minutes
  setTimeout(() => {
    clearInterval(checkInterval)
  }, 300000)
}

const searchPatients = async (query) => {
  // Patient list functionality is disabled - only current patient is available
  patientOptions.value = []
}

const goToMedicalRecords = () => {
  if (currentPatientId.value) {
    router.push(`/dashboard/medical-records/${currentPatientId.value}`)
  }
}

const resetForm = () => {
  exportForm.patientId = currentPatientId.value || ''
  exportForm.recordTypes = ['prescription', 'wzd']
  exportForm.dateRange = null
  exportForm.includeImages = true
  exportForm.includePrescriptions = true
  exportForm.includeMessages = false
  exportForm.format = 'pdf'
}

const refreshHistory = () => {
  // TODO: Load export history from backend
  ElMessage.info('历史记录功能开发中')
}

const downloadFile = (item) => {
  if (item.download_url || item.task_id) {
    const url = item.download_url || medicalApi.downloadExport(item.task_id)
    window.open(url, '_blank')
  }
}

const formatDate = (date) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

const getStatusType = (status) => {
  const typeMap = {
    processing: 'warning',
    completed: 'success',
    failed: 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    processing: '处理中',
    completed: '已完成',
    failed: '失败'
  }
  return textMap[status] || status
}

// Lifecycle
onMounted(() => {
  if (currentPatientId.value) {
    exportForm.patientId = currentPatientId.value
  }
})
</script>

<style lang="scss" scoped>
.export-page {
  .page-header {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    
    .header-left {
      .el-breadcrumb {
        margin-bottom: 8px;
      }
      
      h3 {
        margin: 0;
        font-size: 20px;
        color: #303133;
      }
    }
    
    .header-right {
      flex-shrink: 0;
    }
  }
  
  .el-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .quick-export {
    .el-button {
      height: 80px;
      font-size: 16px;
      
      .el-icon {
        font-size: 24px;
        margin-right: 10px;
      }
    }
  }
  
  .custom-export {
    .el-form {
      max-width: 600px;
    }
  }
}
</style>