<template>
  <div class="patients-page">
    <div class="page-header">
      <h3>患者列表</h3>
      <el-button type="primary" @click="refreshList">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
    </div>
    
    <!-- Search Bar -->
    <div class="search-bar">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索患者姓名或手机号"
        clearable
        @keyup.enter="handleSearch"
        style="width: 300px"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
      <el-button type="primary" @click="handleSearch">搜索</el-button>
    </div>
    
    <!-- Patient List -->
    <el-table
      :data="patientList"
      v-loading="loading"
      style="width: 100%"
      @row-click="handleRowClick"
    >
      <el-table-column prop="name" label="姓名" width="120" />
      
      <el-table-column prop="sex" label="性别" width="80">
        <template #default="scope">
          {{ scope.row.sex === '1' || scope.row.sex === '男' ? '男' : '女' }}
        </template>
      </el-table-column>
      
      <el-table-column prop="age" label="年龄" width="80" />
      
      <el-table-column prop="mobile" label="手机号" width="150" />
      
      <el-table-column prop="tags" label="标签" min-width="200">
        <template #default="scope">
          <el-tag
            v-for="tag in scope.row.tags"
            :key="tag"
            size="small"
            style="margin-right: 5px"
          >
            {{ tag }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="last_visit_date" label="最后就诊" width="180">
        <template #default="scope">
          {{ formatDate(scope.row.last_visit_date) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="visit_count" label="就诊次数" width="100" />
      
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="scope">
          <el-button
            type="primary"
            link
            @click.stop="viewMedicalRecords(scope.row)"
          >
            查看医案
          </el-button>
          <el-button
            type="primary"
            link
            @click.stop="exportRecords(scope.row)"
          >
            导出
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- Pagination -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import { patientApi } from '@/api/patients'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// Data
const patientList = ref([])
const loading = ref(false)
const searchKeyword = ref('')

// Pagination
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// Methods
const fetchPatientList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      search: searchKeyword.value || undefined
    }
    
    const response = await patientApi.getPatientList(params)
    patientList.value = response.data || []
    pagination.total = response.total || 0
  } catch (error) {
    ElMessage.error('获取患者列表失败')
    console.error('Failed to fetch patient list:', error)
  } finally {
    loading.value = false
  }
}

const refreshList = () => {
  fetchPatientList()
}

const handleSearch = () => {
  pagination.page = 1
  fetchPatientList()
}

const handlePageChange = () => {
  fetchPatientList()
}

const handleSizeChange = () => {
  pagination.page = 1
  fetchPatientList()
}

const formatDate = (date) => {
  return date ? dayjs(date).format('YYYY-MM-DD') : '-'
}

const handleRowClick = (row) => {
  // Update current patient in store
  authStore.setPatientInfo({
    patient_id: row.patient_id,
    patient_name: row.name
  })
  
  // Navigate to medical records
  viewMedicalRecords(row)
}

const viewMedicalRecords = (patient) => {
  router.push(`/dashboard/medical-records/${patient.patient_id}`)
}

const exportRecords = (patient) => {
  // Update current patient in store
  authStore.setPatientInfo({
    patient_id: patient.patient_id,
    patient_name: patient.name
  })
  
  // Navigate to export page
  router.push({
    path: '/dashboard/export',
    query: {
      patient_id: patient.patient_id,
      patient_name: patient.name
    }
  })
}

// Lifecycle
onMounted(() => {
  fetchPatientList()
})
</script>

<style lang="scss" scoped>
.patients-page {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h3 {
      margin: 0;
      font-size: 20px;
      color: #303133;
    }
  }
  
  .search-bar {
    background: white;
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
  }
  
  .el-table {
    background: white;
    border-radius: 4px;
    cursor: pointer;
    
    :deep(.el-table__row) {
      &:hover {
        background-color: #f5f7fa;
      }
    }
  }
  
  .pagination-wrapper {
    background: white;
    padding: 20px;
    border-radius: 4px;
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>