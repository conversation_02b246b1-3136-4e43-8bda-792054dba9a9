<template>
  <div class="qr-login">
    <div class="qr-login__header">
      <h2>扫码登录</h2>
      <p class="subtitle">请使用必然中医客户端扫描二维码</p>
    </div>
    
    <div class="qr-login__container">
      <!-- QR Code -->
      <div v-if="!isExpired" class="qr-code-wrapper">
        <div class="qr-code" :class="{ 'scanned': isScanned }">
          <qrcode-vue
            v-if="qrCodeUrl"
            :value="qrCodeUrl"
            :size="200"
            level="H"
          />
          <div v-else class="qr-loading">
            <el-icon class="is-loading" :size="40">
              <Loading />
            </el-icon>
          </div>
        </div>
        
        <!-- Scan Success Overlay -->
        <transition name="fade">
          <div v-if="isScanned" class="scan-overlay">
            <el-icon class="success-icon" :size="60">
              <SuccessFilled />
            </el-icon>
            <p>扫码成功</p>
            <p class="tips">请在手机上确认登录</p>
          </div>
        </transition>
      </div>
      
      <!-- Expired State -->
      <div v-else class="qr-expired">
        <el-icon :size="60" color="#909399">
          <WarningFilled />
        </el-icon>
        <p>二维码已过期</p>
        <el-button type="primary" @click="refreshQRCode">刷新二维码</el-button>
      </div>
    </div>
    
    <!-- Instructions -->
    <div class="qr-login__instructions">
      <h4>操作步骤：</h4>
      <ol>
        <li>打开必然中医 App</li>
        <li>进入患者聊天界面</li>
        <li>点击右上角扫码按钮</li>
        <li>扫描此二维码并确认登录</li>
      </ol>
    </div>
    
    <!-- Timer -->
    <div v-if="!isExpired && countdown > 0" class="qr-login__timer">
      <el-progress
        :percentage="(countdown / 300) * 100"
        :show-text="false"
        :stroke-width="2"
      />
      <span class="timer-text">{{ Math.floor(countdown / 60) }}:{{ String(countdown % 60).padStart(2, '0') }} 后过期</span>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import QrcodeVue from 'qrcode.vue'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { connectWebSocket } from '@/utils/websocket'

const router = useRouter()
const authStore = useAuthStore()

// Reactive state
const qrCodeUrl = ref('')
const isExpired = ref(false)
const isScanned = ref(false)
const countdown = ref(300) // 5 minutes in seconds
const authCode = ref('')

let ws = null
let countdownTimer = null
let statusCheckTimer = null

// Generate auth code
const generateAuthCode = () => {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substr(2, 9)
  return `${timestamp}_${random}_web`
}

// Generate QR code
const generateQRCode = async () => {
  try {
    isExpired.value = false
    isScanned.value = false
    countdown.value = 300
    
    authCode.value = generateAuthCode()
    authStore.setAuthCode(authCode.value)
    
    // Generate QR code URL
    const baseUrl = import.meta.env.VITE_API_URL || window.location.origin
    qrCodeUrl.value = `brzy-web://login?session=${authCode.value}&server=${baseUrl}`
    
    // Start countdown
    startCountdown()
    
    // Establish WebSocket connection
    await establishWebSocket()
    
  } catch (error) {
    console.error('Failed to generate QR code:', error)
    ElMessage.error('生成二维码失败，请刷新重试')
  }
}

// Start countdown timer
const startCountdown = () => {
  stopCountdown()
  countdownTimer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      handleExpired()
    }
  }, 1000)
}

// Stop countdown timer
const stopCountdown = () => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
}

// Handle QR code expiration
const handleExpired = () => {
  isExpired.value = true
  stopCountdown()
  closeWebSocket()
  authStore.setAuthStatus('expired')
}

// Establish WebSocket connection
const establishWebSocket = async () => {
  closeWebSocket()
  
  ws = connectWebSocket(authCode.value, {
    onOpen: () => {
      console.log('WebSocket connected')
    },
    onMessage: (event) => {
      handleWebSocketMessage(event.data)
    },
    onError: (error) => {
      console.error('WebSocket error:', error)
      ElMessage.error('连接服务器失败，请检查网络')
    },
    onClose: () => {
      console.log('WebSocket disconnected')
    }
  })
}

// Handle WebSocket messages
const handleWebSocketMessage = (data) => {
  try {
    const message = JSON.parse(data)
    
    switch (message.type) {
      case 'auth_scanned':
        handleScanned()
        break
        
      case 'auth_success':
        handleAuthSuccess(message.data)
        break
        
      case 'auth_expired':
        handleExpired()
        break
        
      case 'heartbeat':
        // Keep connection alive
        break
        
      default:
        console.log('Unknown message type:', message.type)
    }
  } catch (error) {
    console.error('Failed to parse WebSocket message:', error)
  }
}

// Handle QR code scanned
const handleScanned = () => {
  isScanned.value = true
  authStore.setAuthStatus('scanned')
  ElMessage.success('扫码成功，请在手机上确认')
}

// Handle authentication success
const handleAuthSuccess = async (data) => {
  try {
    stopCountdown()
    closeWebSocket()
    
    // Store auth data
    await authStore.login(data.session_id, data)
    
    ElMessage.success('登录成功')
    
    // Show patient info if available
    if (data.patient_name) {
      ElMessage.info(`当前患者：${data.patient_name}`)
    }
    
    // Redirect to dashboard
    setTimeout(() => {
      if (data.patient_id) {
        // If there's a current patient, go directly to their medical records
        router.push(`/dashboard/medical-records/${data.patient_id}`)
      } else {
        router.push('/dashboard')
      }
    }, 1000)
    
  } catch (error) {
    console.error('Login error:', error)
    ElMessage.error('登录失败，请重试')
    refreshQRCode()
  }
}

// Refresh QR code
const refreshQRCode = () => {
  generateQRCode()
}

// Close WebSocket connection
const closeWebSocket = () => {
  if (ws) {
    ws.close()
    ws = null
  }
}

// Watch for auth status changes
const authStatus = computed(() => authStore.authStatus)
watch(authStatus, (newStatus) => {
  if (newStatus === 'pending') {
    // Re-generate QR code when auth status is reset to pending
    generateQRCode()
  }
})

// Lifecycle hooks
onMounted(() => {
  // Always generate a new QR code when component is mounted
  generateQRCode()
})

onUnmounted(() => {
  stopCountdown()
  closeWebSocket()
  if (statusCheckTimer) {
    clearInterval(statusCheckTimer)
  }
})
</script>

<style lang="scss" scoped>
.qr-login {
  max-width: 400px;
  margin: 0 auto;
  
  &__header {
    text-align: center;
    margin-bottom: 30px;
    
    h2 {
      font-size: 24px;
      color: #303133;
      margin-bottom: 8px;
    }
    
    .subtitle {
      color: #909399;
      font-size: 14px;
    }
  }
  
  &__container {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
    
    .qr-code-wrapper {
      position: relative;
      
      .qr-code {
        padding: 20px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        transition: all 0.3s;
        
        &.scanned {
          filter: blur(2px);
          opacity: 0.6;
        }
        
        .qr-loading {
          width: 200px;
          height: 200px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      
      .scan-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 8px;
        
        .success-icon {
          color: #67c23a;
          margin-bottom: 10px;
        }
        
        p {
          margin: 0;
          font-size: 16px;
          color: #303133;
          
          &.tips {
            font-size: 14px;
            color: #909399;
            margin-top: 8px;
          }
        }
      }
    }
    
    .qr-expired {
      padding: 40px;
      text-align: center;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      
      p {
        margin: 20px 0;
        color: #606266;
        font-size: 16px;
      }
    }
  }
  
  &__instructions {
    background: #f5f7fa;
    border-radius: 8px;
    padding: 20px;
    
    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      color: #606266;
    }
    
    ol {
      margin: 0;
      padding-left: 20px;
      
      li {
        color: #909399;
        font-size: 13px;
        line-height: 1.8;
      }
    }
  }
  
  &__timer {
    margin-top: 20px;
    text-align: center;
    
    .timer-text {
      display: block;
      margin-top: 8px;
      font-size: 12px;
      color: #909399;
    }
  }
}

// Transitions
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>