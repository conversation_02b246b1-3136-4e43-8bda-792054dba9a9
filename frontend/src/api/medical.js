/**
 * Medical records API module
 */
import request from '@/utils/request'

export const medicalApi = {
  /**
   * Get medical records
   */
  getMedicalRecords(patientId, params) {
    return request({
      url: `/api/medical-records/${patientId}`,
      method: 'get',
      params
    })
  },
  
  /**
   * Get medical record detail
   */
  getMedicalRecordDetail(patientId, recordId) {
    return request({
      url: `/api/medical-records/${patientId}/${recordId}`,
      method: 'get'
    })
  },
  
  /**
   * Export medical records
   */
  exportMedicalRecords(data) {
    return request({
      url: '/api/medical-records/export',
      method: 'post',
      data
    })
  },
  
  /**
   * Check export status
   */
  checkExportStatus(taskId) {
    return request({
      url: `/api/pdf/status/${taskId}`,
      method: 'get'
    })
  },
  
  /**
   * Download exported file
   */
  downloadExport(taskId) {
    return request({
      url: `/api/pdf/download/${taskId}`,
      method: 'get',
      responseType: 'blob'
    })
  },
  
  /**
   * Get prescription order detail (用药详情)
   */
  getPrescriptionOrderDetail(orderId) {
    return request({
      url: `/api/medical-records/prescription/${orderId}`,
      method: 'get'
    })
  },
  
  /**
   * Generate prescription PDF
   */
  generatePrescriptionPDF(orderId) {
    return request({
      url: '/api/pdf/generate-prescription',
      method: 'post',
      data: { order_id: orderId }
    })
  }
}