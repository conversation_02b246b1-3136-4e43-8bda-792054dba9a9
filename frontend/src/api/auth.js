/**
 * Authentication API module
 */
import request from '@/utils/request'

export const authApi = {
  /**
   * Check authentication status
   */
  checkStatus(authCode) {
    return request({
      url: `/api/auth/status/${authCode}`,
      method: 'get'
    })
  },
  
  /**
   * Refresh token
   */
  refreshToken(tempToken) {
    return request({
      url: '/api/auth/refresh',
      method: 'post',
      data: {
        temp_token: tempToken
      }
    })
  },
  
  /**
   * Validate token
   */
  validateToken(token) {
    return request({
      url: '/api/auth/validate',
      method: 'post',
      data: {
        token
      }
    })
  },
  
  /**
   * Logout
   */
  logout(token) {
    return request({
      url: '/api/auth/logout',
      method: 'post',
      data: {
        token
      }
    })
  }
}