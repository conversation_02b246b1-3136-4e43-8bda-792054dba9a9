import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import Cookies from 'js-cookie'
import { authApi } from '@/api/auth'

export const useAuthStore = defineStore('auth', () => {
  // State
  const sessionId = ref(Cookies.get('session_id') || '')
  const authCode = ref('') // Only used during auth process, not stored
  const userInfo = ref(null)
  const authStatus = ref('pending')
  const patientInfo = ref(null)
  
  // Getters
  const isAuthenticated = computed(() => !!sessionId.value && authStatus.value === 'authorized')
  const userId = computed(() => userInfo.value?.user_id || '')
  const currentPatientId = computed(() => patientInfo.value?.patient_id || '')
  const currentPatientName = computed(() => patientInfo.value?.patient_name || '')
  
  // Actions
  function setSessionId(id) {
    sessionId.value = id
    if (id) {
      Cookies.set('session_id', id, { expires: 1/48 }) // 30 minutes
    } else {
      Cookies.remove('session_id')
    }
  }
  
  function setAuthCode(code) {
    authCode.value = code
  }
  
  function setUserInfo(info) {
    userInfo.value = info
  }
  
  function setPatientInfo(info) {
    patientInfo.value = info
  }
  
  function setAuthStatus(status) {
    authStatus.value = status
  }
  
  async function login(session_id, info) {
    setSessionId(session_id)
    setUserInfo(info)
    setPatientInfo({
      patient_id: info.patient_id,
      patient_name: info.patient_name
    })
    setAuthStatus('authorized')
  }
  
  async function logout() {
    try {
      if (sessionId.value) {
        await authApi.logout(sessionId.value)
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      setSessionId('')
      setAuthCode('')
      setUserInfo(null)
      setPatientInfo(null)
      setAuthStatus('pending')
    }
  }
  
  async function validateSession() {
    if (!authCode.value) return false
    
    try {
      const response = await authApi.checkStatus(authCode.value)
      if (response.data && response.data.status === 'authorized') {
        setAuthStatus('authorized')
        return true
      }
    } catch (error) {
      console.error('Session validation error:', error)
      logout()
    }
    return false
  }
  
  return {
    // State
    sessionId,
    authCode,
    userInfo,
    authStatus,
    patientInfo,
    
    // Getters
    isAuthenticated,
    userId,
    currentPatientId,
    currentPatientName,
    
    // Actions
    setSessionId,
    setAuthCode,
    setUserInfo,
    setPatientInfo,
    setAuthStatus,
    login,
    logout,
    validateSession
  }
})