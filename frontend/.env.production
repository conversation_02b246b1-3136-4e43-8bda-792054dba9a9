# ===========================================
# 必然中医医案导出系统 - 前端生产环境配置
# ===========================================

# API 服务配置 (生产环境 - 请修改为实际后端地址)
VITE_API_URL=https://your-backend-domain.com

# WebSocket 地址 (生产环境 - 请修改为实际后端地址)
VITE_WS_URL=wss://your-backend-domain.com

# ===========================================
# 应用信息配置
# ===========================================

# 应用标题
VITE_APP_TITLE=必然中医 - 医案导出系统

# 应用版本
VITE_APP_VERSION=1.0.0

# ===========================================
# 生产环境配置
# ===========================================

# 环境模式
NODE_ENV=production

# 生产服务器端口
VITE_PROD_PORT=3000

# 生产服务器主机
VITE_PROD_HOST=0.0.0.0

# ===========================================
# 性能优化配置
# ===========================================

# 是否启用调试模式 (生产环境关闭)
VITE_DEBUG=false

# 日志级别 (生产环境只记录错误)
VITE_LOG_LEVEL=error

# 是否启用源码映射 (生产环境关闭)
VITE_SOURCE_MAP=false

# ===========================================
# 域名绑定配置 (生产环境)
# ===========================================

# 前端服务域名
FRONTEND_DOMAIN=your-frontend-domain.com

# CDN 配置 (如果使用 CDN)
VITE_CDN_URL=https://cdn.your-domain.com

# ===========================================
# 安全配置
# ===========================================

# 是否启用 HTTPS
VITE_HTTPS_ENABLED=true

# CSP 配置
VITE_CSP_ENABLED=true
