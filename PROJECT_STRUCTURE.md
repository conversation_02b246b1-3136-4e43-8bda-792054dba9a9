# 必然中医医案导出系统 - 项目结构说明

## 📁 项目结构

```
brzy_yian/
│
├── backend/                         # 🔧 后端服务
│   ├── app/                         # 应用代码
│   │   ├── api/                     # API 路由
│   │   ├── models/                  # 数据模型
│   │   ├── services/                # 业务逻辑
│   │   ├── utils/                   # 工具函数
│   │   ├── config.py                # 配置管理
│   │   ├── database.py              # 数据库连接
│   │   └── main.py                  # 应用入口
│   ├── logs/                        # 后端日志目录
│   ├── uploads/                     # 文件上传目录
│   ├── temp/                        # 临时文件目录
│   ├── fonts/                       # 字体文件目录
│   ├── generated_pdfs/              # PDF输出目录
│   ├── .env.development             # 开发环境配置
│   ├── .env.production              # 生产环境配置
│   ├── requirements.txt             # Python依赖
│   └── README.md                    # 后端说明文档
│
├── frontend/                        # 🎨 前端应用
│   ├── src/                         # 源代码
│   │   ├── api/                     # API 接口
│   │   ├── components/              # Vue 组件
│   │   ├── router/                  # 路由配置
│   │   ├── stores/                  # 状态管理
│   │   ├── styles/                  # 样式文件
│   │   ├── utils/                   # 工具函数
│   │   ├── views/                   # 页面组件
│   │   ├── App.vue                  # 根组件
│   │   └── main.js                  # 应用入口
│   ├── public/                      # 静态资源
│   ├── logs/                        # 前端日志目录
│   ├── .env.development             # 开发环境配置
│   ├── .env.production              # 生产环境配置
│   ├── package.json                 # Node.js依赖
│   ├── vite.config.js               # Vite配置
│   └── README.md                    # 前端说明文档
│
├── docs/                            # 📚 项目文档
│   ├── api/                         # API文档
│   ├── deployment/                  # 部署文档
│   ├── technical/                   # 技术文档
│   └── troubleshooting/             # 故障排除
│
├── README.md                        # 项目总体说明
└── PROJECT_STRUCTURE.md             # 项目结构说明 (本文件)
```

## 🚀 快速启动指南

### 后端启动

```bash
# 进入后端目录
cd backend

# 安装依赖
pip install -r requirements.txt

# 开发环境启动
export ENVIRONMENT=development
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# 生产环境启动
export ENVIRONMENT=production
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
```

### 前端启动

```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 开发环境启动
npm run dev

# 生产环境构建和启动
npm run build
npm run start
```

## 🔧 环境配置

### 后端环境配置

#### 开发环境 (.env.development)
- 数据库：`mysql://brzy_dev:brzy_dev_pass@localhost:3306/brzy_medical_dev`
- Redis：`redis://:dev_redis_pass@localhost:6379/0`
- 调试模式：开启
- 日志级别：DEBUG

#### 生产环境 (.env.production)
- 数据库：需要配置实际的生产数据库
- Redis：需要配置实际的生产Redis
- 调试模式：关闭
- 日志级别：INFO
- 安全密钥：需要更换为强密钥

### 前端环境配置

#### 开发环境 (.env.development)
- API地址：`http://localhost:8000`
- WebSocket：`ws://localhost:8000`
- 调试模式：开启
- 端口：5173

#### 生产环境 (.env.production)
- API地址：需要配置实际的后端地址
- WebSocket：需要配置实际的WebSocket地址
- 调试模式：关闭
- 端口：3000

## 📝 日志管理

### 后端日志
- 位置：`backend/logs/app.log`
- 格式：时间戳 + 日志级别 + 模块 + 消息
- 轮转：10MB文件大小，保留5个备份

### 前端日志
- 位置：浏览器 localStorage
- 保留：7天
- 导出：支持JSON格式下载

## 🗄️ 数据库配置

### MySQL 数据库

#### 开发环境
```sql
CREATE DATABASE brzy_medical_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'brzy_dev'@'%' IDENTIFIED BY 'brzy_dev_pass';
GRANT ALL PRIVILEGES ON brzy_medical_dev.* TO 'brzy_dev'@'%';
```

#### 生产环境
```sql
CREATE DATABASE brzy_medical_prod CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'brzy_prod'@'%' IDENTIFIED BY 'CHANGE_PROD_PASSWORD';
GRANT ALL PRIVILEGES ON brzy_medical_prod.* TO 'brzy_prod'@'%';
```

### Redis 配置

#### 开发环境
```bash
redis-cli CONFIG SET requirepass "dev_redis_pass"
```

#### 生产环境
```bash
redis-cli CONFIG SET requirepass "CHANGE_REDIS_PASSWORD"
```

## 🌐 部署说明

### 后端部署

1. **配置环境变量**：设置 `ENVIRONMENT=production`
2. **修改配置文件**：更新 `.env.production` 中的数据库、Redis、域名等配置
3. **安装依赖**：`pip install -r requirements.txt`
4. **启动服务**：使用 Gunicorn 或 Docker 部署

### 前端部署

1. **修改配置文件**：更新 `.env.production` 中的API地址
2. **构建项目**：`npm run build`
3. **部署静态文件**：将 `dist/` 目录部署到 Web 服务器

## 🔒 安全配置

### 生产环境必须修改的配置

#### 后端
- `SECRET_KEY`：应用密钥
- `JWT_SECRET_KEY`：JWT密钥
- `DATABASE_URL`：数据库连接
- `REDIS_URL`：Redis连接
- `CORS_ORIGINS`：允许的前端域名
- `ALLOWED_HOSTS`：允许的主机

#### 前端
- `VITE_API_URL`：后端API地址
- `VITE_WS_URL`：WebSocket地址

## 📊 监控和维护

### 健康检查
- 后端：`GET /health`
- 前端：检查页面加载状态

### 日志监控
- 后端：监控 `backend/logs/app.log`
- 前端：检查浏览器控制台和localStorage

### 性能监控
- 数据库连接池状态
- Redis连接状态
- API响应时间
- 前端页面加载时间

## 🚨 故障排除

### 常见问题

1. **数据库连接失败**：检查数据库服务和连接配置
2. **Redis连接失败**：检查Redis服务和密码配置
3. **API连接失败**：检查后端服务状态和网络连接
4. **WebSocket连接失败**：检查WebSocket配置和防火墙
5. **端口占用**：使用 `lsof -i :端口号` 检查占用情况

### 日志查看

```bash
# 后端日志
tail -f backend/logs/app.log

# 系统日志
journalctl -u your-service-name -f

# 容器日志 (如果使用Docker)
docker logs -f container-name
```
