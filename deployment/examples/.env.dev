# ===========================================
# 开发环境配置示例
# ===========================================

# 项目基础配置
PROJECT_NAME=brzy-medical-dev
VERSION=latest
ENVIRONMENT=development

# 应用信息
APP_NAME=必然中医 - 医案导出系统 (开发环境)
DEBUG=true
LOG_LEVEL=DEBUG

# ===========================================
# 容器镜像配置 (开发环境)
# ===========================================

# 使用本地构建或开发镜像
REGISTRY=localhost:5000
REGISTRY_NAMESPACE=brzy-medical
IMAGE_TAG=dev

FRONTEND_IMAGE=${REGISTRY}/${REGISTRY_NAMESPACE}/frontend:${IMAGE_TAG}
BACKEND_IMAGE=${REGISTRY}/${REGISTRY_NAMESPACE}/backend:${IMAGE_TAG}

# ===========================================
# 数据库配置 (开发环境)
# ===========================================

MYSQL_ROOT_PASSWORD=dev_root_password
MYSQL_DATABASE=brzy_medical_dev
MYSQL_USER=brzy_dev_user
MYSQL_PASSWORD=dev_user_password
MYSQL_PORT=3306

# ===========================================
# Redis 配置
# ===========================================

REDIS_PASSWORD=dev_redis_password
REDIS_PORT=6379

# ===========================================
# 安全配置 (开发环境)
# ===========================================

# 开发环境 JWT 密钥
JWT_SECRET_KEY=dev_jwt_secret_key_not_for_production

# 开发环境 API 地址
ORIGINAL_API_BASE_URL=https://api-dev.haoniuzhongyi.top:9090

# 开发环境 CORS (允许本地开发)
CORS_ORIGINS=http://localhost:3000,http://localhost:5173,http://127.0.0.1:5173

# ===========================================
# 前端配置
# ===========================================

FRONTEND_API_URL=http://localhost:8080
FRONTEND_WS_URL=ws://localhost:8080
FRONTEND_PORT=5173

# ===========================================
# 后端配置
# ===========================================

BACKEND_PORT=8080
BACKEND_WORKERS=2

# ===========================================
# 网络和代理配置
# ===========================================

HTTP_PORT=80
HTTPS_PORT=443
NETWORK_SUBNET=**********/16

# ===========================================
# 存储路径配置 (开发环境)
# ===========================================

DATA_DIR=./data
LOG_DIR=./logs
CONFIG_DIR=./config
SSL_DIR=./ssl

# ===========================================
# 监控配置 (开发环境)
# ===========================================

PROMETHEUS_PORT=9090
GRAFANA_PORT=3000
GRAFANA_PASSWORD=dev_grafana_admin

# ===========================================
# 开发特性
# ===========================================

ENABLE_DEBUG=true
ENABLE_PROFILING=true
VERBOSE_LOGGING=true

# 开发环境热重载
HOT_RELOAD=true
AUTO_RELOAD=true