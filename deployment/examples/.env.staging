# ===========================================
# 测试环境配置示例
# ===========================================

# 项目基础配置
PROJECT_NAME=brzy-medical-staging
VERSION=v1.0.0-rc1
ENVIRONMENT=staging

# 应用信息
APP_NAME=必然中医 - 医案导出系统 (测试环境)
DEBUG=false
LOG_LEVEL=INFO

# ===========================================
# 容器镜像配置 (测试环境)
# ===========================================

# 使用私有制品库镜像
REGISTRY=registry.your-company.com
REGISTRY_NAMESPACE=brzy-medical
IMAGE_TAG=staging

FRONTEND_IMAGE=${REGISTRY}/${REGISTRY_NAMESPACE}/frontend:${IMAGE_TAG}
BACKEND_IMAGE=${REGISTRY}/${REGISTRY_NAMESPACE}/backend:${IMAGE_TAG}

# ===========================================
# 数据库配置 (测试环境)
# ===========================================

MYSQL_ROOT_PASSWORD=staging_secure_root_password_123
MYSQL_DATABASE=brzy_medical_staging
MYSQL_USER=brzy_staging_user
MYSQL_PASSWORD=staging_secure_user_password_456
MYSQL_PORT=3306

# ===========================================
# Redis 配置
# ===========================================

REDIS_PASSWORD=staging_redis_password_789
REDIS_PORT=6379

# ===========================================
# 安全配置 (测试环境)
# ===========================================

# 测试环境 JWT 密钥 (应定期更换)
JWT_SECRET_KEY=staging_jwt_secret_key_replace_in_production

# 测试环境 API 地址
ORIGINAL_API_BASE_URL=https://api-staging.haoniuzhongyi.top:9090

# 测试环境 CORS
CORS_ORIGINS=https://staging-medical.your-domain.com,https://test.your-domain.com

# ===========================================
# 前端配置
# ===========================================

FRONTEND_API_URL=https://staging-api.your-domain.com
FRONTEND_WS_URL=wss://staging-api.your-domain.com
FRONTEND_PORT=5173

# ===========================================
# 后端配置
# ===========================================

BACKEND_PORT=8080
BACKEND_WORKERS=3

# ===========================================
# 网络和代理配置
# ===========================================

HTTP_PORT=80
HTTPS_PORT=443
NETWORK_SUBNET=172.21.0.0/16

# ===========================================
# 存储路径配置 (测试环境)
# ===========================================

DATA_DIR=/opt/brzy_medical_staging/data
LOG_DIR=/opt/brzy_medical_staging/logs
CONFIG_DIR=/opt/brzy_medical_staging/config
SSL_DIR=/opt/brzy_medical_staging/ssl

# ===========================================
# 监控配置 (测试环境)
# ===========================================

PROMETHEUS_PORT=9090
GRAFANA_PORT=3000
GRAFANA_PASSWORD=staging_secure_grafana_password

# ===========================================
# 测试环境特性
# ===========================================

ENABLE_DEBUG=false
ENABLE_PROFILING=false
VERBOSE_LOGGING=false

# 性能测试相关
LOAD_TEST_MODE=false
STRESS_TEST_ENABLED=false

# 备份配置
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 4 * * *
BACKUP_RETENTION=14