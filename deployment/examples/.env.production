# ===========================================
# 生产环境配置示例
# ===========================================

# 项目基础配置
PROJECT_NAME=brzy-medical
VERSION=v1.0.0
ENVIRONMENT=production

# 应用信息
APP_NAME=必然中医 - 医案导出系统
DEBUG=false
LOG_LEVEL=WARNING

# ===========================================
# 容器镜像配置 (生产环境)
# ===========================================

# 使用生产级私有制品库
REGISTRY=registry.your-company.com
REGISTRY_NAMESPACE=brzy-medical
IMAGE_TAG=v1.0.0

FRONTEND_IMAGE=${REGISTRY}/${REGISTRY_NAMESPACE}/frontend:${IMAGE_TAG}
BACKEND_IMAGE=${REGISTRY}/${REGISTRY_NAMESPACE}/backend:${IMAGE_TAG}

# ===========================================
# 数据库配置 (生产环境)
# ===========================================

# ⚠️ 请替换为实际的强密码
MYSQL_ROOT_PASSWORD=CHANGE_THIS_PRODUCTION_ROOT_PASSWORD
MYSQL_DATABASE=brzy_medical
MYSQL_USER=brzy_prod_user
MYSQL_PASSWORD=CHANGE_THIS_PRODUCTION_USER_PASSWORD
MYSQL_PORT=3306

# 数据库连接池配置
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=10

# ===========================================
# Redis 配置 (生产环境)
# ===========================================

# ⚠️ 请替换为实际的强密码
REDIS_PASSWORD=CHANGE_THIS_PRODUCTION_REDIS_PASSWORD
REDIS_PORT=6379

# ===========================================
# 安全配置 (生产环境)
# ===========================================

# ⚠️ 请替换为实际的强密钥 (至少32位)
JWT_SECRET_KEY=CHANGE_THIS_PRODUCTION_JWT_SECRET_KEY_32_CHARS_MIN

# 必然中医生产 API 地址
ORIGINAL_API_BASE_URL=https://api.haoniuzhongyi.top:9090

# 生产环境 CORS (仅允许实际域名)
CORS_ORIGINS=https://medical.your-domain.com,https://www.your-domain.com

# ===========================================
# 前端配置 (生产环境)
# ===========================================

FRONTEND_API_URL=https://api.your-domain.com
FRONTEND_WS_URL=wss://api.your-domain.com
FRONTEND_PORT=5173

# ===========================================
# 后端配置 (生产环境)
# ===========================================

BACKEND_PORT=8080
# 生产环境建议: CPU 核心数 * 2
BACKEND_WORKERS=8

# ===========================================
# 网络和代理配置
# ===========================================

HTTP_PORT=80
HTTPS_PORT=443
NETWORK_SUBNET=172.22.0.0/16

# ===========================================
# 存储路径配置 (生产环境)
# ===========================================

DATA_DIR=/opt/brzy_medical_system/data
LOG_DIR=/opt/brzy_medical_system/logs
CONFIG_DIR=/opt/brzy_medical_system/config
SSL_DIR=/opt/brzy_medical_system/ssl

# ===========================================
# 监控配置 (生产环境)
# ===========================================

PROMETHEUS_PORT=9090
GRAFANA_PORT=3000
# ⚠️ 请替换为实际的强密码
GRAFANA_PASSWORD=CHANGE_THIS_PRODUCTION_GRAFANA_PASSWORD

# ===========================================
# 生产环境安全设置
# ===========================================

ENABLE_DEBUG=false
ENABLE_PROFILING=false
VERBOSE_LOGGING=false

# 安全增强
SECURITY_HEADERS=true
RATE_LIMITING=true
FAIL2BAN_ENABLED=true

# ===========================================
# 备份和恢复 (生产环境)
# ===========================================

BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # 每天凌晨2点备份
BACKUP_RETENTION=30        # 保留30天备份
BACKUP_ENCRYPTION=true

# 异地备份
REMOTE_BACKUP_ENABLED=true
REMOTE_BACKUP_ENDPOINT=s3://your-backup-bucket/brzy-medical

# ===========================================
# 性能优化 (生产环境)
# ===========================================

# 缓存策略
CACHE_TTL=3600
STATIC_CACHE_TTL=86400

# 连接限制
MAX_CONNECTIONS=1000
CONNECTION_TIMEOUT=30

# 资源限制
BACKEND_MEMORY_LIMIT=2g
FRONTEND_MEMORY_LIMIT=512m
MYSQL_MEMORY_LIMIT=4g
REDIS_MEMORY_LIMIT=1g

# ===========================================
# 告警和通知
# ===========================================

ALERT_EMAIL=<EMAIL>
ALERT_WEBHOOK=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
SMS_ALERT_ENABLED=true

# 监控阈值
CPU_ALERT_THRESHOLD=80
MEMORY_ALERT_THRESHOLD=85
DISK_ALERT_THRESHOLD=90

# ===========================================
# 合规和审计
# ===========================================

AUDIT_LOG_ENABLED=true
ACCESS_LOG_RETENTION=90
GDPR_COMPLIANCE=true
DATA_ENCRYPTION_AT_REST=true

# ===========================================
# 灾难恢复
# ===========================================

DR_SITE_ENABLED=false
FAILOVER_TIMEOUT=300
RTO_TARGET=900  # 15分钟恢复时间目标
RPO_TARGET=300  # 5分钟恢复点目标