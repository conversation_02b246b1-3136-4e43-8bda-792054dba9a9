# Webhook 自动部署配置指南

本文档说明如何配置 Git Webhook 实现推送后自动部署。

## 功能概述

- 监听 Git 推送事件
- 验证请求来源（支持 GitHub/GitLab）
- 自动拉取代码并执行部署
- 与现有后端服务共享域名

## 快速开始

### 1. 配置环境变量

编辑部署环境配置文件（如 `.env.production`）：

```bash
# Webhook 配置
WEBHOOK_PORT=8090
WEBHOOK_SECRET=your-secure-webhook-secret
AUTO_DEPLOY_BRANCH=release
DEPLOY_ENVIRONMENT=production
```

### 2. 启动 Webhook 服务

```bash
# 前台运行（调试）
./deployment/scripts/webhook-server.sh

# 后台运行（生产环境）
./deployment/scripts/webhook-server.sh --daemon

# 查看状态
./deployment/scripts/webhook-server.sh --status

# 停止服务
./deployment/scripts/webhook-server.sh --stop
```

### 3. 配置 Nginx

使用提供的配置示例：

```bash
cp deployment/config/nginx/webhook.conf.example /etc/nginx/sites-available/your-domain.conf
# 编辑配置文件，替换 your-domain.com 为实际域名
# 重启 Nginx
nginx -t && systemctl reload nginx
```

## Git 仓库配置

### GitHub 配置

1. 进入仓库 Settings -> Webhooks -> Add webhook
2. 配置以下信息：
   - **Payload URL**: `https://your-domain.com/webhook/deploy`
   - **Content type**: `application/json`
   - **Secret**: 与 `WEBHOOK_SECRET` 相同的值
   - **Events**: 选择 "Just the push event"
3. 保存配置

### GitLab 配置

1. 进入项目 Settings -> Webhooks
2. 配置以下信息：
   - **URL**: `https://your-domain.com/webhook/deploy`
   - **Secret token**: 与 `WEBHOOK_SECRET` 相同的值
   - **Trigger**: 勾选 "Push events"
   - **Branch filter**: 填写 `release`（或你的部署分支）
3. 保存配置

## 工作流程

1. 开发者推送代码到指定分支（如 `release`）
2. Git 服务器发送 Webhook 请求到配置的 URL
3. Webhook 服务验证请求签名
4. 如果是目标分支，执行以下操作：
   - 拉取最新代码
   - 调用部署脚本
   - 记录部署日志

## 安全建议

1. **使用强密钥**：生成随机密钥作为 webhook secret
   ```bash
   openssl rand -hex 32
   ```

2. **限制访问**：在 nginx 中限制 webhook 端点的访问来源
   ```nginx
   location /webhook/ {
       allow 192.30.252.0/22;  # GitHub
       allow 140.82.112.0/20;  # GitHub
       deny all;
   }
   ```

3. **监控日志**：定期检查 webhook 日志
   ```bash
   tail -f deployment/logs/webhook.log
   ```

## 高级配置

### 多环境部署

为不同分支配置不同的部署环境：

```bash
# 开发环境
./webhook-server.sh --branch develop --env dev --port 8091 --daemon

# 测试环境
./webhook-server.sh --branch staging --env staging --port 8092 --daemon

# 生产环境
./webhook-server.sh --branch release --env production --port 8093 --daemon
```

### Systemd 服务

创建 systemd 服务文件 `/etc/systemd/system/brzy-webhook.service`：

```ini
[Unit]
Description=BRZY Medical Webhook Server
After=network.target

[Service]
Type=simple
User=deploy
WorkingDirectory=/opt/brzy_medical_system
Environment="WEBHOOK_PORT=8090"
Environment="WEBHOOK_SECRET=your-secret"
Environment="AUTO_DEPLOY_BRANCH=release"
Environment="DEPLOY_ENVIRONMENT=production"
ExecStart=/opt/brzy_medical_system/deployment/scripts/webhook-server.sh
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启用服务：
```bash
systemctl enable brzy-webhook
systemctl start brzy-webhook
systemctl status brzy-webhook
```

## 故障排查

### 查看日志

```bash
# Webhook 服务日志
tail -f deployment/logs/webhook.log

# Nginx 访问日志
tail -f /var/log/nginx/access.log | grep webhook

# 部署脚本日志
tail -f deployment/logs/deploy.log
```

### 测试 Webhook

```bash
# 测试健康检查
curl https://your-domain.com/webhook/health

# 手动触发测试（需要正确的签名）
curl -X POST https://your-domain.com/webhook/deploy \
  -H "Content-Type: application/json" \
  -H "X-Hub-Signature-256: sha256=..." \
  -d '{"ref":"refs/heads/release","after":"abc123..."}'
```

### 常见问题

1. **签名验证失败**
   - 确保 Git 仓库配置的 secret 与服务器一致
   - 检查是否有额外的空格或换行

2. **部署未触发**
   - 确认推送的分支名称正确
   - 查看 webhook 日志确认请求是否到达

3. **权限问题**
   - 确保运行用户有权限访问 Git 仓库
   - 确保有权限执行部署脚本

## 与现有系统集成

Webhook 服务设计为与现有的必然中医系统无缝集成：

- **共享域名**：通过 Nginx 反向代理，webhook 端点与 API 使用同一域名
- **独立服务**：Webhook 服务独立运行，不影响主要业务
- **配置复用**：使用相同的环境配置文件
- **日志整合**：日志保存在统一的日志目录

## 扩展功能

未来可以扩展的功能：

- 部署前后的通知（邮件、钉钉、企业微信）
- 部署结果的 Web 界面展示
- 支持更多 Git 平台（Gitee、Coding 等）
- 回滚功能
- 蓝绿部署支持