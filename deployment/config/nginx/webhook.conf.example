# Webhook 服务 Nginx 配置示例
# 此配置展示如何将 Webhook 服务与后端 API 整合到同一域名下

# 上游服务定义
upstream webhook_backend {
    server 127.0.0.1:8090;  # Webhook 服务端口
}

# 在主要的 server 块中添加以下 location 配置
# (通常在 /etc/nginx/sites-available/your-domain.conf)

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    # SSL 配置...
    
    # 现有的后端 API 配置
    location /api/ {
        proxy_pass http://backend:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # WebSocket 配置
    location /ws/ {
        proxy_pass http://backend:8080/ws/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # Webhook 端点配置
    location /webhook/ {
        proxy_pass http://webhook_backend/webhook/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 传递 GitHub/GitLab 的签名头
        proxy_set_header X-Hub-Signature-256 $http_x_hub_signature_256;
        proxy_set_header X-Gitlab-Token $http_x_gitlab_token;
        
        # 请求体大小限制 (Git push 可能包含大量数据)
        client_max_body_size 50M;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Webhook 健康检查端点
    location /webhook/health {
        proxy_pass http://webhook_backend/webhook/health;
        access_log off;
    }
    
    # 前端静态文件
    location / {
        proxy_pass http://frontend:5173/;
        # 或者直接服务静态文件
        # root /var/www/brzy-medical-web;
        # try_files $uri $uri/ /index.html;
    }
}

# HTTP 到 HTTPS 重定向
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}