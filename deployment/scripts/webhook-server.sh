#!/bin/bash

# BRZY Medical System Webhook Server
# 用于接收 Git 推送通知并自动触发部署

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../.." && pwd)"
DEPLOYMENT_DIR="${PROJECT_ROOT}/deployment"
DEPLOY_SCRIPT="${SCRIPT_DIR}/deploy.sh"

# 默认配置
WEBHOOK_PORT=${WEBHOOK_PORT:-8090}
WEBHOOK_SECRET=${WEBHOOK_SECRET:-}
AUTO_DEPLOY_BRANCH=${AUTO_DEPLOY_BRANCH:-release}
DEPLOY_ENVIRONMENT=${DEPLOY_ENVIRONMENT:-production}
LOG_FILE="${LOG_FILE:-${DEPLOYMENT_DIR}/logs/webhook.log}"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# 显示帮助信息
show_help() {
    cat << EOF
BRZY Medical System Webhook Server

用法: $0 [选项]

选项:
    --port PORT          Webhook 监听端口 (默认: 8090)
    --secret SECRET      Webhook 密钥验证
    --branch BRANCH      触发部署的分支 (默认: release)
    --env ENVIRONMENT    部署环境 (默认: production)
    --daemon             以守护进程方式运行
    --stop               停止 webhook 服务
    --status             查看服务状态
    --help               显示此帮助信息

环境变量:
    WEBHOOK_PORT         监听端口
    WEBHOOK_SECRET       密钥验证
    AUTO_DEPLOY_BRANCH   自动部署分支
    DEPLOY_ENVIRONMENT   部署环境

示例:
    $0 --port 8090 --secret mySecret123 --branch main
    $0 --daemon                                      # 后台运行
    $0 --stop                                        # 停止服务

Git 仓库配置:
    GitHub:
      Settings -> Webhooks -> Add webhook
      Payload URL: https://your-domain.com/webhook/deploy
      Content type: application/json
      Secret: 与 WEBHOOK_SECRET 相同
      Events: Just the push event

    GitLab:
      Settings -> Webhooks
      URL: https://your-domain.com/webhook/deploy
      Secret token: 与 WEBHOOK_SECRET 相同
      Trigger: Push events

EOF
}

# 参数解析
DAEMON=false
STOP=false
STATUS=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --port)
            WEBHOOK_PORT=$2
            shift 2
            ;;
        --secret)
            WEBHOOK_SECRET=$2
            shift 2
            ;;
        --branch)
            AUTO_DEPLOY_BRANCH=$2
            shift 2
            ;;
        --env)
            DEPLOY_ENVIRONMENT=$2
            shift 2
            ;;
        --daemon)
            DAEMON=true
            shift
            ;;
        --stop)
            STOP=true
            shift
            ;;
        --status)
            STATUS=true
            shift
            ;;
        --help|-h)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# PID 文件
PID_FILE="${DEPLOYMENT_DIR}/webhook.pid"

# 停止服务
stop_service() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if kill -0 "$PID" 2>/dev/null; then
            kill "$PID"
            rm -f "$PID_FILE"
            log_success "Webhook 服务已停止 (PID: $PID)"
        else
            rm -f "$PID_FILE"
            log_warning "服务进程不存在，清理 PID 文件"
        fi
    else
        log_info "Webhook 服务未运行"
    fi
}

# 查看状态
check_status() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if kill -0 "$PID" 2>/dev/null; then
            log_success "Webhook 服务运行中 (PID: $PID)"
            log_info "监听端口: $WEBHOOK_PORT"
            log_info "部署分支: $AUTO_DEPLOY_BRANCH"
            log_info "部署环境: $DEPLOY_ENVIRONMENT"
        else
            log_error "服务已停止，但 PID 文件存在"
            rm -f "$PID_FILE"
        fi
    else
        log_info "Webhook 服务未运行"
    fi
}

# 处理停止和状态命令
if [ "$STOP" = true ]; then
    stop_service
    exit 0
fi

if [ "$STATUS" = true ]; then
    check_status
    exit 0
fi

# 创建日志目录
mkdir -p "$(dirname "$LOG_FILE")"

# 验证 webhook 签名 (GitHub)
verify_github_signature() {
    local payload="$1"
    local signature="$2"
    
    if [ -z "$WEBHOOK_SECRET" ]; then
        return 0
    fi
    
    local expected_sig="sha256=$(echo -n "$payload" | openssl dgst -sha256 -hmac "$WEBHOOK_SECRET" | awk '{print $2}')"
    
    if [ "$signature" = "$expected_sig" ]; then
        return 0
    else
        return 1
    fi
}

# 验证 webhook 签名 (GitLab)
verify_gitlab_token() {
    local token="$1"
    
    if [ -z "$WEBHOOK_SECRET" ]; then
        return 0
    fi
    
    if [ "$token" = "$WEBHOOK_SECRET" ]; then
        return 0
    else
        return 1
    fi
}

# 处理部署
handle_deploy() {
    local branch="$1"
    local commit="$2"
    local author="$3"
    
    log_info "收到推送事件: 分支=$branch, 提交=$commit, 作者=$author"
    
    # 检查是否是目标分支
    if [ "$branch" != "$AUTO_DEPLOY_BRANCH" ]; then
        log_info "跳过部署: 分支 '$branch' 不是目标分支 '$AUTO_DEPLOY_BRANCH'"
        return 0
    fi
    
    log_success "触发自动部署..."
    
    # 切换到项目目录
    cd "$PROJECT_ROOT"
    
    # 拉取最新代码
    log_info "拉取最新代码..."
    git pull origin "$branch" || {
        log_error "拉取代码失败"
        return 1
    }
    
    # 执行部署脚本
    log_info "执行部署脚本..."
    "$DEPLOY_SCRIPT" "$DEPLOY_ENVIRONMENT" --pull-images || {
        log_error "部署失败"
        return 1
    }
    
    log_success "部署完成!"
}

# Python webhook 服务器
create_webhook_server() {
    cat << 'EOF'
#!/usr/bin/env python3
import json
import os
import sys
import hmac
import hashlib
import subprocess
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse

class WebhookHandler(BaseHTTPRequestHandler):
    def do_POST(self):
        if self.path != '/webhook/deploy':
            self.send_response(404)
            self.end_headers()
            return
        
        # 读取请求体
        content_length = int(self.headers.get('Content-Length', 0))
        payload = self.rfile.read(content_length)
        
        # 获取环境变量
        webhook_secret = os.environ.get('WEBHOOK_SECRET', '')
        
        # 验证签名
        if webhook_secret:
            # GitHub 签名验证
            signature = self.headers.get('X-Hub-Signature-256', '')
            if signature:
                expected_sig = 'sha256=' + hmac.new(
                    webhook_secret.encode(),
                    payload,
                    hashlib.sha256
                ).hexdigest()
                
                if signature != expected_sig:
                    self.send_response(401)
                    self.end_headers()
                    self.wfile.write(b'Invalid signature')
                    return
            
            # GitLab token 验证
            token = self.headers.get('X-Gitlab-Token', '')
            if token and token != webhook_secret:
                self.send_response(401)
                self.end_headers()
                self.wfile.write(b'Invalid token')
                return
        
        try:
            data = json.loads(payload.decode())
            
            # 解析 Git 事件
            branch = None
            commit = None
            author = None
            
            # GitHub push event
            if 'ref' in data and data.get('ref', '').startswith('refs/heads/'):
                branch = data['ref'].replace('refs/heads/', '')
                commit = data.get('after', '')[:8]
                author = data.get('pusher', {}).get('name', 'unknown')
            
            # GitLab push event
            elif 'object_kind' in data and data['object_kind'] == 'push':
                branch = data.get('ref', '').replace('refs/heads/', '')
                commit = data.get('after', '')[:8]
                author = data.get('user_username', 'unknown')
            
            if branch:
                # 调用部署处理脚本
                cmd = [
                    sys.argv[1],  # deploy handler script
                    branch,
                    commit or 'unknown',
                    author or 'unknown'
                ]
                
                subprocess.Popen(cmd)
                
                self.send_response(200)
                self.end_headers()
                self.wfile.write(b'Deployment triggered')
            else:
                self.send_response(200)
                self.end_headers()
                self.wfile.write(b'Event ignored')
                
        except Exception as e:
            print(f"Error: {e}")
            self.send_response(500)
            self.end_headers()
            self.wfile.write(b'Internal server error')
    
    def do_GET(self):
        if self.path == '/webhook/health':
            self.send_response(200)
            self.end_headers()
            self.wfile.write(b'OK')
        else:
            self.send_response(404)
            self.end_headers()
    
    def log_message(self, format, *args):
        # 自定义日志格式
        print(f"[{self.log_date_time_string()}] {format % args}")

if __name__ == '__main__':
    port = int(os.environ.get('WEBHOOK_PORT', 8090))
    server = HTTPServer(('0.0.0.0', port), WebhookHandler)
    print(f"Webhook server listening on port {port}")
    server.serve_forever()
EOF
}

# 创建部署处理脚本
create_deploy_handler() {
    cat << EOF
#!/bin/bash
# Webhook 部署处理器
source "$SCRIPT_DIR/webhook-server.sh"
handle_deploy "\$1" "\$2" "\$3"
EOF
}

# 启动 webhook 服务
start_webhook_server() {
    # 检查是否已运行
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if kill -0 "$PID" 2>/dev/null; then
            log_error "Webhook 服务已在运行 (PID: $PID)"
            exit 1
        fi
    fi
    
    # 创建临时文件
    PYTHON_SCRIPT=$(mktemp)
    HANDLER_SCRIPT=$(mktemp)
    
    # 写入脚本内容
    create_webhook_server > "$PYTHON_SCRIPT"
    create_deploy_handler > "$HANDLER_SCRIPT"
    chmod +x "$HANDLER_SCRIPT"
    
    # 导出环境变量
    export WEBHOOK_PORT
    export WEBHOOK_SECRET
    export AUTO_DEPLOY_BRANCH
    export DEPLOY_ENVIRONMENT
    
    if [ "$DAEMON" = true ]; then
        # 后台运行
        nohup python3 "$PYTHON_SCRIPT" "$HANDLER_SCRIPT" >> "$LOG_FILE" 2>&1 &
        PID=$!
        echo $PID > "$PID_FILE"
        log_success "Webhook 服务已启动 (PID: $PID)"
        log_info "监听端口: $WEBHOOK_PORT"
        log_info "部署分支: $AUTO_DEPLOY_BRANCH"
        log_info "部署环境: $DEPLOY_ENVIRONMENT"
        log_info "日志文件: $LOG_FILE"
    else
        # 前台运行
        log_success "Webhook 服务启动中..."
        log_info "监听端口: $WEBHOOK_PORT"
        log_info "部署分支: $AUTO_DEPLOY_BRANCH"
        log_info "部署环境: $DEPLOY_ENVIRONMENT"
        python3 "$PYTHON_SCRIPT" "$HANDLER_SCRIPT"
    fi
}

# 主函数
main() {
    log_info "🚀 启动 Webhook 服务..."
    
    # 检查依赖
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装"
        exit 1
    fi
    
    # 检查部署脚本
    if [ ! -f "$DEPLOY_SCRIPT" ]; then
        log_error "部署脚本不存在: $DEPLOY_SCRIPT"
        exit 1
    fi
    
    # 启动服务
    start_webhook_server
}

# 执行主函数
main