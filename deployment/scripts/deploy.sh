#!/bin/bash

# BRZY Medical System CNB Artifacts Deployment Script
# 自动化部署 CNB 构建的制品到生产环境

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../.." && pwd)"
DEPLOYMENT_DIR="${PROJECT_ROOT}/deployment"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
BRZY Medical System CNB Deployment Script

用法: $0 [选项] <环境>

环境:
    dev         开发环境
    staging     测试环境  
    production  生产环境

选项:
    --config-only    只生成配置文件，不部署
    --pull-images    强制拉取最新镜像
    --backup        部署前备份数据
    --dry-run       模拟运行，不执行实际部署
    --help          显示此帮助信息

示例:
    $0 production --backup           # 生产环境部署，带备份
    $0 staging --config-only         # 生成测试环境配置
    $0 dev --pull-images             # 开发环境部署，强制拉取镜像

环境变量:
    REGISTRY_USERNAME    制品库用户名
    REGISTRY_PASSWORD    制品库密码
    BACKUP_DIR          备份目录 (默认: ./backups)
    CONFIG_FILE         环境配置文件 (默认: .env.\${environment})

EOF
}

# 参数解析
ENVIRONMENT=""
CONFIG_ONLY=false
PULL_IMAGES=false
BACKUP=false
DRY_RUN=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --config-only)
            CONFIG_ONLY=true
            shift
            ;;
        --pull-images)
            PULL_IMAGES=true
            shift
            ;;
        --backup)
            BACKUP=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --help|-h)
            show_help
            exit 0
            ;;
        dev|staging|production)
            ENVIRONMENT=$1
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证环境参数
if [ -z "$ENVIRONMENT" ]; then
    log_error "请指定部署环境: dev, staging, 或 production"
    show_help
    exit 1
fi

# 配置文件路径
CONFIG_FILE="${CONFIG_FILE:-${DEPLOYMENT_DIR}/.env.${ENVIRONMENT}}"
DOCKER_COMPOSE_FILE="${DEPLOYMENT_DIR}/docker-compose.${ENVIRONMENT}.yml"

log_info "🚀 开始 ${ENVIRONMENT} 环境部署..."
log_info "配置文件: ${CONFIG_FILE}"
log_info "Compose文件: ${DOCKER_COMPOSE_FILE}"

# 检查必要文件
check_prerequisites() {
    log_info "📋 检查部署前置条件..."
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    # 检查 Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
    
    # 检查配置文件
    if [ ! -f "$CONFIG_FILE" ]; then
        log_error "配置文件不存在: $CONFIG_FILE"
        log_info "请复制模板文件并填入配置:"
        log_info "cp ${DEPLOYMENT_DIR}/templates/env.template $CONFIG_FILE"
        exit 1
    fi
    
    log_success "前置条件检查完成"
}

# 生成 Docker Compose 配置
generate_compose_config() {
    log_info "📝 生成 Docker Compose 配置..."
    
    # 加载环境变量
    source "$CONFIG_FILE"
    
    # 验证必需的环境变量
    required_vars=(
        "FRONTEND_IMAGE"
        "BACKEND_IMAGE" 
        "MYSQL_ROOT_PASSWORD"
        "MYSQL_PASSWORD"
        "JWT_SECRET_KEY"
        "ORIGINAL_API_BASE_URL"
    )
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            log_error "必需的环境变量未设置: $var"
            exit 1
        fi
    done
    
    # 使用 envsubst 替换模板变量
    envsubst < "${DEPLOYMENT_DIR}/templates/docker-compose.template.yml" > "$DOCKER_COMPOSE_FILE"
    
    log_success "Docker Compose 配置生成完成"
}

# 创建必要目录
create_directories() {
    log_info "📁 创建必要目录..."
    
    source "$CONFIG_FILE"
    
    directories=(
        "${DATA_DIR:-./data}/uploads"
        "${DATA_DIR:-./data}/generated_pdfs"
        "${DATA_DIR:-./data}/temp"
        "${LOG_DIR:-./logs}/backend"
        "${LOG_DIR:-./logs}/frontend"
        "${LOG_DIR:-./logs}/nginx"
        "${CONFIG_DIR:-./config}/nginx"
        "${CONFIG_DIR:-./config}/mysql"
        "${CONFIG_DIR:-./config}/prometheus"
        "${CONFIG_DIR:-./config}/grafana"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log_info "创建目录: $dir"
        fi
    done
    
    log_success "目录创建完成"
}

# 备份数据
backup_data() {
    if [ "$BACKUP" = true ]; then
        log_info "💾 备份现有数据..."
        
        BACKUP_DIR="${BACKUP_DIR:-./backups}"
        BACKUP_FILE="${BACKUP_DIR}/backup-${ENVIRONMENT}-$(date +%Y%m%d_%H%M%S).tar.gz"
        
        mkdir -p "$BACKUP_DIR"
        
        # 备份数据和配置
        tar -czf "$BACKUP_FILE" \
            --exclude='logs/*' \
            --exclude='temp/*' \
            data/ config/ 2>/dev/null || true
            
        log_success "数据备份完成: $BACKUP_FILE"
    fi
}

# 拉取镜像
pull_images() {
    if [ "$PULL_IMAGES" = true ] || [ "$ENVIRONMENT" = "production" ]; then
        log_info "📥 拉取最新镜像..."
        
        # 登录制品库
        if [ -n "$REGISTRY_USERNAME" ] && [ -n "$REGISTRY_PASSWORD" ]; then
            echo "$REGISTRY_PASSWORD" | docker login "$REGISTRY" -u "$REGISTRY_USERNAME" --password-stdin
        fi
        
        # 拉取镜像
        docker-compose -f "$DOCKER_COMPOSE_FILE" --env-file "$CONFIG_FILE" pull
        
        log_success "镜像拉取完成"
    fi
}

# 部署应用
deploy_application() {
    log_info "🚀 部署应用..."
    
    if [ "$DRY_RUN" = true ]; then
        log_warning "模拟运行模式，不执行实际部署"
        docker-compose -f "$DOCKER_COMPOSE_FILE" --env-file "$CONFIG_FILE" config
        return
    fi
    
    # 停止旧服务
    docker-compose -f "$DOCKER_COMPOSE_FILE" --env-file "$CONFIG_FILE" down || true
    
    # 启动服务
    docker-compose -f "$DOCKER_COMPOSE_FILE" --env-file "$CONFIG_FILE" up -d
    
    log_success "应用部署完成"
}

# 健康检查
health_check() {
    if [ "$DRY_RUN" = true ]; then
        return
    fi
    
    log_info "🏥 执行健康检查..."
    
    source "$CONFIG_FILE"
    
    local max_attempts=30
    local attempt=0
    
    # 等待服务启动
    sleep 10
    
    while [ $attempt -lt $max_attempts ]; do
        attempt=$((attempt + 1))
        
        # 检查后端健康状态
        if curl -f -s "http://localhost:${BACKEND_PORT:-8080}/health" > /dev/null; then
            log_success "后端服务健康检查通过"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            log_error "后端服务健康检查失败"
            log_info "查看服务日志:"
            docker-compose -f "$DOCKER_COMPOSE_FILE" logs backend
            exit 1
        fi
        
        log_info "等待后端服务启动... (${attempt}/${max_attempts})"
        sleep 10
    done
    
    # 检查前端服务
    if curl -f -s "http://localhost:${FRONTEND_PORT:-5173}/health" > /dev/null; then
        log_success "前端服务健康检查通过"
    else
        log_warning "前端服务健康检查失败，请检查服务状态"
    fi
}

# 显示部署状态
show_status() {
    log_info "📊 部署状态信息"
    
    source "$CONFIG_FILE"
    
    echo ""
    echo "🎯 访问地址:"
    echo "  前端: http://localhost:${FRONTEND_PORT:-5173}"
    echo "  后端: http://localhost:${BACKEND_PORT:-8080}"
    echo "  API文档: http://localhost:${BACKEND_PORT:-8080}/docs"
    echo "  健康检查: http://localhost:${BACKEND_PORT:-8080}/health"
    
    if [ "${PROMETHEUS_PORT:-}" ]; then
        echo "  监控: http://localhost:${PROMETHEUS_PORT}"
    fi
    
    if [ "${GRAFANA_PORT:-}" ]; then
        echo "  可视化: http://localhost:${GRAFANA_PORT}"
    fi
    
    echo ""
    echo "📋 服务状态:"
    docker-compose -f "$DOCKER_COMPOSE_FILE" ps
    
    echo ""
    echo "💡 常用命令:"
    echo "  查看日志: docker-compose -f $DOCKER_COMPOSE_FILE logs -f"
    echo "  重启服务: docker-compose -f $DOCKER_COMPOSE_FILE restart"
    echo "  停止服务: docker-compose -f $DOCKER_COMPOSE_FILE down"
}

# 清理函数
cleanup() {
    if [ $? -ne 0 ]; then
        log_error "部署失败"
        log_info "查看详细日志:"
        docker-compose -f "$DOCKER_COMPOSE_FILE" logs --tail=50
    fi
}

# 设置错误处理
trap cleanup EXIT

# 主函数
main() {
    log_info "🎯 部署目标: ${ENVIRONMENT} 环境"
    
    # 执行部署步骤
    check_prerequisites
    generate_compose_config
    
    if [ "$CONFIG_ONLY" = true ]; then
        log_success "配置文件生成完成，退出"
        exit 0
    fi
    
    create_directories
    backup_data
    pull_images
    deploy_application
    health_check
    show_status
    
    log_success "🎉 ${ENVIRONMENT} 环境部署完成!"
}

# 执行主函数
main