#!/bin/bash

# Configuration Management Script for BRZY Medical System
# 配置文件管理和验证工具

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../.." && pwd)"
DEPLOYMENT_DIR="${PROJECT_ROOT}/deployment"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

show_help() {
    cat << EOF
BRZY Medical System Configuration Manager

用法: $0 <命令> [选项] [参数]

命令:
    init <env>          初始化环境配置文件
    validate <env>      验证环境配置
    generate <env>      生成 Docker Compose 文件
    encrypt <file>      加密敏感配置文件
    decrypt <file>      解密配置文件
    diff <env1> <env2>  比较两个环境的配置差异
    template            显示配置模板
    backup              备份所有配置文件
    restore <file>      恢复配置文件

环境 (env):
    dev, staging, production

选项:
    --force             强制覆盖已存在的文件
    --interactive       交互式配置
    --validate-only     仅验证，不生成文件

示例:
    $0 init production --interactive    # 交互式初始化生产环境配置
    $0 validate staging                 # 验证测试环境配置
    $0 generate production              # 生成生产环境 Docker Compose 文件
    $0 diff dev production              # 比较开发和生产环境配置

EOF
}

# 初始化配置文件
init_config() {
    local env=$1
    local force=$2
    local interactive=$3
    
    log_info "📝 初始化 ${env} 环境配置..."
    
    local config_file="${DEPLOYMENT_DIR}/.env.${env}"
    
    if [ -f "$config_file" ] && [ "$force" != "--force" ]; then
        log_warning "配置文件已存在: $config_file"
        log_info "使用 --force 选项强制覆盖"
        return 1
    fi
    
    # 复制模板
    cp "${DEPLOYMENT_DIR}/templates/env.template" "$config_file"
    
    if [ "$interactive" = "--interactive" ]; then
        interactive_config "$config_file" "$env"
    else
        # 根据环境设置默认值
        case $env in
            dev)
                sed -i.bak 's/ENVIRONMENT=production/ENVIRONMENT=development/' "$config_file"
                sed -i.bak 's/DEBUG=false/DEBUG=true/' "$config_file"
                sed -i.bak 's/LOG_LEVEL=INFO/LOG_LEVEL=DEBUG/' "$config_file"
                ;;
            staging)
                sed -i.bak 's/ENVIRONMENT=production/ENVIRONMENT=staging/' "$config_file"
                sed -i.bak 's/DEBUG=false/DEBUG=true/' "$config_file"
                ;;
            production)
                # 生产环境保持默认安全配置
                ;;
        esac
        
        # 清理备份文件
        rm -f "${config_file}.bak"
    fi
    
    log_success "配置文件初始化完成: $config_file"
    log_warning "请编辑配置文件并填入实际值，特别是:"
    log_warning "  - 数据库密码"
    log_warning "  - JWT 密钥"
    log_warning "  - 制品库地址"
    log_warning "  - API 地址"
}

# 交互式配置
interactive_config() {
    local config_file=$1
    local env=$2
    
    log_info "🔧 交互式配置 ${env} 环境..."
    
    # 基础配置
    echo -n "项目名称 [brzy-medical]: "
    read project_name
    project_name=${project_name:-brzy-medical}
    sed -i.bak "s/PROJECT_NAME=brzy-medical/PROJECT_NAME=${project_name}/" "$config_file"
    
    # 制品库配置
    echo -n "制品库地址 [registry.example.com]: "
    read registry
    if [ -n "$registry" ]; then
        sed -i.bak "s/REGISTRY=registry.example.com/REGISTRY=${registry}/" "$config_file"
    fi
    
    echo -n "制品库命名空间 [brzy-medical]: "
    read namespace
    namespace=${namespace:-brzy-medical}
    sed -i.bak "s/REGISTRY_NAMESPACE=brzy-medical/REGISTRY_NAMESPACE=${namespace}/" "$config_file"
    
    # 数据库配置
    echo -n "数据库名称 [brzy_medical]: "
    read db_name
    db_name=${db_name:-brzy_medical}
    sed -i.bak "s/MYSQL_DATABASE=brzy_medical/MYSQL_DATABASE=${db_name}/" "$config_file"
    
    echo -n "数据库用户 [brzy_user]: "
    read db_user
    db_user=${db_user:-brzy_user}
    sed -i.bak "s/MYSQL_USER=brzy_user/MYSQL_USER=${db_user}/" "$config_file"
    
    # 生成随机密码
    echo -n "生成随机数据库密码? [y/N]: "
    read gen_db_pass
    if [[ $gen_db_pass =~ ^[Yy]$ ]]; then
        db_pass=$(openssl rand -base64 16)
        sed -i.bak "s/MYSQL_PASSWORD=your_secure_user_password_here/MYSQL_PASSWORD=${db_pass}/" "$config_file"
        log_info "数据库密码已生成: ${db_pass}"
    fi
    
    # JWT 密钥
    echo -n "生成随机 JWT 密钥? [y/N]: "
    read gen_jwt
    if [[ $gen_jwt =~ ^[Yy]$ ]]; then
        jwt_key=$(openssl rand -base64 32)
        sed -i.bak "s/JWT_SECRET_KEY=your_super_secure_jwt_secret_key_change_this_in_production/JWT_SECRET_KEY=${jwt_key}/" "$config_file"
        log_info "JWT 密钥已生成"
    fi
    
    # API 地址
    echo -n "必然中医 API 地址 [https://api.haoniuzhongyi.top:9090]: "
    read api_url
    if [ -n "$api_url" ]; then
        sed -i.bak "s|ORIGINAL_API_BASE_URL=https://api.haoniuzhongyi.top:9090|ORIGINAL_API_BASE_URL=${api_url}|" "$config_file"
    fi
    
    # 清理备份文件
    rm -f "${config_file}.bak"
    
    log_success "交互式配置完成"
}

# 验证配置
validate_config() {
    local env=$1
    local config_file="${DEPLOYMENT_DIR}/.env.${env}"
    
    log_info "🔍 验证 ${env} 环境配置..."
    
    if [ ! -f "$config_file" ]; then
        log_error "配置文件不存在: $config_file"
        return 1
    fi
    
    # 加载配置
    source "$config_file"
    
    local errors=0
    local warnings=0
    
    # 必需变量检查
    required_vars=(
        "PROJECT_NAME"
        "FRONTEND_IMAGE"
        "BACKEND_IMAGE"
        "MYSQL_ROOT_PASSWORD"
        "MYSQL_PASSWORD"
        "JWT_SECRET_KEY"
        "ORIGINAL_API_BASE_URL"
    )
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            log_error "必需变量未设置: $var"
            errors=$((errors + 1))
        fi
    done
    
    # 密码强度检查
    if [ ${#MYSQL_PASSWORD} -lt 8 ]; then
        log_warning "数据库密码长度不足 8 位"
        warnings=$((warnings + 1))
    fi
    
    if [ ${#JWT_SECRET_KEY} -lt 32 ]; then
        log_warning "JWT 密钥长度不足 32 位"
        warnings=$((warnings + 1))
    fi
    
    # 弱密码检查
    weak_patterns=("password" "123456" "admin" "test" "demo")
    for pattern in "${weak_patterns[@]}"; do
        if [[ "${MYSQL_PASSWORD,,}" == *"$pattern"* ]]; then
            log_warning "数据库密码包含弱模式: $pattern"
            warnings=$((warnings + 1))
        fi
    done
    
    # URL 格式检查
    if [[ ! "$ORIGINAL_API_BASE_URL" =~ ^https?:// ]]; then
        log_error "API URL 格式无效: $ORIGINAL_API_BASE_URL"
        errors=$((errors + 1))
    fi
    
    # 生产环境特殊检查
    if [ "$env" = "production" ]; then
        if [ "$DEBUG" = "true" ]; then
            log_warning "生产环境建议关闭 DEBUG 模式"
            warnings=$((warnings + 1))
        fi
        
        if [ "$LOG_LEVEL" = "DEBUG" ]; then
            log_warning "生产环境建议使用 INFO 或更高日志级别"
            warnings=$((warnings + 1))
        fi
    fi
    
    # 输出结果
    if [ $errors -eq 0 ] && [ $warnings -eq 0 ]; then
        log_success "配置验证通过"
        return 0
    elif [ $errors -eq 0 ]; then
        log_warning "配置验证通过，但有 $warnings 个警告"
        return 0
    else
        log_error "配置验证失败: $errors 个错误，$warnings 个警告"
        return 1
    fi
}

# 生成 Docker Compose 文件
generate_compose() {
    local env=$1
    local config_file="${DEPLOYMENT_DIR}/.env.${env}"
    local compose_file="${DEPLOYMENT_DIR}/docker-compose.${env}.yml"
    
    log_info "📦 生成 ${env} 环境 Docker Compose 文件..."
    
    if [ ! -f "$config_file" ]; then
        log_error "配置文件不存在: $config_file"
        return 1
    fi
    
    # 验证配置
    if ! validate_config "$env"; then
        log_error "配置验证失败，无法生成 Compose 文件"
        return 1
    fi
    
    # 加载环境变量并生成文件
    source "$config_file"
    envsubst < "${DEPLOYMENT_DIR}/templates/docker-compose.template.yml" > "$compose_file"
    
    log_success "Docker Compose 文件生成完成: $compose_file"
}

# 配置文件对比
diff_configs() {
    local env1=$1
    local env2=$2
    
    local config1="${DEPLOYMENT_DIR}/.env.${env1}"
    local config2="${DEPLOYMENT_DIR}/.env.${env2}"
    
    log_info "🔄 比较 ${env1} 和 ${env2} 环境配置..."
    
    if [ ! -f "$config1" ]; then
        log_error "配置文件不存在: $config1"
        return 1
    fi
    
    if [ ! -f "$config2" ]; then
        log_error "配置文件不存在: $config2"
        return 1
    fi
    
    # 使用 diff 比较配置文件
    if diff -u "$config1" "$config2"; then
        log_info "配置文件相同"
    else
        log_info "配置文件存在差异"
    fi
}

# 备份配置
backup_configs() {
    local backup_dir="${DEPLOYMENT_DIR}/backups"
    local backup_file="${backup_dir}/config-backup-$(date +%Y%m%d_%H%M%S).tar.gz"
    
    log_info "💾 备份配置文件..."
    
    mkdir -p "$backup_dir"
    
    tar -czf "$backup_file" -C "$DEPLOYMENT_DIR" \
        --exclude='backups' \
        --exclude='*.yml' \
        .env.* templates/ 2>/dev/null || true
    
    log_success "配置备份完成: $backup_file"
}

# 主函数
main() {
    local command=$1
    shift
    
    case $command in
        init)
            local env=$1
            shift
            init_config "$env" "$@"
            ;;
        validate)
            validate_config "$1"
            ;;
        generate)
            generate_compose "$1"
            ;;
        diff)
            diff_configs "$1" "$2"
            ;;
        backup)
            backup_configs
            ;;
        template)
            cat "${DEPLOYMENT_DIR}/templates/env.template"
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 参数检查
if [ $# -eq 0 ]; then
    show_help
    exit 1
fi

# 执行主函数
main "$@"