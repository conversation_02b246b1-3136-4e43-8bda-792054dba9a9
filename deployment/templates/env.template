# ===========================================
# 必然中医医案导出系统 - 部署环境配置模板
# ===========================================
# 
# 使用说明:
# 1. 复制此文件为 .env (生产环境) 或 .env.staging (测试环境)
# 2. 填入实际的配置值
# 3. 确保敏感信息的安全性
#

# ===========================================
# 项目基础配置
# ===========================================

PROJECT_NAME=brzy-medical
VERSION=1.0.0
ENVIRONMENT=production

# 应用信息
APP_NAME=必然中医 - 医案导出系统
DEBUG=false
LOG_LEVEL=INFO

# ===========================================
# 容器镜像配置
# ===========================================

# 制品库镜像地址 (请替换为实际的私有制品库地址)
REGISTRY=registry.example.com
REGISTRY_NAMESPACE=brzy-medical

# 镜像标签 (通常使用版本号或 Git SHA)
IMAGE_TAG=${VERSION}

# 完整镜像地址
FRONTEND_IMAGE=${REGISTRY}/${REGISTRY_NAMESPACE}/frontend:${IMAGE_TAG}
BACKEND_IMAGE=${REGISTRY}/${REGISTRY_NAMESPACE}/backend:${IMAGE_TAG}

# ===========================================
# 数据库配置
# ===========================================

# MySQL 配置
MYSQL_ROOT_PASSWORD=your_secure_root_password_here
MYSQL_DATABASE=brzy_medical
MYSQL_USER=brzy_user
MYSQL_PASSWORD=your_secure_user_password_here
MYSQL_PORT=3306

# 数据库连接池配置
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=10

# ===========================================
# Redis 配置 (可选)
# ===========================================

# Redis 缓存
REDIS_PASSWORD=your_secure_redis_password_here
REDIS_PORT=6379

# ===========================================
# 应用安全配置
# ===========================================

# JWT 密钥 (请使用强随机密钥)
JWT_SECRET_KEY=your_super_secure_jwt_secret_key_change_this_in_production

# 必然中医原始 API 地址
ORIGINAL_API_BASE_URL=https://api.haoniuzhongyi.top:9090

# CORS 允许的源 (多个用逗号分隔)
CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com

# ===========================================
# 前端配置
# ===========================================

# 前端访问地址
FRONTEND_API_URL=https://your-domain.com
FRONTEND_WS_URL=wss://your-domain.com

# 前端端口
FRONTEND_PORT=5173

# ===========================================
# 后端配置
# ===========================================

# 后端端口
BACKEND_PORT=8080

# Worker 进程数 (推荐: CPU 核心数 * 2)
BACKEND_WORKERS=4

# ===========================================
# 网络和代理配置
# ===========================================

# HTTP/HTTPS 端口
HTTP_PORT=80
HTTPS_PORT=443

# 网络子网
NETWORK_SUBNET=172.20.0.0/16

# ===========================================
# 存储路径配置
# ===========================================

# 数据目录 (绝对路径)
DATA_DIR=/opt/brzy_medical_system/data

# 日志目录
LOG_DIR=/opt/brzy_medical_system/logs

# 配置目录
CONFIG_DIR=/opt/brzy_medical_system/config

# SSL 证书目录 (HTTPS 部署)
SSL_DIR=/opt/brzy_medical_system/ssl

# ===========================================
# 监控配置 (可选)
# ===========================================

# Prometheus 监控
PROMETHEUS_PORT=9090

# Grafana 可视化
GRAFANA_PORT=3000
GRAFANA_PASSWORD=your_secure_grafana_password

# ===========================================
# 部署选项
# ===========================================

# 服务重启策略
RESTART_POLICY=unless-stopped

# 健康检查配置
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=3

# ===========================================
# 高级配置
# ===========================================

# 容器资源限制
BACKEND_MEMORY_LIMIT=1g
FRONTEND_MEMORY_LIMIT=512m
MYSQL_MEMORY_LIMIT=2g

# 备份配置
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION=7

# ===========================================
# 开发和调试选项 (生产环境设为 false)
# ===========================================

# 启用调试模式
ENABLE_DEBUG=false

# 启用性能分析
ENABLE_PROFILING=false

# 启用详细日志
VERBOSE_LOGGING=false

# ===========================================
# Webhook 自动部署配置 (可选)
# ===========================================

# Webhook 服务端口
WEBHOOK_PORT=8090

# Webhook 验证密钥 (GitHub/GitLab webhook secret)
WEBHOOK_SECRET=your_webhook_secret_here

# 自动部署分支 (推送到此分支时触发部署)
AUTO_DEPLOY_BRANCH=release

# 部署环境 (development/staging/production)
DEPLOY_ENVIRONMENT=production

# ===========================================
# 安全提醒
# ===========================================
#
# 1. 数据库密码: 使用强密码，包含大小写字母、数字和特殊字符
# 2. JWT 密钥: 至少 32 位随机字符串
# 3. Redis 密码: 高强度密码
# 4. 文件权限: 确保 .env 文件权限为 600 (仅所有者可读写)
# 5. 定期轮换: 定期更新密钥和密码
# 6. 备份安全: 确保备份文件的安全存储
#
# 生成强密钥示例命令:
# openssl rand -base64 32  # JWT 密钥
# openssl rand -hex 16     # 数据库密码
#
# ===========================================