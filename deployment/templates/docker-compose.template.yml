# Docker Compose Template for CNB Artifacts Deployment
# 使用环境变量注入配置，支持多环境部署

version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: ${PROJECT_NAME:-brzy}-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-brzy_medical}
      MYSQL_USER: ${MYSQL_USER:-brzy_user}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    volumes:
      - mysql_data:/var/lib/mysql
      - ${CONFIG_DIR:-./config}/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "${MYSQL_PORT:-3306}:3306"
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p$$MYSQL_ROOT_PASSWORD"]
      timeout: 20s
      retries: 10
      interval: 30s

  # Redis Cache (Optional)
  redis:
    image: redis:7-alpine
    container_name: ${PROJECT_NAME:-brzy}-redis
    restart: unless-stopped
    command: redis-server --appendonly yes ${REDIS_PASSWORD:+--requirepass $REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend Service (CNB Artifact)
  backend:
    image: ${BACKEND_IMAGE}
    container_name: ${PROJECT_NAME:-brzy}-backend
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      # 数据库配置
      DATABASE_URL: mysql+pymysql://${MYSQL_USER:-brzy_user}:${MYSQL_PASSWORD}@mysql:3306/${MYSQL_DATABASE:-brzy_medical}
      
      # Redis 配置
      REDIS_URL: redis://${REDIS_PASSWORD:+:$REDIS_PASSWORD@}redis:6379/0
      
      # 应用配置
      APP_NAME: ${APP_NAME:-BRZY Medical Export}
      APP_VERSION: ${VERSION:-1.0.0}
      DEBUG: ${DEBUG:-false}
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
      
      # 安全配置
      JWT_SECRET_KEY: ${JWT_SECRET_KEY}
      
      # 业务配置
      ORIGINAL_API_BASE_URL: ${ORIGINAL_API_BASE_URL}
      
      # CORS 配置
      CORS_ORIGINS: ${CORS_ORIGINS:-http://localhost:5173,https://localhost:5173}
      
      # 运行时配置
      PORT: 8080
      WORKERS: ${BACKEND_WORKERS:-4}
      
      # Python 优化
      PYTHONDONTWRITEBYTECODE: 1
      PYTHONUNBUFFERED: 1
      PYTHONPATH: /workspace
    volumes:
      - ${DATA_DIR:-./data}/uploads:/workspace/uploads
      - ${DATA_DIR:-./data}/generated_pdfs:/workspace/generated_pdfs
      - ${DATA_DIR:-./data}/temp:/workspace/temp
      - ${LOG_DIR:-./logs}/backend:/workspace/logs
      - ${CONFIG_DIR:-./config}/fonts:/workspace/fonts:ro
    ports:
      - "${BACKEND_PORT:-8080}:8080"
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "/workspace/scripts/health-check.sh", "check"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Frontend Service (CNB Artifact)
  frontend:
    image: ${FRONTEND_IMAGE}
    container_name: ${PROJECT_NAME:-brzy}-frontend
    restart: unless-stopped
    depends_on:
      backend:
        condition: service_healthy
    environment:
      # 前端运行时配置
      VITE_API_URL: ${FRONTEND_API_URL:-http://localhost:8080}
      VITE_WS_URL: ${FRONTEND_WS_URL:-ws://localhost:8080}
      VITE_APP_TITLE: ${APP_NAME:-必然中医 - 医案导出系统}
      VITE_APP_VERSION: ${VERSION:-1.0.0}
      
      # Nginx 配置
      PORT: 5173
      NODE_ENV: production
    volumes:
      - ${LOG_DIR:-./logs}/frontend:/var/log/nginx
      - ${CONFIG_DIR:-./config}/nginx:/workspace/config/nginx:ro
    ports:
      - "${FRONTEND_PORT:-5173}:5173"
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5173/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Nginx Reverse Proxy (Production)
  nginx:
    image: nginx:alpine
    container_name: ${PROJECT_NAME:-brzy}-nginx
    restart: unless-stopped
    depends_on:
      - frontend
      - backend
    environment:
      FRONTEND_HOST: frontend
      FRONTEND_PORT: 5173
      BACKEND_HOST: backend
      BACKEND_PORT: 8080
    volumes:
      - ${CONFIG_DIR:-./config}/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ${CONFIG_DIR:-./config}/nginx/conf.d:/etc/nginx/conf.d:ro
      - ${LOG_DIR:-./logs}/nginx:/var/log/nginx
      - ${SSL_DIR:-./ssl}:/etc/nginx/ssl:ro
    ports:
      - "${HTTP_PORT:-80}:80"
      - "${HTTPS_PORT:-443}:443"
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring (Optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: ${PROJECT_NAME:-brzy}-prometheus
    restart: unless-stopped
    volumes:
      - ${CONFIG_DIR:-./config}/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    networks:
      - app-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    profiles:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: ${PROJECT_NAME:-brzy}-grafana
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ${CONFIG_DIR:-./config}/grafana:/etc/grafana/provisioning:ro
    ports:
      - "${GRAFANA_PORT:-3000}:3000"
    networks:
      - app-network
    profiles:
      - monitoring

# 数据卷定义
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

# 网络定义
networks:
  app-network:
    driver: bridge
    ipam:
      config:
        - subnet: ${NETWORK_SUBNET:-**********/16}