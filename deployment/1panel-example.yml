# ==============================================
# 1Panel 部署配置示例
# ==============================================
# 
# 这是一个 1Panel 兼容的 Docker Compose 配置示例
# 用于从 CNB 制品库拉取镜像并部署应用
#

version: '3.8'

services:
  # ===============================================
  # MySQL 数据库服务
  # ===============================================
  mysql:
    image: mysql:8.0
    container_name: brzy-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: brzy_medical
      MYSQL_USER: brzy_user
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"
    networks:
      - brzy-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p$$MYSQL_ROOT_PASSWORD"]
      timeout: 20s
      retries: 10

  # ===============================================
  # Redis 缓存服务
  # ===============================================
  redis:
    image: redis:7-alpine
    container_name: brzy-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - brzy-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ===============================================
  # 后端服务 (CNB 制品)
  # ===============================================
  backend:
    # 从 CNB 制品库拉取镜像
    image: docker.cnb.cool/${CNB_NAMESPACE}/brzy-medical-backend:${IMAGE_TAG:-latest}
    container_name: brzy-backend
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      # 数据库配置
      DATABASE_URL: mysql+pymysql://brzy_user:${MYSQL_PASSWORD}@mysql:3306/brzy_medical
      
      # Redis 配置
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379/0
      
      # 应用配置
      JWT_SECRET_KEY: ${JWT_SECRET_KEY}
      ORIGINAL_API_BASE_URL: ${ORIGINAL_API_BASE_URL}
      
      # 环境设置
      PYTHON_ENV: production
      LOG_LEVEL: INFO
      
      # 后端工作进程数
      BACKEND_WORKERS: ${BACKEND_WORKERS:-4}
    volumes:
      - uploads_data:/app/uploads
      - generated_pdfs:/app/generated_pdfs
      - logs_backend:/app/logs
    ports:
      - "8080:8080"
    networks:
      - brzy-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # ===============================================
  # 前端服务 (CNB 制品)
  # ===============================================
  frontend:
    # 从 CNB 制品库拉取镜像
    image: docker.cnb.cool/${CNB_NAMESPACE}/brzy-medical-frontend:${IMAGE_TAG:-latest}
    container_name: brzy-frontend
    restart: unless-stopped
    depends_on:
      backend:
        condition: service_healthy
    environment:
      # 前端配置 (通过 Nginx 环境变量模板注入)
      FRONTEND_API_URL: ${FRONTEND_API_URL:-http://localhost:8080}
      FRONTEND_WS_URL: ${FRONTEND_WS_URL:-ws://localhost:8080}
      FRONTEND_APP_TITLE: ${FRONTEND_APP_TITLE:-必然中医医案导出系统}
    volumes:
      - logs_frontend:/var/log/nginx
    ports:
      - "80:80"
    networks:
      - brzy-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

# ===============================================
# 数据卷定义
# ===============================================
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  uploads_data:
    driver: local
  generated_pdfs:
    driver: local
  logs_backend:
    driver: local
  logs_frontend:
    driver: local

# ===============================================
# 网络定义
# ===============================================
networks:
  brzy-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# ==============================================
# 1Panel 环境变量配置说明
# ==============================================
# 
# 在 1Panel 中需要配置以下环境变量：
# 
# 必需变量：
# - CNB_NAMESPACE: CNB 制品库命名空间 (例如: your-namespace)
# - IMAGE_TAG: 镜像标签 (例如: latest, release-abc123)
# - MYSQL_ROOT_PASSWORD: MySQL root 密码
# - MYSQL_PASSWORD: MySQL 用户密码
# - REDIS_PASSWORD: Redis 访问密码
# - JWT_SECRET_KEY: JWT 签名密钥 (至少32位)
# - ORIGINAL_API_BASE_URL: 必然中医原始API地址
# 
# 可选变量：
# - BACKEND_WORKERS: 后端工作进程数 (默认: 4)
# - FRONTEND_API_URL: 前端API地址 (默认: http://localhost:8080)
# - FRONTEND_WS_URL: WebSocket地址 (默认: ws://localhost:8080)
# - FRONTEND_APP_TITLE: 应用标题
# 
# 部署步骤：
# 1. 在 1Panel 中创建新应用
# 2. 复制此配置文件到 docker-compose.yml
# 3. 配置上述环境变量
# 4. 点击部署按钮启动应用
# 5. 访问应用: http://your-server-ip
#