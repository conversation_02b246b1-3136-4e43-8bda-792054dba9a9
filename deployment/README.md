# 🚀 CNB 私有制品库部署指南

必然中医医案导出系统的云原生部署方案，使用 Cloud Native Buildpacks (CNB) 构建制品并部署到私有环境。

## 📋 方案概述

### 核心特性
- ✅ **配置外置化**: 构建时不包含环境配置，运行时注入
- ✅ **制品库集成**: 支持私有 Harbor/Nexus/云厂商制品库
- ✅ **多环境支持**: 统一制品，不同环境配置
- ✅ **自动化部署**: 完整的 CI/CD 流程
- ✅ **安全加固**: 密钥管理、权限控制、审计日志

### 部署架构
```
GitHub/GitLab → CNB Build → Private Registry → Target Environment
     ↓              ↓              ↓              ↓
   源代码         构建制品        私有仓库        运行环境
```

## 🛠️ 快速开始

### 1. 环境准备

**系统要求:**
- Ubuntu 20.04+ / CentOS 8+
- Docker Engine 20.10+
- Docker Compose 2.0+
- 至少 4GB RAM, 20GB 磁盘空间

**安装依赖:**
```bash
# Docker 和 Docker Compose
curl -fsSL https://get.docker.com | sh
sudo pip3 install docker-compose

# 配置管理工具
sudo apt-get install gettext-base  # Ubuntu/Debian
sudo yum install gettext           # CentOS/RHEL
```

### 2. 配置初始化

```bash
# 进入部署目录
cd deployment

# 初始化环境配置 (选择: dev/staging/production)
./scripts/config-manager.sh init production --interactive

# 验证配置
./scripts/config-manager.sh validate production

# 生成 Docker Compose 文件
./scripts/config-manager.sh generate production
```

### 3. 一键部署

```bash
# 部署到生产环境
./scripts/deploy.sh production --backup

# 检查部署状态
docker-compose -f docker-compose.production.yml ps

# 查看服务日志
docker-compose -f docker-compose.production.yml logs -f
```

## 📁 目录结构

```
deployment/
├── scripts/
│   ├── deploy.sh              # 主部署脚本
│   ├── config-manager.sh      # 配置管理工具
│   └── ...
├── templates/
│   ├── docker-compose.template.yml  # Docker Compose 模板
│   └── env.template                  # 环境变量模板
├── examples/
│   ├── .env.dev               # 开发环境配置示例
│   ├── .env.staging           # 测试环境配置示例
│   └── .env.production        # 生产环境配置示例
└── README.md                  # 本文档
```

## 🔧 配置管理

### 环境配置文件

每个环境使用独立的配置文件:
- `.env.dev` - 开发环境
- `.env.staging` - 测试环境  
- `.env.production` - 生产环境

### 配置管理命令

```bash
# 初始化新环境配置
./scripts/config-manager.sh init <env> [--interactive]

# 验证配置完整性
./scripts/config-manager.sh validate <env>

# 比较环境配置差异
./scripts/config-manager.sh diff dev production

# 备份配置文件
./scripts/config-manager.sh backup

# 查看配置模板
./scripts/config-manager.sh template
```

## 🏗️ 制品构建流程

### GitHub Actions 集成

推送到 `release` 分支会自动触发构建:

```yaml
# .github/workflows/build-artifacts.yml
name: Build CNB Artifacts
on:
  push:
    branches: [ release ]
```

**构建产物:**
- 前端镜像: `registry/namespace/frontend:version`
- 后端镜像: `registry/namespace/backend:version`
- 部署清单: `deployment-manifests-version.tar.gz`

### 本地构建测试

```bash
# 使用本地构建脚本
./scripts/cnb_local_build.sh build

# 启动本地测试环境
./scripts/cnb_local_build.sh start

# 健康检查
./scripts/cnb_local_build.sh health
```

## 🔐 安全配置

### 密钥管理

**必需的安全配置:**
```bash
# 生成强密钥
openssl rand -base64 32  # JWT 密钥
openssl rand -hex 16     # 数据库密码

# 设置文件权限
chmod 600 .env.*
```

**环境变量安全:**
- `JWT_SECRET_KEY`: 至少 32 位随机字符串
- `MYSQL_ROOT_PASSWORD`: 强密码，包含大小写字母、数字、特殊字符
- `MYSQL_PASSWORD`: 高强度用户密码
- `REDIS_PASSWORD`: Redis 访问密码

### 制品库认证

**GitHub Secrets 配置:**
```
REGISTRY_USERNAME    # 制品库用户名
REGISTRY_PASSWORD    # 制品库密码
MYSQL_ROOT_PASSWORD  # 数据库 root 密码
MYSQL_PASSWORD       # 数据库用户密码
JWT_SECRET_KEY       # JWT 签名密钥
```

## 🌍 多环境部署

### 开发环境 (dev)

**特点:**
- 启用调试模式
- 详细日志输出
- 热重载支持
- 本地镜像构建

**部署命令:**
```bash
./scripts/deploy.sh dev --pull-images
```

### 测试环境 (staging)

**特点:**
- 生产级配置
- 性能测试支持
- 完整监控
- 定期备份

**部署命令:**
```bash
./scripts/deploy.sh staging --backup
```

### 生产环境 (production)

**特点:**
- 最高安全级别
- 性能优化配置
- 高可用部署
- 灾难恢复

**部署命令:**
```bash
./scripts/deploy.sh production --backup
```

## 📊 监控和运维

### 服务监控

**健康检查端点:**
- 前端: `http://localhost:5173/health`
- 后端: `http://localhost:8080/health`
- 指标: `http://localhost:8080/metrics`

**监控工具 (可选):**
- Prometheus: `http://localhost:9090`
- Grafana: `http://localhost:3000`

### 日志管理

**日志位置:**
```
logs/
├── backend/           # 后端应用日志
├── frontend/          # 前端访问日志
└── nginx/            # 反向代理日志
```

**日志查看:**
```bash
# 实时日志
docker-compose -f docker-compose.production.yml logs -f

# 特定服务日志
docker-compose -f docker-compose.production.yml logs backend

# 历史日志
tail -f logs/backend/app.log
```

### 备份和恢复

**自动备份:**
```bash
# 启用自动备份 (在 .env 中配置)
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # 每天凌晨2点
BACKUP_RETENTION=30        # 保留30天
```

**手动备份:**
```bash
# 备份数据
./scripts/deploy.sh production --backup

# 恢复数据
docker-compose -f docker-compose.production.yml down
# 恢复数据卷
docker-compose -f docker-compose.production.yml up -d
```

## 🚨 故障排查

### 常见问题

**1. 配置验证失败**
```bash
# 检查配置
./scripts/config-manager.sh validate production

# 查看详细错误
./scripts/config-manager.sh validate production --verbose
```

**2. 镜像拉取失败**
```bash
# 检查制品库认证
docker login registry.your-company.com

# 手动拉取测试
docker pull registry.your-company.com/brzy-medical/backend:v1.0.0
```

**3. 服务启动失败**
```bash
# 查看容器状态
docker-compose -f docker-compose.production.yml ps

# 查看启动日志
docker-compose -f docker-compose.production.yml logs backend

# 进入容器调试
docker-compose -f docker-compose.production.yml exec backend bash
```

**4. 健康检查失败**
```bash
# 手动健康检查
curl -f http://localhost:8080/health

# 查看详细健康状态
curl -s http://localhost:8080/health | jq .

# 容器内健康检查
docker exec brzy-backend /workspace/scripts/health-check.sh detailed
```

### 性能优化

**1. 数据库优化**
```sql
-- 查看数据库性能
SHOW PROCESSLIST;
SHOW ENGINE INNODB STATUS;

-- 优化查询
EXPLAIN SELECT * FROM medical_records WHERE ...;
```

**2. 应用性能**
```bash
# 查看应用指标
curl http://localhost:8080/metrics

# 容器资源使用
docker stats
```

**3. 网络优化**
```bash
# 检查网络延迟
docker-compose exec backend ping mysql
docker-compose exec backend ping redis
```

## 📋 运维检查清单

### 部署前检查

- [ ] 配置文件验证通过
- [ ] 制品库镜像可访问
- [ ] 数据库连接正常
- [ ] 必要目录创建完成
- [ ] 备份策略确认
- [ ] 监控告警配置
- [ ] SSL 证书有效

### 部署后检查

- [ ] 所有服务正常启动
- [ ] 健康检查通过
- [ ] 前端页面可访问
- [ ] API 接口正常
- [ ] WebSocket 连接正常
- [ ] 数据库连接稳定
- [ ] 日志输出正常
- [ ] 监控数据收集

### 日常运维

- [ ] 定期检查服务状态
- [ ] 监控资源使用情况
- [ ] 审查安全日志
- [ ] 验证备份完整性
- [ ] 更新安全补丁
- [ ] 密钥轮换计划

## 📚 参考文档

- [CNB 官方文档](https://buildpacks.io/)
- [Docker Compose 参考](https://docs.docker.com/compose/)
- [必然中医项目文档](../docs/)
- [故障排查指南](../docs/troubleshooting/)

## 🆘 技术支持

如遇问题请联系:
- 📧 技术支持: <EMAIL>
- 🐛 Bug 报告: GitHub Issues
- 📖 文档建议: 项目 Wiki

---

> 💡 **提示**: 首次部署建议在测试环境验证完整流程后再部署到生产环境。