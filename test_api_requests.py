#!/usr/bin/env python3
"""
测试 API 请求格式的脚本
用于验证后端的请求参数是否与 iOS 一致
"""

import requests
import json
from urllib.parse import urlencode

# API 基础配置
BASE_URL = "https://api.haoniuzhongyi.top:9090/easydoctorv2-ws/apiController"

# 测试配置
TEST_CONFIG = {
    "userId": "0000000025073",
    "sessionToken": "1748765765876-BEPuIUTkoNZXqCEK",
    "patientId": "9000000087735",
    "caseId": "some_case_id"  # 需要一个真实的医案ID
}

def test_api_request(method_code, extra_params=None):
    """测试单个 API 请求"""
    
    # 构建基础参数（模拟 iOS 请求）
    params = {
        "method_code": method_code,
        "userId": TEST_CONFIG["userId"],
        "sessionToken": TEST_CONFIG["sessionToken"],
        "app_version": "4.6.3",
        "imei": "60BA14A4-58A0-4E3F-831C-F4B0F62EB8D4",  # 完整的 UUID
        "mac": "0F535656-8B87-4A08-825E-6654D2F32344",   # 完整的 UUID
        "sys_type": "2",
        "userType": "1",
        "terminalType": "biran",
        "doctorId": TEST_CONFIG["userId"],
        "tel": "88025668998"
    }
    
    # 添加额外参数
    if extra_params:
        params.update(extra_params)
    
    # 构建 URL（GET 请求）
    url = f"{BASE_URL}?{urlencode(params)}"
    
    print(f"\n{'='*80}")
    print(f"Testing API: {method_code}")
    print(f"{'='*80}")
    print(f"URL: {url}")
    print(f"\nParameters:")
    print(json.dumps(params, indent=2, ensure_ascii=False))
    
    try:
        # 发送 GET 请求
        headers = {
            "User-Agent": "iOS-Test/1.0"
        }
        response = requests.get(BASE_URL, params=params, headers=headers)
        
        print(f"\nResponse Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        # 解析响应
        try:
            data = response.json()
            print(f"\nResponse Data:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 判断是否成功
            if data.get("code") == "0000":
                print(f"\n✅ SUCCESS: {method_code} API call successful")
            else:
                print(f"\n❌ FAILED: {method_code} - {data.get('errorMsg', 'Unknown error')}")
                
        except json.JSONDecodeError:
            print(f"\nResponse Text: {response.text}")
            print(f"\n❌ FAILED: Invalid JSON response")
            
    except Exception as e:
        print(f"\n❌ ERROR: {e}")

def main():
    """运行所有测试"""
    
    print("API Request Format Test")
    print("Testing backend compatibility with iOS requests")
    
    # 测试1：患者详情（应该成功）
    test_api_request("000116", {
        "patientId": TEST_CONFIG["patientId"]
    })
    
    # 测试2：处方列表（可能失败，如果设备ID被截断）
    test_api_request("000117", {
        "patientId": TEST_CONFIG["patientId"],
        "page": "1",
        "pageSize": "20"
    })
    
    # 测试3：医案列表（新接口）
    test_api_request("000233", {
        "pt_id": TEST_CONFIG["patientId"]
    })
    
    # 测试4：医案详情（新接口）
    test_api_request("000228", {
        "case_id": TEST_CONFIG["caseId"]
    })
    
    # 测试5：使用被截断的设备ID
    print(f"\n\n{'#'*80}")
    print("Testing with truncated device IDs")
    print(f"{'#'*80}")
    
    params = {
        "method_code": "000117",
        "userId": TEST_CONFIG["userId"],
        "sessionToken": TEST_CONFIG["sessionToken"],
        "app_version": "4.6.3",
        "imei": "60BA14A4-58A0-4E",  # 被截断的 UUID
        "mac": "60BA14A4-58A0-4E",   # 被截断的 UUID
        "sys_type": "2",
        "userType": "1",
        "terminalType": "biran",
        "doctorId": TEST_CONFIG["userId"],
        "tel": "88025668998",
        "patientId": TEST_CONFIG["patientId"],
        "page": "1",
        "pageSize": "20"
    }
    
    print("\nTesting 000117 with truncated device IDs:")
    print(f"imei: {params['imei']} (length: {len(params['imei'])})")
    print(f"mac: {params['mac']} (length: {len(params['mac'])})")
    
    try:
        response = requests.get(BASE_URL, params=params)
        data = response.json()
        print(f"\nResponse: {json.dumps(data, indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"\nError: {e}")

if __name__ == "__main__":
    main()