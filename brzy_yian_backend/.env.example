# ===========================================
# 必然中医医案导出系统 - 后端环境配置模板
# ===========================================
# 
# 使用说明:
# 1. 复制此文件为 .env
# 2. 填入实际的配置值
# 3. 确保敏感信息的安全性
#

# ===========================================
# 应用基础配置
# ===========================================

# 应用信息
APP_NAME=必然中医 - 医案导出系统
APP_VERSION=1.0.0
DEBUG=true
LOG_LEVEL=DEBUG

# 安全密钥 (启动脚本会自动生成随机密钥)
SECRET_KEY=your-secret-key-here-change-this-in-production
JWT_SECRET_KEY=your-jwt-secret-key-change-this-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# ===========================================
# 数据库配置 - MySQL 8.0
# ===========================================

# MySQL 连接信息
DATABASE_URL=mysql+pymysql://brzy_user:brzy_pass_123@localhost:3306/brzy_medical

# 数据库连接池配置
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=10

# ===========================================
# Redis 配置 - Redis 7
# ===========================================

# Redis 连接信息
REDIS_URL=redis://:redis_pass_123@localhost:6379/0
REDIS_PASSWORD=redis_pass_123

# ===========================================
# 必然中医原始 API 配置
# ===========================================

# 原始 API 地址
ORIGINAL_API_URL=https://api.haoniuzhongyi.top:9090

# API 超时设置
ORIGINAL_API_TIMEOUT=30

# ===========================================
# CORS 跨域配置
# ===========================================

# 允许的域名 (开发环境)
CORS_ORIGINS=http://localhost:5173,http://127.0.0.1:5173

# ===========================================
# 文件存储配置
# ===========================================

# 上传目录
UPLOAD_DIR=./uploads
MAX_UPLOAD_SIZE=10485760

# PDF 生成配置
PDF_FONT_DIR=./fonts
PDF_TEMP_DIR=./temp

# ===========================================
# 日志配置
# ===========================================

# 日志文件路径
LOG_FILE=../logs/backend/app.log

# ===========================================
# WebSocket 配置
# ===========================================

# WebSocket 心跳间隔 (秒)
WS_HEARTBEAT_INTERVAL=30

# ===========================================
# 其他配置
# ===========================================

# 允许的主机
ALLOWED_HOSTS=*
