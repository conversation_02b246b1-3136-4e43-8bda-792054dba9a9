"""
Authentication service for managing auth sessions and tokens
"""
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import secrets
import json
import logging
from jose import jwt, JW<PERSON>rror
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from app.config import settings
from app.models.auth import AuthSession, AuthStatus
from app.schemas.auth import ScanAuthRequest, DeviceInfo
from app.services.cache_service import CacheService
from app.utils.security import token_encryption, generate_session_id

logger = logging.getLogger(__name__)


class AuthService:
    """Authentication service class"""
    
    def __init__(self, db: Session, cache_service: Optional[CacheService] = None):
        self.db = db
        self.cache = cache_service or CacheService()
    
    async def create_auth_session(
        self,
        auth_code: str,
        session_token: str,
        user_id: str,
        device_info: Optional[Dict[str, Any]] = None,
        current_patient_id: Optional[str] = None,
        patient_name: Optional[str] = None,
        ip_address: Optional[str] = None
    ) -> AuthSession:
        """
        Create a new authentication session
        
        Args:
            auth_code: Authorization code from iOS app
            session_token: Original session token
            user_id: User ID
            device_info: Device information
            current_patient_id: Current patient ID if in chat
            patient_name: Current patient name
            ip_address: Client IP address
            
        Returns:
            Auth session object
        """
        try:
            # Check if auth code already exists
            existing = self.db.query(AuthSession).filter(
                AuthSession.auth_code == auth_code
            ).first()
            
            if existing:
                if existing.status == AuthStatus.PENDING:
                    # Generate session ID for web
                    session_id = generate_session_id()
                    
                    # Encrypt the session token
                    encrypted_token = token_encryption.encrypt_token(session_token)
                    
                    # Update existing pending session
                    expires_at = datetime.utcnow() + timedelta(
                        minutes=settings.jwt_access_token_expire_minutes
                    )
                    
                    existing.session_id = session_id
                    existing.session_token = encrypted_token
                    existing.expires_at = expires_at
                    existing.status = AuthStatus.AUTHORIZED
                    existing.authorized_at = datetime.utcnow()
                    existing.device_info = json.dumps(device_info) if device_info else None
                    existing.current_patient_id = current_patient_id
                    existing.patient_name = patient_name
                    existing.ip_address = ip_address
                    
                    self.db.commit()
                    
                    # Cache session info with session_id, not auth_code
                    await self._cache_session_info(session_id, user_id, encrypted_token)
                    
                    return existing
                else:
                    raise ValueError("Auth code already used")
            
            # Generate session ID for web
            session_id = generate_session_id()
            
            # Encrypt the session token
            encrypted_token = token_encryption.encrypt_token(session_token)
            
            # Create session expires time
            expires_at = datetime.utcnow() + timedelta(
                minutes=settings.jwt_access_token_expire_minutes
            )
            
            # Create new session
            auth_session = AuthSession(
                auth_code=auth_code,
                session_id=session_id,
                user_id=user_id,
                session_token=encrypted_token,
                expires_at=expires_at,
                status=AuthStatus.AUTHORIZED,
                device_info=json.dumps(device_info) if device_info else None,
                current_patient_id=current_patient_id,
                patient_name=patient_name,
                ip_address=ip_address,
                authorized_at=datetime.utcnow()
            )
            
            self.db.add(auth_session)
            self.db.commit()
            
            # Cache session info with session_id
            await self._cache_session_info(session_id, user_id, encrypted_token)
            
            logger.info(f"Created auth session for user {user_id}")
            return auth_session
            
        except IntegrityError as e:
            self.db.rollback()
            logger.error(f"Database integrity error: {e}")
            raise ValueError("Failed to create auth session")
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error creating auth session: {e}")
            raise
    
    async def get_session_by_session_id(self, session_id: str) -> Optional[AuthSession]:
        """Get session by session ID"""
        # Try cache first
        cached = await self.cache.get(f"session:{session_id}")
        if cached:
            user_data = json.loads(cached)
            # Get full session from database
            session = self.db.query(AuthSession).filter(
                AuthSession.session_id == session_id,
                AuthSession.status == AuthStatus.AUTHORIZED
            ).first()
            
            if session and session.is_valid():
                # Update last accessed time
                session.last_accessed_at = datetime.utcnow()
                self.db.commit()
                return session
        
        # Not in cache, check database
        session = self.db.query(AuthSession).filter(
            AuthSession.session_id == session_id
        ).first()
        
        if session and session.is_valid():
            # Update last accessed time
            session.last_accessed_at = datetime.utcnow()
            self.db.commit()
            
            # Re-cache
            await self._cache_session_info(
                session_id,
                session.user_id,
                session.session_token
            )
            return session
        
        return None
    
    async def get_session_by_auth_code(self, auth_code: str) -> Optional[AuthSession]:
        """Get session by authorization code"""
        return self.db.query(AuthSession).filter(
            AuthSession.auth_code == auth_code
        ).first()
    
    async def get_session_by_token(self, token: str) -> Optional[AuthSession]:
        """Get session by temporary token (session_id)"""
        # The token passed from frontend is actually the session_id
        return await self.get_session_by_session_id(token)
    
    async def update_session_status(
        self,
        auth_code: str,
        status: AuthStatus,
        web_user_agent: Optional[str] = None
    ) -> bool:
        """Update session status"""
        session = await self.get_session_by_auth_code(auth_code)
        if not session:
            return False
        
        session.status = status
        if web_user_agent:
            session.web_user_agent = web_user_agent
        
        if status == AuthStatus.EXPIRED:
            # Remove from cache
            if session.temp_token:
                await self.cache.delete(f"auth:{session.temp_token}")
        
        self.db.commit()
        return True
    
    async def refresh_token(self, current_token: str) -> Optional[str]:
        """Refresh temporary token"""
        session = await self.get_session_by_token(current_token)
        if not session:
            return None
        
        # Generate new token
        new_token = self._generate_temp_token()
        new_expires = datetime.utcnow() + timedelta(
            minutes=settings.jwt_access_token_expire_minutes
        )
        
        # Update session
        session.temp_token = new_token
        session.expires_at = new_expires
        session.last_accessed_at = datetime.utcnow()
        self.db.commit()
        
        # Update cache
        await self.cache.delete(f"auth:{current_token}")
        await self._cache_session_info(
            new_token,
            session.user_id,
            session.session_token
        )
        
        logger.info(f"Refreshed token for user {session.user_id}")
        return new_token
    
    async def invalidate_token(self, temp_token: str) -> bool:
        """Invalidate a temporary token"""
        session = await self.get_session_by_token(temp_token)
        if not session:
            return False
        
        session.status = AuthStatus.EXPIRED
        session.expires_at = datetime.utcnow()
        self.db.commit()
        
        # Remove from cache using session_id
        await self.cache.delete(f"session:{temp_token}")
        
        logger.info(f"Invalidated token for user {session.user_id}")
        return True
    
    async def cleanup_expired_sessions(self) -> int:
        """Clean up expired sessions"""
        try:
            # Find expired sessions
            expired_sessions = self.db.query(AuthSession).filter(
                AuthSession.expires_at < datetime.utcnow(),
                AuthSession.status != AuthStatus.EXPIRED
            ).all()
            
            count = len(expired_sessions)
            for session in expired_sessions:
                session.status = AuthStatus.EXPIRED
                if session.temp_token:
                    await self.cache.delete(f"auth:{session.temp_token}")
            
            self.db.commit()
            logger.info(f"Cleaned up {count} expired sessions")
            return count
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error cleaning up sessions: {e}")
            return 0
    
    def create_jwt_token(self, data: dict) -> str:
        """Create JWT token"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(
            minutes=settings.jwt_access_token_expire_minutes
        )
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(
            to_encode,
            settings.jwt_secret_key,
            algorithm=settings.jwt_algorithm
        )
        return encoded_jwt
    
    def verify_jwt_token(self, token: str) -> Optional[dict]:
        """Verify JWT token"""
        try:
            payload = jwt.decode(
                token,
                settings.jwt_secret_key,
                algorithms=[settings.jwt_algorithm]
            )
            return payload
        except JWTError:
            return None
    
    def _generate_temp_token(self) -> str:
        """Generate a secure temporary token"""
        return secrets.token_urlsafe(32)
    
    async def get_decrypted_token(self, session: AuthSession) -> Optional[str]:
        """Get decrypted session token"""
        if not session.session_token:
            return None
        
        decrypted = token_encryption.decrypt_token(session.session_token)
        return decrypted
    
    async def _cache_session_info(
        self,
        session_id: str,
        user_id: str,
        encrypted_token: str
    ) -> None:
        """Cache session information"""
        cache_data = {
            "user_id": user_id,
            "encrypted_token": encrypted_token,
            "cached_at": datetime.utcnow().isoformat()
        }
        
        expire_seconds = settings.jwt_access_token_expire_minutes * 60
        await self.cache.setex(
            f"session:{session_id}",
            expire_seconds,
            json.dumps(cache_data)
        )