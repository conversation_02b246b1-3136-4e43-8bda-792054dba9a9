"""
Patient related schemas
"""
from typing import Optional, List, Any, Dict
from pydantic import BaseModel, Field, validator
from datetime import datetime


class PatientBase(BaseModel):
    """Base patient model"""
    patient_id: str = Field(..., description="Patient ID")
    name: str = Field(..., description="Patient name")
    age: Optional[str] = Field(None, description="Age")
    sex: Optional[str] = Field(None, description="Gender (1: Male, 2: Female)")
    mobile: Optional[str] = Field(None, description="Mobile number")
    tags: List[str] = Field(default_factory=list, description="Patient tags")
    
    @validator("sex")
    def validate_sex(cls, v):
        """Convert sex to readable format"""
        if v == "1":
            return "男"
        elif v == "2":
            return "女"
        return v


class PatientInfo(PatientBase):
    """Detailed patient information"""
    birthday: Optional[str] = Field(None, description="Birthday")
    is_pregnant: Optional[bool] = Field(None, description="Is pregnant")
    remark: Optional[str] = Field(None, description="Remark")
    avatar_url: Optional[str] = Field(None, description="Avatar URL")
    last_visit_date: Optional[datetime] = Field(None, description="Last visit date")
    visit_count: Optional[int] = Field(None, description="Visit count")
    created_at: Optional[datetime] = Field(None, description="Creation time")


class PatientListItem(PatientBase):
    """Patient list item model"""
    last_visit_date: Optional[str] = Field(None, description="Last visit date")
    visit_count: Optional[int] = Field(None, description="Visit count")
    last_message: Optional[str] = Field(None, description="Last message preview")
    unread_count: Optional[int] = Field(None, description="Unread message count")


class PatientListResponse(BaseModel):
    """Patient list response model"""
    code: int = Field(default=200, description="Response code")
    message: str = Field(default="Success", description="Response message")
    data: List[PatientListItem] = Field(default_factory=list, description="Patient list")
    total: int = Field(default=0, description="Total count")
    page: int = Field(default=1, description="Current page")
    size: int = Field(default=20, description="Page size")


class PatientDetailResponse(BaseModel):
    """Patient detail response model"""
    code: int = Field(default=200, description="Response code")
    message: str = Field(default="Success", description="Response message")
    data: Optional[PatientInfo] = Field(None, description="Patient information")


class PatientSearchParams(BaseModel):
    """Patient search parameters"""
    search: Optional[str] = Field(None, description="Search keyword (name/mobile)")
    tags: Optional[List[str]] = Field(None, description="Filter by tags")
    has_prescription: Optional[bool] = Field(None, description="Has prescription")
    has_unread: Optional[bool] = Field(None, description="Has unread messages")
    sort_by: Optional[str] = Field("last_visit", description="Sort field")
    sort_order: Optional[str] = Field("desc", description="Sort order (asc/desc)")
    
    @validator("sort_by")
    def validate_sort_by(cls, v):
        """Validate sort field"""
        allowed_fields = ["last_visit", "name", "visit_count", "created_at"]
        if v not in allowed_fields:
            raise ValueError(f"Invalid sort field. Allowed: {allowed_fields}")
        return v
    
    @validator("sort_order")
    def validate_sort_order(cls, v):
        """Validate sort order"""
        if v not in ["asc", "desc"]:
            raise ValueError("Sort order must be 'asc' or 'desc'")
        return v


class PatientStatistics(BaseModel):
    """Patient statistics model"""
    total_patients: int = Field(default=0, description="Total patients")
    active_patients: int = Field(default=0, description="Active patients (visited in last 30 days)")
    new_patients_month: int = Field(default=0, description="New patients this month")
    total_visits: int = Field(default=0, description="Total visits")
    total_prescriptions: int = Field(default=0, description="Total prescriptions")