"""
Authentication API endpoints
"""
from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, Request, status
from sqlalchemy.orm import Session
from typing import Optional
import logging
from datetime import datetime

from app.database import get_db
from app.schemas.auth import (
    ScanAuthRequest,
    ScanAuthResponse,
    AuthStatusResponse,
    RefreshTokenRequest,
    RefreshTokenResponse
)
from app.services.auth_service import AuthService
from app.services.cache_service import CacheService
from app.services.websocket_manager import manager
from app.models.auth import AuthStatus

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/scan", response_model=ScanAuthResponse)
async def scan_auth(
    request: ScanAuthRequest,
    client_request: Request,
    db: Session = Depends(get_db)
):
    """
    Handle scan authentication from iOS app
    
    This endpoint is called when the iOS app scans a QR code from the web interface.
    It creates an authentication session and returns a temporary token.
    """
    try:
        # For iOS app, use the sessionToken directly as provided
        # The sessionToken format from iOS appears to be: timestamp-randomId 
        # Let's extract a user ID from the sessionToken format
        logger.info(f"Processing session token: {request.session_token[:30]}...")
        
        # Extract real doctor user ID from auth_code format (timestamp_random_userId_suffix)
        try:
            # iOS auth_code format: timestamp_random_userId_web
            # Example: 1748767461310_1386_0000000025073_web
            parts = request.auth_code.split("_")
            if len(parts) >= 3:
                # Use the third part as the real doctor user ID
                user_id = parts[2]
                logger.info(f"Extracted real doctor ID from auth_code: {user_id}")
            else:
                # Fallback: extract from sessionToken
                session_parts = request.session_token.split("-")
                if len(session_parts) >= 2:
                    potential_user_id = session_parts[1]
                    user_id = ''.join(c for c in potential_user_id if c.isalnum())[:16]
                    logger.info(f"Fallback: extracted user ID from token: {user_id}")
                else:
                    # Last resort: use a simple hash of the token
                    import hashlib
                    user_id = "u_" + hashlib.sha256(request.session_token.encode()).hexdigest()[:8]
                    logger.info(f"Generated fallback user ID: {user_id}")
                
        except Exception as e:
            logger.error(f"Failed to extract user ID from auth_code: {e}")
            # Generate user ID from session token hash as fallback
            import hashlib
            user_id = "u_" + hashlib.sha256(request.session_token.encode()).hexdigest()[:8]
        
        # Get client IP
        client_ip = client_request.client.host
        
        # Create auth session
        auth_service = AuthService(db)
        auth_session = await auth_service.create_auth_session(
            auth_code=request.auth_code,
            session_token=request.session_token,
            user_id=user_id,
            device_info=request.device_info.dict(),
            current_patient_id=request.current_patient_id,
            patient_name=request.current_patient_name,
            ip_address=client_ip
        )
        
        # Notify web client via WebSocket
        await manager.notify_auth_success(
            request.web_session_id,
            {
                "session_id": auth_session.session_id,
                "user_id": user_id,
                "patient_id": auth_session.current_patient_id,
                "patient_name": auth_session.patient_name,
                "expires_in": 1800  # 30 minutes
            }
        )
        
        logger.info(f"Scan auth successful for user {user_id}")
        
        return ScanAuthResponse(
            code=200,
            message="授权成功",
            session_id=auth_session.session_id,
            expires_in=1800
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Scan auth error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authorization failed"
        )


@router.get("/status/{auth_code}", response_model=AuthStatusResponse)
async def check_auth_status(
    auth_code: str,
    db: Session = Depends(get_db)
):
    """
    Check authentication status by auth code
    
    This endpoint is polled by the web interface to check if the user
    has completed the authentication process.
    """
    auth_service = AuthService(db)
    session = await auth_service.get_session_by_auth_code(auth_code)
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Auth session not found"
        )
    
    response = AuthStatusResponse(
        auth_code=auth_code,
        status=session.status
    )
    
    if session.status == AuthStatus.AUTHORIZED and session.temp_token:
        response.temp_token = session.temp_token
        response.expires_at = session.expires_at
        response.user_info = {
            "user_id": session.user_id,
            "current_patient_id": session.current_patient_id
        }
    
    return response


@router.post("/refresh", response_model=RefreshTokenResponse)
async def refresh_token(
    request: RefreshTokenRequest,
    db: Session = Depends(get_db)
):
    """
    Refresh temporary access token
    
    This endpoint allows refreshing an existing token before it expires.
    """
    auth_service = AuthService(db)
    new_token = await auth_service.refresh_token(request.temp_token)
    
    if not new_token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token"
        )
    
    return RefreshTokenResponse(
        code=200,
        message="Token refreshed successfully",
        new_token=new_token,
        expires_in=1800
    )


@router.post("/logout")
async def logout(
    request: Request,
    db: Session = Depends(get_db)
):
    """
    Logout and invalidate token
    
    This endpoint invalidates the current token and ends the session.
    """
    # Get request body
    body = await request.json()
    token = body.get("token")
    
    if not token:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Token is required"
        )
    
    auth_service = AuthService(db)
    success = await auth_service.invalidate_token(token)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to logout"
        )
    
    return {
        "code": 200,
        "message": "Logged out successfully"
    }


@router.post("/validate")
async def validate_token(
    token: str,
    db: Session = Depends(get_db)
):
    """
    Validate temporary token
    
    This endpoint checks if a token is valid and returns session information.
    """
    auth_service = AuthService(db)
    session = await auth_service.get_session_by_token(token)
    
    if not session or not session.is_valid():
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token"
        )
    
    return {
        "code": 200,
        "message": "Token is valid",
        "data": {
            "user_id": session.user_id,
            "expires_at": session.expires_at.isoformat(),
            "current_patient_id": session.current_patient_id
        }
    }