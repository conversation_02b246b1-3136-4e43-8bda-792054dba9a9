"""
Security utilities for encryption and decryption
"""
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import secrets
import logging
from typing import Optional

from app.config import settings

logger = logging.getLogger(__name__)


class TokenEncryption:
    """Handle encryption and decryption of sensitive tokens"""
    
    def __init__(self):
        # Generate encryption key from secret key
        self.fernet = self._create_fernet()
    
    def _create_fernet(self) -> Fernet:
        """Create Fernet instance from settings secret key"""
        # Use PBKDF2 to derive a key from the secret
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=b'brzy_medical_export_salt',  # Fixed salt for consistency
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(settings.secret_key.encode()))
        return Fernet(key)
    
    def encrypt_token(self, token: str) -> str:
        """
        Encrypt a token
        
        Args:
            token: Plain text token
            
        Returns:
            Encrypted token as string
        """
        try:
            encrypted = self.fernet.encrypt(token.encode())
            return base64.urlsafe_b64encode(encrypted).decode()
        except Exception as e:
            logger.error(f"Token encryption failed: {e}")
            raise
    
    def decrypt_token(self, encrypted_token: str) -> Optional[str]:
        """
        Decrypt a token
        
        Args:
            encrypted_token: Encrypted token string
            
        Returns:
            Decrypted token or None if decryption fails
        """
        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_token.encode())
            decrypted = self.fernet.decrypt(encrypted_bytes)
            return decrypted.decode()
        except Exception as e:
            logger.error(f"Token decryption failed: {e}")
            return None


def generate_session_id() -> str:
    """Generate a secure random session ID"""
    return secrets.token_urlsafe(32)


def hash_auth_code(auth_code: str) -> str:
    """
    Create a hash of the auth code for secure comparison
    
    Args:
        auth_code: The auth code to hash
        
    Returns:
        Hashed auth code
    """
    import hashlib
    return hashlib.sha256(f"{auth_code}:{settings.secret_key}".encode()).hexdigest()


# Global instance
token_encryption = TokenEncryption()