#!/bin/bash
set -e

# Backend Runtime Configuration and Startup Script
# 用于在容器启动时验证配置并启动服务

echo "🚀 Starting BRZY Medical Backend with external configuration..."

# 配置检查函数
check_required_env() {
    local var_name=$1
    local var_value=${!var_name}
    
    if [ -z "$var_value" ]; then
        echo "❌ Required environment variable $var_name is not set"
        return 1
    else
        echo "✅ $var_name is configured"
        return 0
    fi
}

# 配置验证
echo "📝 Validating runtime configuration..."

# 必需的环境变量
REQUIRED_VARS=(
    "DATABASE_URL"
    "JWT_SECRET_KEY" 
    "ORIGINAL_API_BASE_URL"
)

# 检查必需环境变量
MISSING_VARS=0
for var in "${REQUIRED_VARS[@]}"; do
    if ! check_required_env "$var"; then
        MISSING_VARS=$((MISSING_VARS + 1))
    fi
done

if [ $MISSING_VARS -gt 0 ]; then
    echo "❌ Missing $MISSING_VARS required environment variables"
    echo "Please set the following environment variables:"
    for var in "${REQUIRED_VARS[@]}"; do
        if [ -z "${!var}" ]; then
            echo "  - $var"
        fi
    done
    exit 1
fi

# 设置默认值
export APP_NAME="${APP_NAME:-BRZY Medical Export}"
export APP_VERSION="${APP_VERSION:-1.0.0}"
export DEBUG="${DEBUG:-false}"
export LOG_LEVEL="${LOG_LEVEL:-INFO}"
export CORS_ORIGINS="${CORS_ORIGINS:-http://localhost:5173,https://localhost:5173}"
export PORT="${PORT:-8080}"
export WORKERS="${WORKERS:-4}"

# Redis 配置（可选）
if [ -n "$REDIS_URL" ]; then
    echo "✅ Redis cache enabled: $REDIS_URL"
else
    echo "⚠️ Redis cache not configured (optional)"
fi

echo "📊 Runtime configuration summary:"
echo "App Name: $APP_NAME"
echo "App Version: $APP_VERSION"
echo "Debug Mode: $DEBUG"
echo "Log Level: $LOG_LEVEL"
echo "Port: $PORT"
echo "Workers: $WORKERS"
echo "CORS Origins: $CORS_ORIGINS"

# 创建必要的目录
echo "📁 Creating runtime directories..."
mkdir -p /workspace/logs /workspace/temp /workspace/uploads /workspace/generated_pdfs

# 设置 Python 路径
export PYTHONPATH="/workspace:$PYTHONPATH"

# 数据库连接测试
echo "🔍 Testing database connection..."
python3 << 'EOF'
import sys
import os
sys.path.insert(0, '/workspace')

try:
    from app.database import check_database_connection
    if check_database_connection():
        print("✅ Database connection successful")
    else:
        print("❌ Database connection failed")
        sys.exit(1)
except Exception as e:
    print(f"❌ Database connection error: {e}")
    sys.exit(1)
EOF

if [ $? -ne 0 ]; then
    echo "❌ Database connection test failed, exiting..."
    exit 1
fi

# 数据库表初始化
echo "🔧 Initializing database tables..."
python3 /workspace/scripts/init-database.py

if [ $? -ne 0 ]; then
    echo "❌ Database initialization failed, exiting..."
    exit 1
fi

# Redis 连接测试（如果配置）
if [ -n "$REDIS_URL" ]; then
    echo "🔍 Testing Redis connection..."
    python3 << 'EOF'
import sys
import os
sys.path.insert(0, '/workspace')

try:
    import redis
    redis_url = os.getenv('REDIS_URL')
    if redis_url:
        r = redis.from_url(redis_url)
        r.ping()
        print("✅ Redis connection successful")
    else:
        print("⚠️ Redis URL not provided")
except Exception as e:
    print(f"⚠️ Redis connection warning: {e}")
    # Redis 是可选的，不强制退出
EOF
fi

# 启动应用
echo "🌐 Starting FastAPI application..."
echo "Server: uvicorn"
echo "Module: app.main:app"
echo "Host: 0.0.0.0"
echo "Port: $PORT"
echo "Workers: $WORKERS"

# 根据 workers 数量选择启动方式
if [ "$WORKERS" = "1" ]; then
    # 单进程启动
    exec uvicorn app.main:app \
        --host 0.0.0.0 \
        --port "$PORT" \
        --log-level "${LOG_LEVEL,,}" \
        --access-log \
        --use-colors \
        --loop uvloop \
        --http httptools
else
    # 多进程启动
    exec uvicorn app.main:app \
        --host 0.0.0.0 \
        --port "$PORT" \
        --workers "$WORKERS" \
        --log-level "${LOG_LEVEL,,}" \
        --access-log \
        --use-colors \
        --loop uvloop \
        --http httptools
fi