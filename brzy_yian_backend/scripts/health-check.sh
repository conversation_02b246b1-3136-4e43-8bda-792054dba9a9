#!/bin/bash

# Backend Health Check Script
# 用于容器健康检查和监控

set -e

# 配置
PORT="${PORT:-8080}"
TIMEOUT="${HEALTH_TIMEOUT:-10}"
MAX_RETRIES="${HEALTH_RETRIES:-3}"

# 健康检查函数
check_health() {
    local url="http://localhost:${PORT}/health"
    local retry_count=0
    
    while [ $retry_count -lt $MAX_RETRIES ]; do
        if curl -f -s --max-time $TIMEOUT "$url" > /dev/null 2>&1; then
            echo "✅ Health check passed"
            return 0
        fi
        
        retry_count=$((retry_count + 1))
        if [ $retry_count -lt $MAX_RETRIES ]; then
            echo "⚠️ Health check attempt $retry_count failed, retrying..."
            sleep 2
        fi
    done
    
    echo "❌ Health check failed after $MAX_RETRIES attempts"
    return 1
}

# 详细健康检查
detailed_check() {
    local url="http://localhost:${PORT}/health"
    
    echo "🔍 Performing detailed health check..."
    echo "URL: $url"
    echo "Timeout: ${TIMEOUT}s"
    echo "Max Retries: $MAX_RETRIES"
    
    # 获取详细的健康状态
    local response=$(curl -f -s --max-time $TIMEOUT "$url" 2>/dev/null || echo "")
    
    if [ -n "$response" ]; then
        echo "📊 Health status response:"
        echo "$response"
        
        # 尝试解析 JSON 响应
        if command -v python3 >/dev/null 2>&1; then
            echo "$response" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    print(f\"Status: {data.get('status', 'unknown')}\")
    print(f\"Version: {data.get('version', 'unknown')}\")
    
    services = data.get('services', {})
    if services:
        print('Services:')
        for service, status in services.items():
            print(f\"  - {service}: {status}\")
except:
    pass
"
        fi
        return 0
    else
        echo "❌ No response from health endpoint"
        return 1
    fi
}

# 主逻辑
case "${1:-check}" in
    "check")
        check_health
        ;;
    "detailed")
        detailed_check
        ;;
    "monitor")
        echo "🔄 Starting continuous health monitoring..."
        while true; do
            timestamp=$(date -u +"%Y-%m-%d %H:%M:%S UTC")
            printf "[%s] " "$timestamp"
            if check_health; then
                :  # 成功时不输出额外信息
            else
                echo "Health check failed at $timestamp"
            fi
            sleep 30
        done
        ;;
    *)
        echo "Usage: $0 [check|detailed|monitor]"
        echo "  check    - Basic health check (default)"
        echo "  detailed - Detailed health status"
        echo "  monitor  - Continuous monitoring"
        exit 1
        ;;
esac