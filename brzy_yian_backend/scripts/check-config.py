#!/usr/bin/env python3
"""
Backend Configuration Validation Script
用于验证运行时配置的完整性和有效性
"""

import os
import sys
import re
import urllib.parse
from typing import List, Dict, Any


class ConfigValidator:
    """配置验证器"""
    
    def __init__(self):
        self.errors: List[str] = []
        self.warnings: List[str] = []
        
    def validate_required_env(self, var_name: str) -> bool:
        """验证必需的环境变量"""
        value = os.getenv(var_name)
        if not value:
            self.errors.append(f"Missing required environment variable: {var_name}")
            return False
        return True
    
    def validate_database_url(self, url: str) -> bool:
        """验证数据库 URL 格式"""
        try:
            parsed = urllib.parse.urlparse(url)
            if not parsed.scheme:
                self.errors.append("DATABASE_URL: Missing scheme (mysql+pymysql://)")
                return False
            if not parsed.hostname:
                self.errors.append("DATABASE_URL: Missing hostname")
                return False
            if not parsed.path or parsed.path == '/':
                self.errors.append("DATABASE_URL: Missing database name")
                return False
            return True
        except Exception as e:
            self.errors.append(f"DATABASE_URL: Invalid format - {e}")
            return False
    
    def validate_redis_url(self, url: str) -> bool:
        """验证 Redis URL 格式"""
        try:
            parsed = urllib.parse.urlparse(url)
            if not parsed.scheme or parsed.scheme not in ['redis', 'rediss']:
                self.warnings.append("REDIS_URL: Should use redis:// or rediss:// scheme")
                return False
            if not parsed.hostname:
                self.warnings.append("REDIS_URL: Missing hostname")
                return False
            return True
        except Exception as e:
            self.warnings.append(f"REDIS_URL: Invalid format - {e}")
            return False
    
    def validate_jwt_secret(self, secret: str) -> bool:
        """验证 JWT 密钥强度"""
        if len(secret) < 32:
            self.errors.append("JWT_SECRET_KEY: Too short, should be at least 32 characters")
            return False
        
        # 检查是否为默认值
        default_patterns = [
            'your-secret-key',
            'change-this',
            'development',
            'test',
            '123456'
        ]
        
        for pattern in default_patterns:
            if pattern.lower() in secret.lower():
                self.errors.append(f"JWT_SECRET_KEY: Contains insecure pattern '{pattern}'")
                return False
        
        return True
    
    def validate_api_url(self, url: str) -> bool:
        """验证 API URL 格式"""
        try:
            parsed = urllib.parse.urlparse(url)
            if not parsed.scheme or parsed.scheme not in ['http', 'https']:
                self.errors.append("ORIGINAL_API_BASE_URL: Must use http:// or https://")
                return False
            if not parsed.hostname:
                self.errors.append("ORIGINAL_API_BASE_URL: Missing hostname")
                return False
            return True
        except Exception as e:
            self.errors.append(f"ORIGINAL_API_BASE_URL: Invalid format - {e}")
            return False
    
    def validate_cors_origins(self, origins: str) -> bool:
        """验证 CORS 源配置"""
        origin_list = [origin.strip() for origin in origins.split(',')]
        
        for origin in origin_list:
            if origin == '*':
                self.warnings.append("CORS_ORIGINS: Wildcard (*) allows all origins - security risk in production")
                continue
                
            try:
                parsed = urllib.parse.urlparse(origin)
                if not parsed.scheme:
                    self.warnings.append(f"CORS_ORIGINS: '{origin}' missing scheme")
                elif parsed.scheme not in ['http', 'https']:
                    self.warnings.append(f"CORS_ORIGINS: '{origin}' should use http:// or https://")
            except Exception:
                self.warnings.append(f"CORS_ORIGINS: Invalid origin format '{origin}'")
        
        return True
    
    def validate_log_level(self, level: str) -> bool:
        """验证日志级别"""
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if level.upper() not in valid_levels:
            self.errors.append(f"LOG_LEVEL: Invalid level '{level}', must be one of {valid_levels}")
            return False
        return True
    
    def validate_workers(self, workers: str) -> bool:
        """验证 worker 数量"""
        try:
            worker_count = int(workers)
            if worker_count < 1:
                self.errors.append("WORKERS: Must be at least 1")
                return False
            if worker_count > 16:
                self.warnings.append("WORKERS: High worker count may cause resource issues")
        except ValueError:
            self.errors.append("WORKERS: Must be a valid integer")
            return False
        return True
    
    def validate_all(self) -> bool:
        """执行完整的配置验证"""
        print("🔍 Validating backend configuration...")
        
        # 必需的环境变量
        required_vars = [
            'DATABASE_URL',
            'JWT_SECRET_KEY',
            'ORIGINAL_API_BASE_URL'
        ]
        
        for var in required_vars:
            self.validate_required_env(var)
        
        # 详细验证
        database_url = os.getenv('DATABASE_URL')
        if database_url:
            self.validate_database_url(database_url)
        
        jwt_secret = os.getenv('JWT_SECRET_KEY')
        if jwt_secret:
            self.validate_jwt_secret(jwt_secret)
        
        api_url = os.getenv('ORIGINAL_API_BASE_URL')
        if api_url:
            self.validate_api_url(api_url)
        
        # 可选配置验证
        redis_url = os.getenv('REDIS_URL')
        if redis_url:
            self.validate_redis_url(redis_url)
        
        cors_origins = os.getenv('CORS_ORIGINS', 'http://localhost:5173')
        self.validate_cors_origins(cors_origins)
        
        log_level = os.getenv('LOG_LEVEL', 'INFO')
        self.validate_log_level(log_level)
        
        workers = os.getenv('WORKERS', '4')
        self.validate_workers(workers)
        
        # 输出结果
        if self.warnings:
            print("⚠️ Configuration warnings:")
            for warning in self.warnings:
                print(f"  - {warning}")
        
        if self.errors:
            print("❌ Configuration errors:")
            for error in self.errors:
                print(f"  - {error}")
            return False
        
        print("✅ Configuration validation passed")
        return True


def main():
    """主函数"""
    validator = ConfigValidator()
    
    try:
        is_valid = validator.validate_all()
        
        if not is_valid:
            print("\n❌ Configuration validation failed!")
            print("Please fix the above errors before starting the application.")
            sys.exit(1)
        
        print("\n✅ All configuration checks passed!")
        
        # 输出配置摘要
        print("\n📊 Configuration summary:")
        print(f"  Database: {os.getenv('DATABASE_URL', 'Not set')[:50]}...")
        print(f"  API URL: {os.getenv('ORIGINAL_API_BASE_URL', 'Not set')}")
        print(f"  Redis: {'Enabled' if os.getenv('REDIS_URL') else 'Disabled'}")
        print(f"  Log Level: {os.getenv('LOG_LEVEL', 'INFO')}")
        print(f"  Workers: {os.getenv('WORKERS', '4')}")
        print(f"  Debug: {os.getenv('DEBUG', 'false')}")
        
    except Exception as e:
        print(f"❌ Configuration validation error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()