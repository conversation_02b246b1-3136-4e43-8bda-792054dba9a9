# CNB buildpack configuration for FastAPI backend
# 配置外置化 - 构建时不包含环境配置

python:
  # Python version specification
  version: "3.11.*"
  
  # Package management
  pip:
    # Use latest pip for better dependency resolution
    version: "23.*"
    # Install from requirements.txt
    requirements-file: "requirements.txt"
    # Enable pip cache for faster builds
    cache: true
    # 仅安装生产依赖（排除开发依赖）
    production-only: true
  
  # Runtime configuration (移除具体配置值)
  runtime:
    # ASGI server configuration
    server: "uvicorn"
    # Application module (固定值)
    app-module: "app.main:app"
    # Port configuration (运行时可覆盖)
    port: 8080
    # Worker configuration (运行时可调整)
    workers: "auto"
    # 启用配置外置
    enable-external-config: true
    # 配置检查脚本
    config-check-script: "/workspace/scripts/check-config.py"
  
  # 构建时环境变量（仅构建相关）
  build-env:
    PYTHONPATH: "/workspace"
    PYTHON_ENV: "production"
    # Disable bytecode generation for faster startup
    PYTHONDONTWRITEBYTECODE: "1"
    # Force stdout/stderr to be unbuffered
    PYTHONUNBUFFERED: "1"
    # Optimize Python for production
    PYTHONOPTIMIZE: "2"
    # 排除运行时配置文件
    EXCLUDE_ENV_FILES: ".env*,config/*.local.*,secrets/*"

# Health check configuration
health-check:
  # Health endpoint (固定路径)
  endpoint: "/health"
  # Initial delay before health checks
  initial-delay: 30
  # Interval between health checks
  interval: 10
  # Timeout for health check requests
  timeout: 5
  # Number of retries before marking as unhealthy
  retries: 3

# Build process configuration
pre-build:
  commands:
    - "echo 'Starting backend build process...'"
    - "python --version"
    - "pip --version"
    - "echo 'Installing system dependencies for PDF generation...'"
    - "echo 'Building for external configuration deployment'"

post-build:
  commands:
    - "echo 'Backend build completed successfully'"
    - "python -c 'import fastapi; print(f\"FastAPI version: {fastapi.__version__}\")'"
    - "python -c 'import sqlalchemy; print(f\"SQLAlchemy version: {sqlalchemy.__version__}\")'"
    - "echo 'Creating configuration template and scripts'"
    - "mkdir -p /workspace/scripts /workspace/config"

# Performance optimizations
optimization:
  # Precompile Python bytecode
  compile-bytecode: true
  # Strip debug symbols
  strip-debug: true
  # Optimize for production
  optimize-level: 2
  # 移除不必要的文件
  remove-cache: true
  remove-test-files: true

# Security settings
security:
  # Run as non-root user
  user: "cnb"
  # Remove development tools
  remove-dev-tools: true
  # Scan for vulnerabilities
  vulnerability-scan: true
  # 移除敏感文件
  remove-sensitive-files: true

# 系统依赖配置
system-packages:
  - "libmariadb-dev"     # For MySQL client
  - "libffi-dev"         # For cryptography
  - "libjpeg-dev"        # For Pillow/PIL
  - "libpng-dev"         # For image processing
  - "fonts-liberation"   # For PDF fonts
  - "fontconfig"         # Font configuration
  - "curl"               # For health checks
  - "gettext-base"       # For envsubst

# 运行时配置管理
runtime-config:
  # 必需的环境变量列表
  required-env-vars:
    - "DATABASE_URL"
    - "JWT_SECRET_KEY"
    - "ORIGINAL_API_BASE_URL"
  
  # 可选的环境变量
  optional-env-vars:
    - "REDIS_URL"
    - "LOG_LEVEL"
    - "CORS_ORIGINS"
    - "APP_NAME"
    - "APP_VERSION"
    - "DEBUG"
  
  # 配置验证脚本
  validation:
    enabled: true
    script: "/workspace/scripts/validate-config.py"
    fail-on-missing: true
  
  # 配置模板文件
  templates:
    - source: "config.py.template"
      destination: "/workspace/app/runtime_config.py"

# 应用启动配置
startup:
  # 使用自定义启动脚本
  script: "/workspace/scripts/start-backend.sh"
  # 预启动检查
  pre-start-checks:
    - "python /workspace/scripts/check-config.py"
    - "python -c 'from app.database import check_database_connection; exit(0 if check_database_connection() else 1)'"
    - "python /workspace/scripts/init-database.py"
  
# 监控和日志
monitoring:
  # 启用内置监控端点
  enable-metrics: true
  # 日志配置
  logging:
    level: "INFO"
    format: "json"
    destination: "stdout"