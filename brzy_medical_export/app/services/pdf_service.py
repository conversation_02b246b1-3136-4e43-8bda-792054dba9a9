"""
PDF Generation Service
"""
import os
import uuid
import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime
import logging

from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from reportlab.pdfbase.cidfonts import UnicodeCIDFont

from app.services.api_proxy import APIProxyService

logger = logging.getLogger(__name__)


class PDFGenerationService:
    """PDF generation service for medical records"""
    
    def __init__(self):
        self.output_dir = "generated_pdfs"
        self.setup_output_directory()
        self.setup_chinese_fonts()
        self.tasks = {}  # In-memory task storage (should use Redis in production)
        logger.info(f"PDFGenerationService initialized with font: {getattr(self, 'chinese_font_name', 'Helvetica')}")
    
    def setup_output_directory(self):
        """Create output directory if it doesn't exist"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def setup_chinese_fonts(self):
        """Setup Chinese font support"""
        try:
            # First, try to use built-in CID fonts which support Chinese
            try:
                pdfmetrics.registerFont(UnicodeCIDFont('STSong-Light'))
                logger.info("Successfully registered STSong-Light CID font for Chinese support")
                self.chinese_font_name = 'STSong-Light'
                return
            except Exception as e:
                logger.warning(f"Failed to register CID font: {e}")
            
            # If CID font fails, try system fonts
            font_paths = [
                "/System/Library/Fonts/STHeiti Medium.ttc",  # macOS STHeiti Medium
                "/System/Library/Fonts/STHeiti Light.ttc",  # macOS STHeiti Light  
                "/System/Library/Fonts/Hiragino Sans GB.ttc",  # macOS Hiragino Sans GB
                "/System/Library/Fonts/PingFang.ttc",  # macOS PingFang
                "/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf",  # Linux Android fonts
                "/usr/share/fonts/truetype/arphic/uming.ttc",  # Linux AR PL UMing
                "C:/Windows/Fonts/simhei.ttf",  # Windows SimHei
                "C:/Windows/Fonts/simsun.ttc",  # Windows SimSun
                "C:/Windows/Fonts/msyh.ttc",  # Windows Microsoft YaHei
            ]
            
            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        pdfmetrics.registerFont(TTFont('chinese', font_path))
                        logger.info(f"Successfully registered Chinese font: {font_path}")
                        self.chinese_font_name = 'chinese'
                        return
                    except Exception as e:
                        logger.warning(f"Failed to register font {font_path}: {e}")
                        continue
            
            # If no fonts work, log error but continue with default font
            logger.error("No Chinese font could be registered. PDF will use default fonts - Chinese characters may not display correctly.")
            self.chinese_font_name = 'Helvetica'  # Fallback to default
                
        except Exception as e:
            logger.error(f"Failed to setup Chinese fonts: {e}")
            self.chinese_font_name = 'Helvetica'  # Fallback to default
    
    async def generate_medical_record_pdf(
        self, 
        patient_id: str, 
        session_token: str,
        medical_records: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """
        Generate PDF for patient medical records
        
        Args:
            patient_id: Patient ID
            session_token: iOS session token
            medical_records: Optional pre-fetched medical records
            
        Returns:
            task_id: PDF generation task ID
        """
        task_id = str(uuid.uuid4())
        
        # Store task info
        self.tasks[task_id] = {
            "status": "processing",
            "created_at": datetime.now(),
            "patient_id": patient_id,
            "file_path": None,
            "error": None
        }
        
        # Start background task
        asyncio.create_task(
            self._generate_pdf_background(
                task_id, patient_id, session_token, medical_records
            )
        )
        
        return task_id
    
    async def generate_prescription_pdf(
        self, 
        order_id: str, 
        session_token: str,
        user_id: str,
        device_info: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Generate PDF for a single prescription order
        
        Args:
            order_id: Prescription order ID
            session_token: iOS session token
            user_id: User ID
            device_info: Device information
            
        Returns:
            task_id: PDF generation task ID
        """
        task_id = str(uuid.uuid4())
        
        # Store task info
        self.tasks[task_id] = {
            "status": "processing",
            "created_at": datetime.now(),
            "order_id": order_id,
            "type": "prescription",
            "file_path": None,
            "error": None
        }
        
        # Start background task
        asyncio.create_task(
            self._generate_prescription_pdf_background(
                task_id, order_id, session_token, user_id, device_info
            )
        )
        
        return task_id
    
    async def _generate_prescription_pdf_background(
        self, 
        task_id: str, 
        order_id: str, 
        session_token: str,
        user_id: str,
        device_info: Optional[Dict[str, Any]] = None
    ):
        """Background task to generate prescription PDF"""
        try:
            # Fetch prescription order details
            api_proxy = APIProxyService()
            prescription_data = await api_proxy.get_prescription_order_detail(
                session_token=session_token,
                user_id=user_id,
                order_id=order_id,
                device_info=device_info
            )
            
            if not prescription_data:
                raise Exception("Failed to fetch prescription data")
            
            # Generate PDF
            file_path = await self._create_prescription_pdf_file(task_id, order_id, prescription_data)
            
            # Update task status
            self.tasks[task_id].update({
                "status": "completed",
                "file_path": file_path,
                "completed_at": datetime.now()
            })
            
            logger.info(f"Prescription PDF generation completed for task {task_id}")
            
        except Exception as e:
            logger.error(f"Prescription PDF generation failed for task {task_id}: {e}")
            self.tasks[task_id].update({
                "status": "failed",
                "error": str(e),
                "failed_at": datetime.now()
            })
    
    async def _generate_pdf_background(
        self, 
        task_id: str, 
        patient_id: str, 
        session_token: str,
        medical_records: Optional[List[Dict[str, Any]]] = None
    ):
        """Background task to generate PDF"""
        try:
            # Fetch medical records if not provided
            if medical_records is None:
                api_proxy = APIProxyService()
                records_response = await api_proxy.get_patient_medical_records(
                    patient_id, session_token
                )
                if records_response.get("code") != "0000":
                    raise Exception(f"Failed to fetch medical records: {records_response.get('errorMsg')}")
                medical_records = records_response.get("data", [])
            
            # Generate PDF
            file_path = await self._create_pdf_file(task_id, patient_id, medical_records)
            
            # Update task status
            self.tasks[task_id].update({
                "status": "completed",
                "file_path": file_path,
                "completed_at": datetime.now()
            })
            
            logger.info(f"PDF generation completed for task {task_id}")
            
        except Exception as e:
            logger.error(f"PDF generation failed for task {task_id}: {e}")
            self.tasks[task_id].update({
                "status": "failed",
                "error": str(e),
                "failed_at": datetime.now()
            })
    
    async def _create_pdf_file(
        self, 
        task_id: str, 
        patient_id: str, 
        medical_records: List[Dict[str, Any]]
    ) -> str:
        """Create the actual PDF file"""
        
        filename = f"medical_record_{patient_id}_{task_id}.pdf"
        file_path = os.path.join(self.output_dir, filename)
        
        # Create PDF document
        doc = SimpleDocTemplate(
            file_path,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )
        
        # Build content
        story = []
        styles = self._get_styles()
        
        # Title
        title = Paragraph("患者医案记录", styles['title'])
        story.append(title)
        story.append(Spacer(1, 20))
        
        # Patient info (if available in first record)
        if medical_records:
            first_record = medical_records[0]
            patient_info = self._create_patient_info_section(first_record, styles)
            story.extend(patient_info)
            story.append(Spacer(1, 20))
        
        # Medical records
        for i, record in enumerate(medical_records):
            record_section = self._create_medical_record_section(record, i + 1, styles)
            story.extend(record_section)
            story.append(Spacer(1, 15))
        
        # Footer
        footer = Paragraph(
            f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            styles['footer']
        )
        story.append(footer)
        
        # Build PDF
        doc.build(story)
        
        return file_path
    
    async def _create_prescription_pdf_file(
        self, 
        task_id: str, 
        order_id: str, 
        prescription_data: Dict[str, Any]
    ) -> str:
        """Create prescription PDF file"""
        
        filename = f"prescription_{order_id}_{task_id}.pdf"
        file_path = os.path.join(self.output_dir, filename)
        
        # Create PDF document
        doc = SimpleDocTemplate(
            file_path,
            pagesize=A4,
            rightMargin=60,
            leftMargin=60,
            topMargin=72,
            bottomMargin=18
        )
        
        # Build content
        story = []
        styles = self._get_styles()
        
        # Define unified column widths for all tables
        # A4 width = 8.27 inches, with margins of 60 points (0.83 inch) each side
        # Available width = 8.27 - 1.66 = 6.61 inches
        # Using 6.5 inches for tables to have some padding
        col_widths = [1.4*inch, 2.2*inch, 1.4*inch, 1.5*inch]  # Total = 6.5 inches
        
        # Title
        title = Paragraph("处方详情", styles['title'])
        story.append(title)
        story.append(Spacer(1, 20))
        
        # Basic Information
        story.append(Paragraph("基本信息", styles['heading']))
        basic_info_data = [
            ['订单编号', prescription_data.get('id', ''), '创建时间', prescription_data.get('createdTime', '')],
            ['患者姓名', prescription_data.get('patientName', ''), '患者年龄', f"{prescription_data.get('patientAge', '')}岁"],
            ['联系电话', prescription_data.get('patientMobile', ''), '订单状态', prescription_data.get('orderStatus', '')],
            ['支付状态', '已支付' if prescription_data.get('payStatus') == 'PAYED' else '未支付', 
             '支付时间', prescription_data.get('payTime', '未支付')]
        ]
        
        basic_info_table = Table(basic_info_data, colWidths=col_widths)
        basic_info_table.setStyle(self._get_table_style())
        story.append(basic_info_table)
        story.append(Spacer(1, 15))
        
        # Diagnosis Information
        if prescription_data.get('dialectical') or prescription_data.get('rosterRemark'):
            story.append(Paragraph("诊断信息", styles['heading']))
            diag_data = [
                ['辨证', prescription_data.get('dialectical', ''), '备注', prescription_data.get('rosterRemark', '')]
            ]
            diag_table = Table(diag_data, colWidths=col_widths)
            diag_table.setStyle(self._get_table_style())
            story.append(diag_table)
            story.append(Spacer(1, 15))
        
        # Prescription Information
        story.append(Paragraph("处方信息", styles['heading']))
        pres_data = [
            ['剂型', prescription_data.get('drugForm', ''), '制法', prescription_data.get('mode', '')],
            ['处方天数', f"{prescription_data.get('preDays', '')}天", '每日用量', f"{prescription_data.get('preUsage', '')}次"],
            ['总剂数', f"{prescription_data.get('totalCount', '')}剂", '总重量', f"{prescription_data.get('totalWeight', '')}g"],
            ['服用时间', prescription_data.get('eatTime', '遵医嘱'), '禁忌', prescription_data.get('contraindication', '无')]
        ]
        
        pres_table = Table(pres_data, colWidths=col_widths)
        pres_table.setStyle(self._get_table_style())
        story.append(pres_table)
        story.append(Spacer(1, 15))
        
        # Medicine Details
        if prescription_data.get('details'):
            story.append(Paragraph(f"药材明细 (共 {len(prescription_data['details'])} 味药)", styles['heading']))
            
            # Medicine table header
            medicine_data = [['药材ID', '药材名称', '用量', '小计']]
            
            # Medicine table data
            for detail in prescription_data['details']:
                medicine_data.append([
                    detail.get('id', ''),
                    detail.get('name', ''),
                    f"{detail.get('amount', '')}{detail.get('unit', '')}",
                    f"¥{detail.get('totalPrice', '')}"
                ])
            
            medicine_table = Table(medicine_data, colWidths=[1.0*inch, 3.0*inch, 1.25*inch, 1.25*inch])
            medicine_table.setStyle(self._get_medicine_table_style())
            story.append(medicine_table)
            story.append(Spacer(1, 15))
        
        # Cost Details
        story.append(Paragraph("费用明细", styles['heading']))
        cost_data = [
            ['药材费', f"¥{prescription_data.get('baseDrugPrice', '')}", '制作费', f"¥{prescription_data.get('makeCost', '')}"],
            ['医疗服务费', f"¥{prescription_data.get('medicalServiceFee', '')}", '诊察费', f"¥{prescription_data.get('consultationFee', '')}"],
            ['运费', f"¥{prescription_data.get('carriage', '')}", '总计', f"¥{prescription_data.get('amount', '')}"]
        ]
        
        cost_table = Table(cost_data, colWidths=col_widths)
        cost_table.setStyle(self._get_cost_table_style())
        story.append(cost_table)
        story.append(Spacer(1, 15))
        
        # Delivery Information
        if prescription_data.get('reciverAddress'):
            story.append(Paragraph("配送信息", styles['heading']))
            delivery_data = [
                ['收货人', prescription_data.get('orderReciver', ''), '联系电话', prescription_data.get('reciverMobile', '')],
                ['收货地址', prescription_data.get('reciverAddress', ''), '配送中心', prescription_data.get('dcName', '')],
                ['快递公司', prescription_data.get('carrier', '未发货'), '快递单号', prescription_data.get('deliverySn', '未发货')]
            ]
            
            delivery_table = Table(delivery_data, colWidths=col_widths)
            delivery_table.setStyle(self._get_table_style())
            story.append(delivery_table)
            story.append(Spacer(1, 15))
        
        # Footer
        footer = Paragraph(
            f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            styles['footer']
        )
        story.append(footer)
        
        # Build PDF
        doc.build(story)
        
        return file_path
    
    def _get_styles(self) -> Dict[str, ParagraphStyle]:
        """Get custom styles for PDF"""
        styles = getSampleStyleSheet()
        
        # Use the registered Chinese font
        font_name = getattr(self, 'chinese_font_name', 'Helvetica')
        
        custom_styles = {
            'title': ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontName=font_name,
                fontSize=18,
                alignment=TA_CENTER,
                spaceAfter=12
            ),
            'heading': ParagraphStyle(
                'CustomHeading',
                parent=styles['Heading2'],
                fontName=font_name,
                fontSize=14,
                alignment=TA_LEFT,
                spaceAfter=6
            ),
            'normal': ParagraphStyle(
                'CustomNormal',
                parent=styles['Normal'],
                fontName=font_name,
                fontSize=10,
                alignment=TA_LEFT
            ),
            'footer': ParagraphStyle(
                'CustomFooter',
                parent=styles['Normal'],
                fontName=font_name,
                fontSize=8,
                alignment=TA_RIGHT,
                textColor=colors.grey
            )
        }
        
        return custom_styles
    
    def _create_patient_info_section(
        self, 
        record: Dict[str, Any], 
        styles: Dict[str, ParagraphStyle]
    ) -> List:
        """Create patient information section"""
        content = []
        
        # Patient info header
        content.append(Paragraph("患者信息", styles['heading']))
        
        # Extract patient info from record
        patient_name = record.get('patientName', '未知')
        patient_age = record.get('patientAge', '未知')
        patient_gender = '男' if record.get('patientGender') == '1' else '女' if record.get('patientGender') == '2' else '未知'
        
        # Create patient info table
        patient_data = [
            ['姓名', patient_name, '年龄', patient_age],
            ['性别', patient_gender, '患者ID', record.get('patientId', '未知')]
        ]
        
        patient_table = Table(patient_data, colWidths=[1*inch, 1.5*inch, 1*inch, 1.5*inch])
        patient_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), getattr(self, 'chinese_font_name', 'Helvetica')),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        content.append(patient_table)
        
        return content
    
    def _create_medical_record_section(
        self, 
        record: Dict[str, Any], 
        record_num: int,
        styles: Dict[str, ParagraphStyle]
    ) -> List:
        """Create medical record section"""
        content = []
        
        # Record header
        visit_date = record.get('visitDate', '未知日期')
        content.append(Paragraph(f"第{record_num}次就诊记录 - {visit_date}", styles['heading']))
        
        # Main content
        sections = [
            ('主诉', record.get('chiefComplaint', '')),
            ('现病史', record.get('presentIllness', '')),
            ('既往史', record.get('pastHistory', '')),
            ('体格检查', record.get('physicalExam', '')),
            ('辅助检查', record.get('auxiliaryExam', '')),
            ('中医四诊', record.get('tcmDiagnosis', '')),
            ('诊断', record.get('diagnosis', '')),
            ('治疗方案', record.get('treatment', '')),
            ('处方', record.get('prescription', '')),
            ('医嘱', record.get('doctorAdvice', ''))
        ]
        
        for section_name, section_content in sections:
            if section_content and section_content.strip():
                content.append(Paragraph(f"<b>{section_name}:</b>", styles['normal']))
                content.append(Paragraph(section_content, styles['normal']))
                content.append(Spacer(1, 6))
        
        return content
    
    def _get_table_style(self) -> TableStyle:
        """Get standard table style"""
        font_name = getattr(self, 'chinese_font_name', 'Helvetica')
        return TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),  # First column background
            ('BACKGROUND', (2, 0), (2, -1), colors.lightgrey),  # Third column background
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), font_name),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ])
    
    def _get_medicine_table_style(self) -> TableStyle:
        """Get medicine table style"""
        font_name = getattr(self, 'chinese_font_name', 'Helvetica')
        return TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),  # Header row
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), font_name),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightblue])
        ])
    
    def _get_cost_table_style(self) -> TableStyle:
        """Get cost table style with highlighted total"""
        font_name = getattr(self, 'chinese_font_name', 'Helvetica')
        return TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),  # First column background
            ('BACKGROUND', (2, 0), (2, -1), colors.lightgrey),  # Third column background
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), font_name),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            # Highlight the total amount row
            ('BACKGROUND', (2, -1), (3, -1), colors.lightyellow),
            ('FONTSIZE', (3, -1), (3, -1), 12),
            ('FONTNAME', (3, -1), (3, -1), font_name),
            ('TEXTCOLOR', (3, -1), (3, -1), colors.red)
        ])
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get PDF generation task status"""
        return self.tasks.get(task_id)
    
    def get_pdf_file_path(self, task_id: str) -> Optional[str]:
        """Get PDF file path for download"""
        task = self.tasks.get(task_id)
        if task and task.get("status") == "completed":
            return task.get("file_path")
        return None
    
    def cleanup_old_files(self, max_age_hours: int = 24):
        """Clean up old PDF files"""
        try:
            cutoff_time = datetime.now().timestamp() - (max_age_hours * 3600)
            
            # Clean up files
            for filename in os.listdir(self.output_dir):
                file_path = os.path.join(self.output_dir, filename)
                if os.path.isfile(file_path):
                    if os.path.getmtime(file_path) < cutoff_time:
                        os.remove(file_path)
                        logger.info(f"Cleaned up old PDF file: {filename}")
            
            # Clean up old tasks
            old_tasks = [
                task_id for task_id, task in self.tasks.items()
                if task.get("created_at", datetime.now()).timestamp() < cutoff_time
            ]
            
            for task_id in old_tasks:
                del self.tasks[task_id]
            
            logger.info(f"Cleaned up {len(old_tasks)} old tasks")
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")


# Global instance
pdf_service = PDFGenerationService()