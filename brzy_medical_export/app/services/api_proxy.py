"""
API proxy service for calling original backend
"""
import httpx
import json
import logging
from typing import Optional, Dict, Any, List
from datetime import datetime

from app.config import settings
from app.services.cache_service import CacheService
from app.utils.security import token_encryption

logger = logging.getLogger(__name__)


class APIProxyService:
    """Proxy service for calling original backend APIs"""
    
    def __init__(self, cache_service: Optional[CacheService] = None):
        self.base_url = settings.original_api_url
        self.timeout = settings.original_api_timeout
        self.cache = cache_service or CacheService()
    
    async def _make_request(
        self,
        method_code: str,
        session_token: str,
        user_id: str,
        params: Optional[Dict[str, Any]] = None,
        device_info: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Make request to original backend
        
        Args:
            method_code: API method code
            session_token: iOS session token
            user_id: User ID
            params: Additional parameters
            device_info: iOS device information from session
            
        Returns:
            Response data
        """
        # Decrypt the session token before using it
        from app.utils.security import token_encryption
        
        # Try to decrypt the session token (it might be encrypted)
        decrypted_token = token_encryption.decrypt_token(session_token)
        if decrypted_token:
            actual_session_token = decrypted_token
            logger.debug(f"Using decrypted session token for API call")
        else:
            # If decryption fails, use the token as-is (might already be plain)
            actual_session_token = session_token
            logger.debug(f"Using session token as-is for API call")
        
        # Use device info from iOS session if available, otherwise use defaults
        if device_info:
            app_version = device_info.get("app_version", "4.6.3")
            # Get device identifiers from device_info
            imei = device_info.get("imei") or device_info.get("device_id", "")
            mac = device_info.get("mac") or device_info.get("device_id", "")
            
            # Check if device identifiers are truncated and fix them
            # iOS UUIDs should be 36 characters long (8-4-4-4-12 format)
            if not imei or len(imei) < 36:
                if imei:
                    logger.warning(f"IMEI truncated or invalid: {imei} (length: {len(imei)}), using default")
                imei = "60BA14A4-58A0-4E3F-831C-F4B0F62EB8D4"
            
            if not mac or len(mac) < 36:
                if mac:
                    logger.warning(f"MAC truncated or invalid: {mac} (length: {len(mac)}), using default")
                mac = "0F535656-8B87-4A08-825E-6654D2F32344"
                
            logger.debug(f"Using device info from session: {device_info}")
            logger.debug(f"Final IMEI: {imei}, MAC: {mac}")
        else:
            app_version = "4.6.3"
            # Use iOS-like device identifiers as defaults
            imei = "60BA14A4-58A0-4E3F-831C-F4B0F62EB8D4"
            mac = "0F535656-8B87-4A08-825E-6654D2F32344"
            logger.debug("No device info available, using iOS-like defaults")
        
        request_data = {
            "method_code": method_code,
            "userId": user_id,
            "sessionToken": actual_session_token,
            # iOS端必需的通用参数 - 使用来自iOS会话的实际设备信息
            "app_version": app_version,
            "imei": imei,  # 使用iOS设备IMEI
            "mac": mac,    # 使用iOS设备MAC
            "sys_type": "2",    # 2表示iOS
            "userType": "1",    # 固定值
            "terminalType": "biran"  # 固定值，必然中医
        }
        
        if params:
            request_data.update(params)
        
        # Debug logging - log complete request details
        logger.debug("="*80)
        logger.debug(f"API Request Details for method: {method_code}")
        logger.debug(f"Base URL: {self.base_url}")
        logger.debug(f"User ID: {user_id}")
        logger.debug(f"Session Token (first 10 chars): {session_token[:10]}...")
        logger.debug(f"Additional params: {json.dumps(params, ensure_ascii=False, indent=2) if params else 'None'}")
        logger.debug(f"Complete request data: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
        logger.debug("="*80)
        
        logger.info(f"Calling API: {method_code} for user: {user_id}")
        
        try:
            headers = {
                "User-Agent": "BRZY-Medical-Export/1.0"
            }
            
            # Log headers
            logger.debug(f"Request headers: {json.dumps(headers, indent=2)}")
            
            # Convert to URL query parameters (like iOS does)
            query_params = {}
            for key, value in request_data.items():
                query_params[key] = str(value)
            
            # Add doctorId (same as userId, required by iOS API)
            query_params["doctorId"] = user_id
            
            # Add tel parameter if available from device_info or use default
            if device_info and device_info.get("tel"):
                query_params["tel"] = device_info.get("tel")
            else:
                # Use a default tel or extract from user data if available
                query_params["tel"] = "88025668998"  # Using the same as iOS example for now
            
            logger.debug(f"URL query parameters: {json.dumps(query_params, ensure_ascii=False, indent=2)}")
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(
                    self.base_url,
                    params=query_params,
                    headers=headers
                )
                
                # Log response details
                logger.debug(f"Response status code: {response.status_code}")
                logger.debug(f"Response headers: {dict(response.headers)}")
                
                response.raise_for_status()
                data = response.json()
                
                # Log response data
                logger.debug(f"Response data: {json.dumps(data, ensure_ascii=False, indent=2)}")
                
                # Handle null response
                if data is None:
                    logger.warning(f"API {method_code} returned null response")
                    return {}
                
                # Check if response is successful
                # iOS API uses "0000" for success, not "000000"
                if data.get("code") == "0000":
                    logger.debug("API call successful")
                    # Some APIs return data directly, others under "data" key
                    if "data" in data:
                        return data.get("data", {})
                    else:
                        # For APIs that return data directly
                        # Remove code and errorMsg from response
                        result = {k: v for k, v in data.items() if k not in ["code", "errorMsg"]}
                        return result if result else data
                else:
                    error_msg = data.get("errorMsg", "Unknown error")
                    error_code = data.get("code", "Unknown code")
                    logger.error(f"API error - Code: {error_code}, Message: {error_msg}")
                    logger.error(f"Full error response: {json.dumps(data, ensure_ascii=False, indent=2)}")
                    raise Exception(f"API error: {error_msg}")
                    
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error: {e}")
            logger.error(f"Response text: {e.response.text if hasattr(e, 'response') else 'No response text'}")
            raise
        except Exception as e:
            logger.error(f"Request error: {e}")
            logger.error(f"Error type: {type(e).__name__}")
            raise
    
    
    
    async def get_prescription_list(
        self,
        session_token: str,
        user_id: str,
        patient_id: str,
        page: int = 1,
        size: int = 20,
        device_info: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Get prescription list for a patient"""
        params = {
            "patientId": patient_id,
            "page": page,
            "pageSize": size
        }
        
        data = await self._make_request(
            method_code="000117",  # 获取处方列表
            session_token=session_token,
            user_id=user_id,
            params=params,
            device_info=device_info
        )
        
        return data.get("list", [])
    
    async def get_prescription_detail(
        self,
        session_token: str,
        user_id: str,
        prescription_id: str,
        device_info: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Get prescription detail"""
        # Check cache
        cache_key = f"prescription:{user_id}:{prescription_id}"
        cached = await self.cache.get(cache_key)
        if cached:
            return json.loads(cached)
        
        data = await self._make_request(
            method_code="000118",  # 获取处方详情
            session_token=session_token,
            user_id=user_id,
            params={"prescriptionId": prescription_id},
            device_info=device_info
        )
        
        # Cache for 24 hours (prescription data doesn't change)
        await self.cache.setex(cache_key, 86400, json.dumps(data))
        
        return data
    
    async def get_wzd_list(
        self,
        session_token: str,
        user_id: str,
        patient_id: str,
        device_info: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Get consultation form (问诊单) list"""
        data = await self._make_request(
            method_code="000120",  # 获取问诊单列表
            session_token=session_token,
            user_id=user_id,
            params={"patientId": patient_id},
            device_info=device_info
        )
        
        return data.get("list", [])
    
    async def get_chat_messages(
        self,
        session_token: str,
        user_id: str,
        patient_id: str,
        last_message_id: Optional[str] = None,
        limit: int = 50,
        device_info: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Get chat messages"""
        params = {
            "sessionId": patient_id,
            "limit": limit
        }
        
        if last_message_id:
            params["lastMessageId"] = last_message_id
        
        data = await self._make_request(
            method_code="000202",  # 获取聊天记录
            session_token=session_token,
            user_id=user_id,
            params=params,
            device_info=device_info
        )
        
        return data.get("messages", [])
    
    async def get_medical_records_summary(
        self,
        session_token: str,
        user_id: str,
        patient_id: str,
        device_info: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Get medical records summary for a patient"""
        # Get medical case list using the correct API
        # 注意：000233 接口需要 patientId 参数，还需要 page 和 pageSize
        params = {
            "patientId": patient_id,
            "page": 1,
            "pageSize": 20
        }
        
        data = await self._make_request(
            method_code="000233",  # 获取医案列表
            session_token=session_token,
            user_id=user_id,
            params=params,
            device_info=device_info
        )
        
        # Handle empty or null response
        if not data:
            logger.warning(f"Empty response from 000233 API for patient {patient_id}")
            return {
                "medical_cases": [],
                "prescriptions": [],
                "wzd_records": [],
                "statistics": {
                    "total_cases": 0,
                    "total_prescriptions": 0,
                    "total_wzd": 0,
                    "last_visit": None
                }
            }
        
        # Process the medical case list - 000233 returns "consilias" not "list"
        consilias = data.get("consilias", []) if isinstance(data, dict) else []
        
        results = {
            "medical_cases": consilias,
            "prescriptions": [],  # Will be populated from medical cases
            "wzd_records": [],    # Will be populated from medical cases
            "statistics": {
                "total_cases": len(consilias),
                "total_prescriptions": 0,
                "total_wzd": 0,
                "last_visit": None
            }
        }
        
        # Process medical cases based on type
        # type: "1" = 问诊单, "3" = 处方
        for case in consilias:
            case_type = case.get("type", "")
            
            # Extract prescription info (type = "3")
            if case_type == "3":
                prescription = {
                    "id": case.get("id"),
                    "preName": case.get("symptom", "处方"),
                    "createTime": case.get("createTime"),
                    "doctorName": "",  # Not provided in 000233 response
                    "diagnosis": case.get("symptom", ""),
                    "status": "completed"
                }
                results["prescriptions"].append(prescription)
                results["statistics"]["total_prescriptions"] += 1
            
            # Extract wzd info (type = "1")
            elif case_type == "1":
                wzd = {
                    "id": case.get("id"),
                    "chiefComplaint": case.get("symptom", "问诊单"),
                    "createTime": case.get("createTime"),
                    "doctorName": "",  # Not provided in 000233 response
                    "presentIllness": case.get("symptom", ""),
                    "status": "completed",
                    "url": case.get("url", "")
                }
                results["wzd_records"].append(wzd)
                results["statistics"]["total_wzd"] += 1
        
        # Find last visit date from medical cases
        all_dates = []
        for case in consilias:
            if case.get("createTime"):
                all_dates.append(case["createTime"])
        
        if all_dates:
            results["statistics"]["last_visit"] = max(all_dates)
        
        return results
    
    async def get_medical_record_detail(
        self,
        session_token: str,
        user_id: str,
        record_id: str,
        device_info: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Get medical record detail using 000228 API"""
        # 注意：000228 接口需要 case_id 参数
        params = {
            "case_id": record_id
        }
        
        data = await self._make_request(
            method_code="000228",  # 获取医案详情
            session_token=session_token,
            user_id=user_id,
            params=params,
            device_info=device_info
        )
        
        return data
    
    async def get_prescription_order_detail(
        self,
        session_token: str,
        user_id: str,
        order_id: str,
        device_info: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Get prescription order detail (用药详情) using 000228 API"""
        # 尝试不同的参数名称，因为接口文档可能不准确
        # 先尝试 orderId，如果返回null再尝试其他参数名
        
        # 第一次尝试：使用 orderId 参数
        params = {
            "orderId": order_id
        }
        
        # Add tel parameter if available from device_info
        if device_info and device_info.get("tel"):
            params["tel"] = device_info.get("tel")
        else:
            params["tel"] = "88025668998"  # Default value from iOS example
        
        try:
            data = await self._make_request(
                method_code="000228",  # 获取用药详情
                session_token=session_token,
                user_id=user_id,
                params=params,
                device_info=device_info
            )
            
            # 如果返回有效数据，直接返回
            if data and data != {}:
                return data
                
        except Exception as e:
            logger.warning(f"First attempt with orderId failed: {e}")
        
        # 第二次尝试：使用 prescriptionId 参数
        try:
            params_alt = {
                "prescriptionId": order_id
            }
            if device_info and device_info.get("tel"):
                params_alt["tel"] = device_info.get("tel")
            else:
                params_alt["tel"] = "88025668998"
                
            logger.info(f"Trying 000228 with prescriptionId parameter for order: {order_id}")
            
            data = await self._make_request(
                method_code="000228",
                session_token=session_token,
                user_id=user_id,
                params=params_alt,
                device_info=device_info
            )
            
            if data and data != {}:
                return data
                
        except Exception as e:
            logger.warning(f"Second attempt with prescriptionId failed: {e}")
        
        # 第三次尝试：使用其他可能的参数名
        try:
            params_alt2 = {
                "id": order_id
            }
            if device_info and device_info.get("tel"):
                params_alt2["tel"] = device_info.get("tel")
            else:
                params_alt2["tel"] = "88025668998"
                
            logger.info(f"Trying 000228 with id parameter for order: {order_id}")
            
            data = await self._make_request(
                method_code="000228",
                session_token=session_token,
                user_id=user_id,
                params=params_alt2,
                device_info=device_info
            )
            
            if data and data != {}:
                return data
                
        except Exception as e:
            logger.warning(f"Third attempt with id failed: {e}")
        
        # 如果所有尝试都失败，返回错误信息
        logger.error(f"All attempts to get prescription detail failed for order: {order_id}")
        raise Exception(f"Unable to get prescription detail for order: {order_id}. API returned null or empty response.")