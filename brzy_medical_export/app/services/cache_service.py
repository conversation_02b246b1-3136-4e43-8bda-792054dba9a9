"""
Redis cache service for caching data
"""
import json
import logging
from typing import Optional, Any, List
import redis.asyncio as aioredis
from redis.asyncio import Redis
from datetime import timed<PERSON><PERSON>

from app.config import settings

logger = logging.getLogger(__name__)


class CacheService:
    """Redis cache service"""
    
    def __init__(self):
        self._redis: Optional[Redis] = None
    
    async def get_redis(self) -> Redis:
        """Get Redis connection"""
        if not self._redis:
            self._redis = aioredis.from_url(
                settings.redis_url,
                password=settings.redis_password,
                encoding="utf-8",
                decode_responses=True
            )
        return self._redis
    
    async def get(self, key: str) -> Optional[str]:
        """Get value from cache"""
        try:
            redis = await self.get_redis()
            value = await redis.get(key)
            return value
        except Exception as e:
            logger.error(f"Cache get error for key {key}: {e}")
            return None
    
    async def set(
        self,
        key: str,
        value: Any,
        expire: Optional[int] = None
    ) -> bool:
        """Set value in cache"""
        try:
            redis = await self.get_redis()
            if isinstance(value, (dict, list)):
                value = json.dumps(value)
            
            if expire:
                await redis.setex(key, expire, value)
            else:
                await redis.set(key, value)
            return True
        except Exception as e:
            logger.error(f"Cache set error for key {key}: {e}")
            return False
    
    async def setex(self, key: str, seconds: int, value: Any) -> bool:
        """Set value with expiration"""
        return await self.set(key, value, expire=seconds)
    
    async def delete(self, key: str) -> bool:
        """Delete key from cache"""
        try:
            redis = await self.get_redis()
            result = await redis.delete(key)
            return result > 0
        except Exception as e:
            logger.error(f"Cache delete error for key {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists"""
        try:
            redis = await self.get_redis()
            return await redis.exists(key) > 0
        except Exception as e:
            logger.error(f"Cache exists error for key {key}: {e}")
            return False
    
    async def expire(self, key: str, seconds: int) -> bool:
        """Set expiration for key"""
        try:
            redis = await self.get_redis()
            return await redis.expire(key, seconds)
        except Exception as e:
            logger.error(f"Cache expire error for key {key}: {e}")
            return False
    
    async def ttl(self, key: str) -> int:
        """Get time to live for key"""
        try:
            redis = await self.get_redis()
            return await redis.ttl(key)
        except Exception as e:
            logger.error(f"Cache ttl error for key {key}: {e}")
            return -1
    
    async def hset(self, name: str, key: str, value: Any) -> bool:
        """Set hash field"""
        try:
            redis = await self.get_redis()
            if isinstance(value, (dict, list)):
                value = json.dumps(value)
            await redis.hset(name, key, value)
            return True
        except Exception as e:
            logger.error(f"Cache hset error for {name}:{key}: {e}")
            return False
    
    async def hget(self, name: str, key: str) -> Optional[str]:
        """Get hash field"""
        try:
            redis = await self.get_redis()
            return await redis.hget(name, key)
        except Exception as e:
            logger.error(f"Cache hget error for {name}:{key}: {e}")
            return None
    
    async def hgetall(self, name: str) -> dict:
        """Get all hash fields"""
        try:
            redis = await self.get_redis()
            return await redis.hgetall(name)
        except Exception as e:
            logger.error(f"Cache hgetall error for {name}: {e}")
            return {}
    
    async def hdel(self, name: str, *keys: str) -> int:
        """Delete hash fields"""
        try:
            redis = await self.get_redis()
            return await redis.hdel(name, *keys)
        except Exception as e:
            logger.error(f"Cache hdel error for {name}: {e}")
            return 0
    
    async def lpush(self, key: str, *values: Any) -> int:
        """Push values to list"""
        try:
            redis = await self.get_redis()
            return await redis.lpush(key, *values)
        except Exception as e:
            logger.error(f"Cache lpush error for {key}: {e}")
            return 0
    
    async def lrange(self, key: str, start: int, stop: int) -> List[str]:
        """Get list range"""
        try:
            redis = await self.get_redis()
            return await redis.lrange(key, start, stop)
        except Exception as e:
            logger.error(f"Cache lrange error for {key}: {e}")
            return []
    
    async def close(self):
        """Close Redis connection"""
        if self._redis:
            await self._redis.close()
            self._redis = None
    
    # Convenience methods for specific use cases
    
    async def cache_patient_data(
        self,
        user_id: str,
        patient_id: str,
        data: dict,
        expire_hours: int = 24
    ) -> bool:
        """Cache patient data"""
        key = f"patient:{user_id}:{patient_id}"
        expire_seconds = expire_hours * 3600
        return await self.setex(key, expire_seconds, data)
    
    async def get_patient_data(
        self,
        user_id: str,
        patient_id: str
    ) -> Optional[dict]:
        """Get cached patient data"""
        key = f"patient:{user_id}:{patient_id}"
        data = await self.get(key)
        if data:
            try:
                return json.loads(data)
            except json.JSONDecodeError:
                return None
        return None
    
    async def cache_medical_record(
        self,
        user_id: str,
        patient_id: str,
        record_id: str,
        record_type: str,
        data: dict,
        expire_hours: int = 48
    ) -> bool:
        """Cache medical record"""
        key = f"medical:{user_id}:{patient_id}:{record_type}:{record_id}"
        expire_seconds = expire_hours * 3600
        return await self.setex(key, expire_seconds, data)
    
    async def get_medical_record(
        self,
        user_id: str,
        patient_id: str,
        record_id: str,
        record_type: str
    ) -> Optional[dict]:
        """Get cached medical record"""
        key = f"medical:{user_id}:{patient_id}:{record_type}:{record_id}"
        data = await self.get(key)
        if data:
            try:
                return json.loads(data)
            except json.JSONDecodeError:
                return None
        return None