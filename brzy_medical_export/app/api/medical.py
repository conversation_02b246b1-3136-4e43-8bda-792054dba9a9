"""
Medical records API endpoints
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Optional, List
from datetime import datetime
import logging

from app.database import get_db
from app.schemas.medical import (
    MedicalRecordListResponse,
    MedicalRecordDetail,
    MedicalExportRequest,
    MedicalExportResponse,
    MedicalRecordSummary,
    PrescriptionRecord,
    MedicineItem,
    PrescriptionOrderDetail,
    PrescriptionOrderDetailResponse,
    PrescriptionDetailItem
)
from app.models.medical import RecordType
from app.utils.auth import get_current_session
from app.models.auth import AuthSession
from app.services.api_proxy import APIProxyService
from app.services.cache_service import CacheService

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/{patient_id}", response_model=MedicalRecordListResponse)
async def get_medical_records(
    patient_id: str,
    record_type: Optional[RecordType] = Query(None, description="Filter by record type"),
    start_date: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    session: AuthSession = Depends(get_current_session),
    db: Session = Depends(get_db)
):
    """
    Get medical records for a specific patient
    
    This endpoint returns all medical records (prescriptions, consultations, etc.)
    for the specified patient, with optional filtering.
    """
    try:
        api_proxy = APIProxyService()
        
        # Parse device info from session if available
        device_info = None
        if session.device_info:
            import json
            try:
                device_info = json.loads(session.device_info)
            except:
                device_info = None
        
        # Get medical records summary
        summary = await api_proxy.get_medical_records_summary(
            session_token=session.session_token,
            user_id=session.user_id,
            patient_id=patient_id,
            device_info=device_info
        )
        
        records = []
        
        # Process prescriptions
        if not record_type or record_type == RecordType.PRESCRIPTION:
            for pres in summary.get("prescriptions", []):
                # Parse date string to datetime
                date_str = pres.get("createTime")
                record_date = None
                if date_str:
                    try:
                        record_date = datetime.strptime(date_str, "%Y-%m-%d")
                    except:
                        pass
                
                record = MedicalRecordSummary(
                    record_id=pres.get("id", ""),
                    record_type=RecordType.PRESCRIPTION,
                    title=pres.get("preName", "处方"),
                    record_date=record_date,
                    doctor_name=pres.get("doctorName", ""),
                    summary=pres.get("diagnosis", ""),
                    status=pres.get("status", "")
                )
                records.append(record)
        
        # Process consultation forms
        if not record_type or record_type == RecordType.WZD:
            for wzd in summary.get("wzd_records", []):
                # Parse date string to datetime
                date_str = wzd.get("createTime")
                record_date = None
                if date_str:
                    try:
                        record_date = datetime.strptime(date_str, "%Y-%m-%d")
                    except:
                        pass
                
                record = MedicalRecordSummary(
                    record_id=wzd.get("id", ""),
                    record_type=RecordType.WZD,
                    title=wzd.get("chiefComplaint", "问诊单"),
                    record_date=record_date,
                    doctor_name=wzd.get("doctorName", ""),
                    summary=wzd.get("presentIllness", ""),
                    status=wzd.get("status", "")
                )
                records.append(record)
        
        # Filter by date if provided
        if start_date or end_date:
            filtered_records = []
            for record in records:
                if record.record_date:
                    record_date_str = record.record_date.strftime("%Y-%m-%d")
                    if start_date and record_date_str < start_date:
                        continue
                    if end_date and record_date_str > end_date:
                        continue
                    filtered_records.append(record)
            records = filtered_records
        
        # Sort by date descending
        records.sort(key=lambda x: x.record_date or datetime.min, reverse=True)
        
        return MedicalRecordListResponse(
            code=200,
            message="Success",
            data=records,
            total=len(records),
            statistics=summary.get("statistics", {})
        )
        
    except Exception as e:
        logger.error(f"Error getting medical records: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/prescription/{order_id}", response_model=PrescriptionOrderDetailResponse)
async def get_prescription_order_detail(
    order_id: str,
    session: AuthSession = Depends(get_current_session),
    db: Session = Depends(get_db)
):
    """
    Get prescription order detail (用药详情)
    
    This endpoint returns the complete details of a prescription order,
    including all medicines, prices, and delivery information.
    """
    try:
        api_proxy = APIProxyService()
        
        # Parse device info from session if available
        device_info = None
        if session.device_info:
            import json
            try:
                device_info = json.loads(session.device_info)
            except:
                device_info = None
        
        # Get prescription order detail
        detail = await api_proxy.get_prescription_order_detail(
            session_token=session.session_token,
            user_id=session.user_id,
            order_id=order_id,
            device_info=device_info
        )
        
        # Transform the response to match our schema
        prescription_detail = PrescriptionOrderDetail(**detail)
        
        return PrescriptionOrderDetailResponse(
            code=200,
            message="Success",
            data=prescription_detail
        )
        
    except Exception as e:
        logger.error(f"Error getting prescription order detail: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{patient_id}/{record_id}", response_model=MedicalRecordDetail)
async def get_medical_record_detail(
    patient_id: str,
    record_id: str,
    session: AuthSession = Depends(get_current_session),
    db: Session = Depends(get_db)
):
    """
    Get detailed information for a specific medical record
    
    This endpoint returns the complete details of a medical record,
    including all associated data.
    """
    try:
        api_proxy = APIProxyService()
        
        # Parse device info from session if available
        device_info = None
        if session.device_info:
            import json
            try:
                device_info = json.loads(session.device_info)
            except:
                device_info = None
        
        # Get medical record detail
        detail = await api_proxy.get_medical_record_detail(
            session_token=session.session_token,
            user_id=session.user_id,
            record_id=record_id,
            device_info=device_info
        )
        
        # Transform the response to match our schema
        return MedicalRecordDetail(
            code=200,
            message="Success",
            data=detail
        )
        
    except Exception as e:
        logger.error(f"Error getting medical record detail: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/export", response_model=MedicalExportResponse)
async def export_medical_records(
    request: MedicalExportRequest,
    session: AuthSession = Depends(get_current_session),
    db: Session = Depends(get_db)
):
    """
    Export medical records to PDF or other formats
    
    This endpoint initiates an export task for the specified medical records.
    The actual file generation is handled asynchronously.
    """
    # TODO: Implement export logic
    return MedicalExportResponse(
        code=200,
        message="Export task created",
        task_id="task_123456",
        status="processing"
    )