"""
PDF generation API endpoints
"""
import os
from fastapi import APIRouter, Depends, HTTPException, Response, Request
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from typing import Optional, List
import logging

from app.database import get_db
from app.utils.auth import get_current_session
from app.models.auth import AuthSession
from app.services.pdf_service import pdf_service
from app.services.auth_service import AuthService

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/generate")
async def generate_pdf(
    request: Request,
    patient_id: str,
    medical_records: Optional[List[dict]] = None,
    session: AuthSession = Depends(get_current_session),
    db: Session = Depends(get_db)
):
    """
    Generate PDF for patient medical records
    
    This endpoint generates a PDF file containing the patient's
    medical records and returns a task ID for tracking.
    """
    try:
        # Get decrypted session token
        auth_service = AuthService(db)
        session_token = await auth_service.get_decrypted_token(session.session_id)
        
        if not session_token:
            raise HTTPException(status_code=401, detail="Invalid session")
        
        # Start PDF generation
        task_id = await pdf_service.generate_medical_record_pdf(
            patient_id=patient_id,
            session_token=session_token,
            medical_records=medical_records
        )
        
        logger.info(f"PDF generation started for patient {patient_id}, task {task_id}")
        
        return {
            "code": 200,
            "message": "PDF generation started",
            "data": {
                "task_id": task_id,
                "status": "processing",
                "status_url": f"/api/pdf/status/{task_id}",
                "download_url": f"/api/pdf/download/{task_id}"
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to start PDF generation: {e}")
        raise HTTPException(status_code=500, detail="Failed to start PDF generation")


@router.get("/download/{task_id}")
async def download_pdf(
    task_id: str,
    session: AuthSession = Depends(get_current_session)
):
    """
    Download generated PDF
    
    This endpoint allows downloading a previously generated PDF file.
    """
    try:
        file_path = pdf_service.get_pdf_file_path(task_id)
        
        if not file_path or not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="PDF not found or not ready")
        
        # Get task info for filename
        task = pdf_service.get_task_status(task_id)
        if task:
            if task.get("type") == "prescription":
                order_id = task.get("order_id", "unknown")
                filename = f"prescription_{order_id}.pdf"
            else:
                patient_id = task.get("patient_id", "unknown")
                filename = f"medical_record_{patient_id}.pdf"
        else:
            filename = f"document_{task_id}.pdf"
        
        return FileResponse(
            path=file_path,
            filename=filename,
            media_type="application/pdf",
            headers={
                "Content-Disposition": f"attachment; filename={filename}"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to download PDF {task_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to download PDF")


@router.get("/status/{task_id}")
async def get_pdf_status(
    task_id: str,
    session: AuthSession = Depends(get_current_session)
):
    """
    Check PDF generation status
    
    This endpoint returns the current status of a PDF generation task.
    """
    try:
        task = pdf_service.get_task_status(task_id)
        
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        
        status_data = {
            "task_id": task_id,
            "status": task["status"],
            "created_at": task["created_at"].isoformat()
        }
        
        # Add patient_id for medical record tasks or order_id for prescription tasks
        if "patient_id" in task:
            status_data["patient_id"] = task["patient_id"]
        if "order_id" in task:
            status_data["order_id"] = task["order_id"]
        if "type" in task:
            status_data["type"] = task["type"]
        
        if task["status"] == "completed":
            status_data["download_url"] = f"/api/pdf/download/{task_id}"
            if "completed_at" in task:
                status_data["completed_at"] = task["completed_at"].isoformat()
        
        elif task["status"] == "failed":
            status_data["error"] = task.get("error", "Unknown error")
            if "failed_at" in task:
                status_data["failed_at"] = task["failed_at"].isoformat()
        
        return {
            "code": 200,
            "message": "Task status retrieved",
            "data": status_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get PDF status {task_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get task status")


@router.post("/generate-batch")
async def generate_batch_pdf(
    request: Request,
    patient_ids: List[str],
    session: AuthSession = Depends(get_current_session),
    db: Session = Depends(get_db)
):
    """
    Generate PDF for multiple patients' medical records
    
    This endpoint generates a combined PDF file containing medical records
    for multiple patients and returns a task ID for tracking.
    """
    try:
        # Get decrypted session token
        auth_service = AuthService(db)
        session_token = await auth_service.get_decrypted_token(session.session_id)
        
        if not session_token:
            raise HTTPException(status_code=401, detail="Invalid session")
        
        # For batch generation, we'll create a combined task
        # This could be enhanced to support truly parallel processing
        task_ids = []
        for patient_id in patient_ids:
            task_id = await pdf_service.generate_medical_record_pdf(
                patient_id=patient_id,
                session_token=session_token
            )
            task_ids.append(task_id)
        
        logger.info(f"Batch PDF generation started for {len(patient_ids)} patients")
        
        return {
            "code": 200,
            "message": f"Batch PDF generation started for {len(patient_ids)} patients",
            "data": {
                "task_ids": task_ids,
                "total_patients": len(patient_ids),
                "status": "processing"
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to start batch PDF generation: {e}")
        raise HTTPException(status_code=500, detail="Failed to start batch PDF generation")


@router.post("/generate-prescription")
async def generate_prescription_pdf(
    request: Request,
    session: AuthSession = Depends(get_current_session),
    db: Session = Depends(get_db)
):
    """
    Generate PDF for a single prescription order
    
    This endpoint generates a PDF file containing the prescription details
    and returns a task ID for tracking.
    """
    try:
        # Get request body
        body = await request.json()
        order_id = body.get('order_id')
        
        if not order_id:
            raise HTTPException(status_code=400, detail="order_id is required")
        
        # Get decrypted session token
        auth_service = AuthService(db)
        session_token = await auth_service.get_decrypted_token(session)
        
        if not session_token:
            raise HTTPException(status_code=401, detail="Invalid session")
        
        # Parse device info from session if available
        device_info = None
        if session.device_info:
            import json
            try:
                device_info = json.loads(session.device_info)
            except:
                device_info = None
        
        # Start PDF generation for prescription
        task_id = await pdf_service.generate_prescription_pdf(
            order_id=order_id,
            session_token=session_token,
            user_id=session.user_id,
            device_info=device_info
        )
        
        logger.info(f"Prescription PDF generation started for order {order_id}, task {task_id}")
        
        return {
            "code": 200,
            "message": "PDF generation started",
            "data": {
                "task_id": task_id,
                "status": "processing",
                "status_url": f"/api/pdf/status/{task_id}",
                "download_url": f"/api/pdf/download/{task_id}"
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to start prescription PDF generation: {e}")
        raise HTTPException(status_code=500, detail="Failed to start PDF generation")


@router.post("/cleanup")
async def cleanup_old_files(
    max_age_hours: int = 24,
    session: AuthSession = Depends(get_current_session)
):
    """
    Clean up old PDF files and tasks
    
    This endpoint cleans up PDF files and tasks older than specified hours.
    """
    try:
        pdf_service.cleanup_old_files(max_age_hours)
        
        return {
            "code": 200,
            "message": f"Cleanup completed for files older than {max_age_hours} hours"
        }
        
    except Exception as e:
        logger.error(f"Failed to cleanup files: {e}")
        raise HTTPException(status_code=500, detail="Failed to cleanup files")