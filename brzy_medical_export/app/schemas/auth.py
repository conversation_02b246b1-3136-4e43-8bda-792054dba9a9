"""
Authentication related schemas
"""
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field, validator
from datetime import datetime
from app.models.auth import AuthStatus


class DeviceInfo(BaseModel):
    """Device information model"""
    app_version: str = Field(..., description="Application version")
    os_version: str = Field(..., description="Operating system version")
    device_model: Optional[str] = Field(None, description="Device model")
    platform: Optional[str] = Field(None, description="Platform (iOS/Android)")
    device_id: Optional[str] = Field(None, description="Device identifier")
    imei: Optional[str] = Field(None, description="Device IMEI")
    mac: Optional[str] = Field(None, description="Device MAC address")
    tel: Optional[str] = Field(None, description="Phone number")


class ScanAuthRequest(BaseModel):
    """Scan authentication request model"""
    auth_code: str = Field(..., description="Authorization code from iOS app")
    session_token: str = Field(..., description="Original session token")
    web_session_id: str = Field(..., description="Web session identifier")
    device_info: DeviceInfo = Field(..., description="Device information")
    current_patient_id: Optional[str] = Field(None, description="Current patient ID if in chat")
    current_patient_name: Optional[str] = Field(None, description="Current patient name if in chat")
    
    @validator("auth_code")
    def validate_auth_code(cls, v):
        """Validate auth code format"""
        parts = v.split("_")
        if len(parts) < 3:
            raise ValueError("Invalid auth code format")
        # Validate timestamp (first part should be a valid timestamp)
        try:
            timestamp = float(parts[0])
            # Check if timestamp is within reasonable range (not more than 5 minutes old)
            import time
            current_time = time.time() * 1000  # Use time.time() instead of datetime.utcnow().timestamp()
            if abs(current_time - timestamp) > 300000:  # 5 minutes in milliseconds
                raise ValueError("Auth code expired")
        except ValueError as e:
            if "could not convert string to float" in str(e):
                raise ValueError("Invalid timestamp in auth code")
            raise
        return v


class ScanAuthResponse(BaseModel):
    """Scan authentication response model"""
    code: int = Field(default=200, description="Response code")
    message: str = Field(default="Authorization successful", description="Response message")
    session_id: str = Field(..., description="Session ID for web access")
    expires_in: Optional[int] = Field(None, description="Session expiration time in seconds")


class AuthStatusResponse(BaseModel):
    """Authentication status response model"""
    auth_code: str = Field(..., description="Authorization code")
    status: AuthStatus = Field(..., description="Current status")
    temp_token: Optional[str] = Field(None, description="Temporary token if authorized")
    user_info: Optional[Dict[str, Any]] = Field(None, description="User information if authorized")
    expires_at: Optional[datetime] = Field(None, description="Token expiration time")


class RefreshTokenRequest(BaseModel):
    """Refresh token request model"""
    temp_token: str = Field(..., description="Current temporary token")


class RefreshTokenResponse(BaseModel):
    """Refresh token response model"""
    code: int = Field(default=200, description="Response code")
    message: str = Field(default="Token refreshed", description="Response message")
    new_token: str = Field(..., description="New temporary token")
    expires_in: int = Field(..., description="Token expiration time in seconds")


class WebSocketAuth(BaseModel):
    """WebSocket authentication message"""
    type: str = Field(default="auth", description="Message type")
    auth_code: str = Field(..., description="Authorization code")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Message timestamp")


class WebSocketMessage(BaseModel):
    """WebSocket message model"""
    type: str = Field(..., description="Message type")
    data: Dict[str, Any] = Field(default_factory=dict, description="Message data")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Message timestamp")