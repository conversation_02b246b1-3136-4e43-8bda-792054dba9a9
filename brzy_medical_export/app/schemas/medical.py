"""
Medical record related schemas
"""
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from app.models.medical import RecordType


class MedicineItem(BaseModel):
    """Medicine item in prescription"""
    drug_id: str = Field(..., description="Drug ID")
    drug_name: str = Field(..., description="Drug name")
    dose: str = Field(..., description="Dose amount")
    unit: str = Field(..., description="Unit (g/ml/etc)")
    use_method: Optional[str] = Field(None, description="Usage method")
    price: Optional[str] = Field(None, description="Unit price")
    total_price: Optional[str] = Field(None, description="Total price")
    note: Optional[str] = Field(None, description="Special notes")


class PrescriptionRecord(BaseModel):
    """Prescription record model"""
    prescription_id: str = Field(..., description="Prescription ID")
    create_time: datetime = Field(..., description="Creation time")
    patient_name: str = Field(..., description="Patient name")
    diagnosis: str = Field(..., description="Diagnosis")
    prescription_name: Optional[str] = Field(None, description="Prescription name")
    drugs: List[MedicineItem] = Field(default_factory=list, description="Drug list")
    total_amount: str = Field(..., description="Total amount")
    status: str = Field(..., description="Status")
    doctor_name: str = Field(..., description="Doctor name")
    use_type: Optional[str] = Field(None, description="Usage type")
    total_doses: Optional[str] = Field(None, description="Total doses")
    daily_doses: Optional[str] = Field(None, description="Daily doses")
    instructions: Optional[str] = Field(None, description="Instructions")
    contraindication: Optional[str] = Field(None, description="Contraindications")


class WzdRecord(BaseModel):
    """Consultation form (问诊单) record model"""
    wzd_id: str = Field(..., description="WZD ID")
    create_time: datetime = Field(..., description="Creation time")
    chief_complaint: str = Field(..., description="Chief complaint")
    present_illness: Optional[str] = Field(None, description="Present illness")
    past_history: Optional[str] = Field(None, description="Past history")
    questions: List[Dict[str, Any]] = Field(default_factory=list, description="Questions and answers")
    doctor_notes: Optional[str] = Field(None, description="Doctor notes")
    status: str = Field(..., description="Status")


class ChatMessage(BaseModel):
    """Chat message model"""
    message_id: str = Field(..., description="Message ID")
    from_user: str = Field(..., description="Sender ID")
    to_user: str = Field(..., description="Receiver ID")
    content_type: str = Field(..., description="Content type")
    content: str = Field(..., description="Message content")
    create_time: datetime = Field(..., description="Creation time")
    is_read: bool = Field(default=False, description="Is read")
    extra_data: Optional[Dict[str, Any]] = Field(None, description="Extra data")


class MedicalRecordSummary(BaseModel):
    """Medical record summary model"""
    record_id: str = Field(..., description="Record ID")
    record_type: RecordType = Field(..., description="Record type")
    title: str = Field(..., description="Title or chief complaint")
    record_date: Optional[datetime] = Field(None, description="Record date")
    doctor_name: str = Field(..., description="Doctor name")
    summary: Optional[str] = Field(None, description="Content summary")
    status: Optional[str] = Field(None, description="Status")


class MedicalRecordListResponse(BaseModel):
    """Medical record list response"""
    code: int = Field(default=200, description="Response code")
    message: str = Field(default="Success", description="Response message")
    data: List[MedicalRecordSummary] = Field(default_factory=list, description="Record list")
    total: int = Field(default=0, description="Total count")
    statistics: Optional[Dict[str, Any]] = Field(None, description="Statistics by type")


class MedicalRecordDetail(BaseModel):
    """Medical record detail model"""
    record_id: str = Field(..., description="Record ID")
    record_type: RecordType = Field(..., description="Record type")
    patient_id: str = Field(..., description="Patient ID")
    patient_name: str = Field(..., description="Patient name")
    create_time: datetime = Field(..., description="Creation time")
    data: Dict[str, Any] = Field(..., description="Record data")


class MedicalExportRequest(BaseModel):
    """Medical record export request"""
    patient_id: str = Field(..., description="Patient ID")
    record_ids: Optional[List[str]] = Field(None, description="Specific record IDs to export")
    record_types: Optional[List[RecordType]] = Field(None, description="Record types to include")
    date_range: Optional[Dict[str, str]] = Field(None, description="Date range filter")
    include_images: bool = Field(default=True, description="Include images")
    include_prescriptions: bool = Field(default=True, description="Include prescriptions")
    include_messages: bool = Field(default=False, description="Include chat messages")
    format: str = Field(default="pdf", description="Export format (pdf/word)")


class MedicalExportResponse(BaseModel):
    """Medical record export response"""
    code: int = Field(default=200, description="Response code")
    message: str = Field(default="Success", description="Response message")
    task_id: str = Field(..., description="Export task ID")
    status: str = Field(..., description="Task status")
    download_url: Optional[str] = Field(None, description="Download URL when ready")


class PrescriptionDetailItem(BaseModel):
    """Prescription medicine detail item"""
    id: int = Field(..., description="Medicine ID")
    name: str = Field(..., description="Medicine name")
    amount: float = Field(..., description="Medicine amount")
    unit: str = Field(..., description="Unit (g/ml/etc)")
    totalPrice: str = Field(..., description="Total price")
    type: Optional[str] = Field("", description="Medicine type")


class PrescriptionOrderDetail(BaseModel):
    """Prescription order detail response (用药详情)"""
    id: int = Field(..., description="Order ID")
    patientId: int = Field(..., description="Patient ID")
    patientName: str = Field(..., description="Patient name")
    patientAge: int = Field(..., description="Patient age")
    patientMobile: str = Field(..., description="Patient mobile")
    age: int = Field(..., description="Age")
    sex: int = Field(..., description="Sex (1=male, 2=female)")
    createdTime: str = Field(..., description="Created time")
    orderStatus: str = Field(..., description="Order status")
    orderType: str = Field(..., description="Order type")
    payStatus: str = Field(..., description="Payment status")
    payTime: Optional[str] = Field(None, description="Payment time")
    details: List[PrescriptionDetailItem] = Field(..., description="Medicine details")
    dialectical: str = Field(..., description="Dialectical diagnosis")
    drugForm: str = Field(..., description="Drug form")
    mode: str = Field(..., description="Preparation mode")
    preDays: int = Field(..., description="Prescription days")
    preUsage: int = Field(..., description="Daily usage")
    totalCount: int = Field(..., description="Total packages")
    totalWeight: str = Field(..., description="Total weight")
    amount: str = Field(..., description="Total amount")
    baseDrugPrice: str = Field(..., description="Base drug price")
    makeCost: str = Field(..., description="Making cost")
    medicalServiceFee: str = Field(..., description="Medical service fee")
    consultationFee: str = Field(..., description="Consultation fee")
    carriage: str = Field(..., description="Carriage fee")
    carrier: Optional[str] = Field(None, description="Carrier name")
    deliverySn: Optional[str] = Field(None, description="Delivery SN")
    dcId: Optional[int] = Field(None, description="Distribution center ID")
    dcName: Optional[str] = Field(None, description="Distribution center name")
    reciverAddress: Optional[str] = Field(None, description="Receiver address")
    reciverMobile: Optional[str] = Field(None, description="Receiver mobile")
    orderReciver: Optional[str] = Field(None, description="Order receiver")
    eatTime: Optional[str] = Field(None, description="Eating time")
    eatDays: Optional[str] = Field(None, description="Eating days")
    makeMethod: Optional[str] = Field(None, description="Making method")
    contraindication: Optional[str] = Field(None, description="Contraindication")
    comment: Optional[str] = Field(None, description="Comment")
    remak: Optional[str] = Field(None, description="Remark")
    rosterRemark: Optional[str] = Field(None, description="Roster remark")
    logisticsUrl: Optional[str] = Field(None, description="Logistics URL")
    prescriptionUrl: Optional[str] = Field(None, description="Prescription URL")
    productProviteName: Optional[str] = Field(None, description="Product provider name")
    isSecrecy: int = Field(0, description="Is secrecy")
    isAutoSend: int = Field(0, description="Is auto send")
    ispregnant: int = Field(0, description="Is pregnant")
    userId: str = Field(..., description="User ID")
    userName: Optional[str] = Field("", description="User name")
    source: Optional[str] = Field(None, description="Source")
    type: Optional[str] = Field(None, description="Type")
    takerId: Optional[int] = Field(None, description="Taker ID")
    balanceWeight: Optional[str] = Field(None, description="Balance weight")
    awardPrice: Optional[str] = Field("", description="Award price")
    isDaiJian: Optional[str] = Field("", description="Is DaiJian")
    mcDose: Optional[str] = Field("", description="MC dose")
    mcDoseUnit: Optional[str] = Field("g", description="MC dose unit")
    mcjk: Optional[str] = Field("", description="MCJK")
    mrjc: Optional[str] = Field("", description="MRJC")
    sendAfterDay: Optional[str] = Field("", description="Send after day")
    orderProductUrl: Optional[str] = Field("", description="Order product URL")
    saleProductCount: Optional[str] = Field("", description="Sale product count")
    yspProductName: Optional[str] = Field("", description="YSP product name")
    yspProductImg: Optional[str] = Field("", description="YSP product image")
    yspEffect: Optional[str] = Field("", description="YSP effect")
    yspUnitPrice: Optional[str] = Field("", description="YSP unit price")
    yspSettleExplain: Optional[str] = Field("", description="YSP settle explain")
    patientType: Optional[str] = Field(None, description="Patient type")
    code: Optional[str] = Field("0000", description="Response code")
    errorMsg: Optional[str] = Field("", description="Error message")


class PrescriptionOrderDetailResponse(BaseModel):
    """Prescription order detail API response"""
    code: int = Field(default=200, description="Response code")
    message: str = Field(default="Success", description="Response message")
    data: PrescriptionOrderDetail = Field(..., description="Prescription order detail")