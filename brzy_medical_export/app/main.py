"""
Main FastAPI application entry point
"""
from fastapi import <PERSON><PERSON><PERSON>, Request, WebSocket
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import J<PERSON><PERSON>esponse
from contextlib import asynccontextmanager
import logging
import sys
from datetime import datetime

from app.config import settings
from app.database import init_db, check_database_connection
from app.api import auth, patients, medical, pdf
from app.utils.logger import setup_logging
from app.services.websocket_manager import manager

# Setup logging
setup_logging(settings.log_level, settings.log_file)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan events
    """
    # Startup
    app.state.start_time = datetime.utcnow()
    logger.info(f"Starting {settings.app_name} v{settings.app_version}")
    
    # Initialize database
    try:
        init_db()
        if check_database_connection():
            logger.info("Database connection established")
        else:
            logger.error("Database connection failed")
            sys.exit(1)
    except Exception as e:
        logger.error(f"Failed to initialize application: {e}")
        sys.exit(1)
    
    # Initialize WebSocket manager
    await manager.startup()
    
    logger.info("Application startup completed successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down application")
    await manager.shutdown()


# Create FastAPI application
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    debug=settings.debug,
    lifespan=lifespan,
    docs_url="/api/docs" if settings.debug else None,
    redoc_url="/api/redoc" if settings.debug else None,
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "code": 500,
            "message": "Internal server error",
            "timestamp": datetime.utcnow().isoformat(),
        },
    )


# Health check endpoint
@app.get("/health")
async def health_check():
    """Enhanced health check endpoint for CNB deployment"""
    try:
        # Check database connection
        db_status = check_database_connection()
        
        # Check Redis connection (import here to avoid circular imports)
        redis_status = False
        try:
            from app.services.cache_service import redis_client
            if redis_client:
                await redis_client.ping()
                redis_status = True
        except Exception:
            redis_status = False
        
        # Overall health status
        overall_healthy = db_status and redis_status
        
        return {
            "status": "healthy" if overall_healthy else "unhealthy",
            "timestamp": datetime.utcnow().isoformat(),
            "version": settings.app_version,
            "environment": getattr(settings, 'environment', 'production'),
            "services": {
                "database": "connected" if db_status else "disconnected",
                "redis": "connected" if redis_status else "disconnected",
                "websocket": "ready",
            },
            "uptime": int((datetime.utcnow() - app.state.start_time).total_seconds()) if hasattr(app.state, 'start_time') else 0,
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "timestamp": datetime.utcnow().isoformat(),
            "error": str(e)
        }


# Readiness probe endpoint (for CNB)
@app.get("/ready")
async def readiness_check():
    """Readiness probe for container orchestration"""
    try:
        db_status = check_database_connection()
        if not db_status:
            return JSONResponse(
                status_code=503,
                content={
                    "status": "not_ready",
                    "reason": "database_unavailable",
                    "timestamp": datetime.utcnow().isoformat()
                }
            )
        
        return {
            "status": "ready",
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return JSONResponse(
            status_code=503,
            content={
                "status": "not_ready",
                "reason": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
        )


# Liveness probe endpoint (for CNB)
@app.get("/live")
async def liveness_check():
    """Liveness probe for container orchestration"""
    return {
        "status": "alive",
        "timestamp": datetime.utcnow().isoformat()
    }


# Metrics endpoint (basic)
@app.get("/metrics")
async def metrics():
    """Basic metrics endpoint for monitoring"""
    try:
        # Import psutil for system metrics (add to requirements if needed)
        import psutil
        import os
        
        # System metrics
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "system": {
                "cpu_percent": psutil.cpu_percent(interval=1),
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "percent": memory.percent,
                    "used": memory.used
                },
                "disk": {
                    "total": disk.total,
                    "used": disk.used,
                    "free": disk.free,
                    "percent": (disk.used / disk.total) * 100
                }
            },
            "process": {
                "pid": os.getpid(),
                "memory_info": dict(psutil.Process().memory_info()._asdict()),
                "cpu_percent": psutil.Process().cpu_percent()
            },
            "application": {
                "version": settings.app_version,
                "environment": getattr(settings, 'environment', 'production'),
                "uptime": int((datetime.utcnow() - app.state.start_time).total_seconds()) if hasattr(app.state, 'start_time') else 0
            }
        }
    except ImportError:
        # Fallback if psutil is not available
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "message": "Detailed metrics not available (psutil not installed)",
            "basic_metrics": {
                "version": settings.app_version,
                "uptime": int((datetime.utcnow() - app.state.start_time).total_seconds()) if hasattr(app.state, 'start_time') else 0
            }
        }
    except Exception as e:
        logger.error(f"Metrics collection failed: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "error": "metrics_collection_failed",
                "timestamp": datetime.utcnow().isoformat()
            }
        )


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "name": settings.app_name,
        "version": settings.app_version,
        "timestamp": datetime.utcnow().isoformat(),
    }


# Include routers
app.include_router(auth.router, prefix="/api/auth", tags=["Authentication"])
app.include_router(patients.router, prefix="/api/patients", tags=["Patients"])
app.include_router(medical.router, prefix="/api/medical-records", tags=["Medical Records"])
app.include_router(pdf.router, prefix="/api/pdf", tags=["PDF Generation"])

# WebSocket endpoint
@app.websocket("/ws/{auth_code}")
async def websocket_endpoint(websocket: WebSocket, auth_code: str):
    await manager.websocket_endpoint(websocket, auth_code)


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
    )