"""
Patient related database models
"""
from sqlalchemy import Column, String, JSON, DateTime, Index
from sqlalchemy.sql import func

from app.database import Base


class PatientCache(Base):
    """
    Patient data cache model for storing patient information
    """
    __tablename__ = "patient_cache"
    
    user_id = Column(String(50), primary_key=True, comment="医生用户ID")
    patient_id = Column(String(50), primary_key=True, comment="患者ID")
    data = Column(JSON, comment="患者数据JSON")
    updated_at = Column(
        DateTime,
        default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )
    
    # Additional fields
    name = Column(String(100), comment="患者姓名")
    age = Column(String(10), comment="年龄")
    sex = Column(String(10), comment="性别")
    mobile = Column(String(20), comment="手机号")
    tags = Column(JSON, comment="标签列表")
    last_visit_date = Column(DateTime, comment="最后就诊时间")
    visit_count = Column(String(10), comment="就诊次数")
    
    # Indexes
    __table_args__ = (
        Index("idx_updated", "updated_at"),
        Index("idx_user_patient", "user_id", "patient_id"),
        Index("idx_name", "name"),
    )
    
    def __repr__(self):
        return f"<PatientCache(user_id={self.user_id}, patient_id={self.patient_id}, name={self.name})>"
    
    def to_dict(self) -> dict:
        """Convert model to dictionary"""
        return {
            "patient_id": self.patient_id,
            "name": self.name,
            "age": self.age,
            "sex": self.sex,
            "mobile": self.mobile,
            "tags": self.tags or [],
            "last_visit_date": self.last_visit_date.isoformat() if self.last_visit_date else None,
            "visit_count": self.visit_count,
            "data": self.data or {},
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }