"""
Medical record related database models
"""
from sqlalchemy import Column, String, BigInteger, JSON, DateTime, Enum, Index, Text
from sqlalchemy.sql import func
import enum

from app.database import Base


class RecordType(str, enum.Enum):
    """Medical record types"""
    WZD = "wzd"  # 问诊单
    FZD = "fzd"  # 复诊单
    PRESCRIPTION = "prescription"  # 处方
    MESSAGE = "message"  # 聊天消息
    REPORT = "report"  # 检查报告
    IMAGE = "image"  # 影像资料


class MedicalRecordCache(Base):
    """
    Medical records cache model for storing various medical records
    """
    __tablename__ = "medical_records_cache"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(String(50), nullable=False, comment="医生用户ID")
    patient_id = Column(String(50), nullable=False, comment="患者ID")
    record_type = Column(
        Enum(RecordType),
        nullable=False,
        comment="记录类型"
    )
    record_id = Column(String(100), nullable=False, comment="记录ID")
    data = Column(JSON, comment="记录数据JSON")
    created_at = Column(
        DateTime,
        default=func.now(),
        nullable=False,
        comment="创建时间"
    )
    updated_at = Column(
        DateTime,
        default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )
    
    # Additional fields for quick access
    title = Column(String(200), comment="标题/主诉")
    content_summary = Column(Text, comment="内容摘要")
    record_date = Column(DateTime, comment="记录日期")
    doctor_name = Column(String(50), comment="医生姓名")
    diagnosis = Column(Text, comment="诊断")
    prescription_name = Column(String(200), comment="处方名称")
    total_amount = Column(String(20), comment="费用总额")
    status = Column(String(20), comment="状态")
    
    # Indexes and constraints
    __table_args__ = (
        Index("uk_record", "user_id", "patient_id", "record_type", "record_id", unique=True),
        Index("idx_patient", "user_id", "patient_id"),
        Index("idx_type_updated", "record_type", "updated_at"),
        Index("idx_record_date", "record_date"),
    )
    
    def __repr__(self):
        return f"<MedicalRecordCache(id={self.id}, type={self.record_type}, patient_id={self.patient_id})>"
    
    def to_dict(self) -> dict:
        """Convert model to dictionary"""
        return {
            "id": self.id,
            "record_type": self.record_type.value if self.record_type else None,
            "record_id": self.record_id,
            "title": self.title,
            "content_summary": self.content_summary,
            "record_date": self.record_date.isoformat() if self.record_date else None,
            "doctor_name": self.doctor_name,
            "diagnosis": self.diagnosis,
            "prescription_name": self.prescription_name,
            "total_amount": self.total_amount,
            "status": self.status,
            "data": self.data or {},
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }