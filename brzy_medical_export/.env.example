# ===========================================
# 必然中医 - 医案导出系统 后端配置
# ===========================================

# 应用基础配置
APP_NAME=BRZY Medical Export
APP_VERSION=1.0.0
DEBUG=false
SECRET_KEY=your-secret-key-here-change-this-in-production
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# 数据库配置 
# 格式: mysql+pymysql://username:password@host:port/database
DATABASE_URL=mysql+pymysql://root:password@localhost:3306/brzy_medical_export
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=10

# Redis配置 (可选，用于缓存和会话存储)
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=

# 原始API配置 (连接到必然中医后端API)
# 这是iOS应用连接的原始后端地址
ORIGINAL_API_URL=https://api.haoniuzhongyi.top:9090
ORIGINAL_API_TIMEOUT=30

# JWT配置 (用于token加密)
JWT_SECRET_KEY=your-jwt-secret-key-change-this-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS配置 (允许跨域的前端地址)
CORS_ORIGINS=http://localhost:5173,https://your-frontend-domain.com

# 文件存储配置
UPLOAD_DIR=./uploads
MAX_UPLOAD_SIZE=10485760  # 10MB

# PDF生成配置
PDF_FONT_DIR=./fonts
PDF_TEMP_DIR=./temp

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log

# WebSocket配置
WS_HEARTBEAT_INTERVAL=30
WS_CONNECTION_TIMEOUT=60

# ===========================================
# 部署建议
# ===========================================
# 
# 1. 生产环境必须修改的配置：
#    - SECRET_KEY: 使用强随机密钥
#    - JWT_SECRET_KEY: 使用强随机密钥  
#    - DATABASE_URL: 配置实际数据库连接
#    - ORIGINAL_API_URL: 配置实际的必然中医API地址
#    - DEBUG: 设置为false
#    - ALLOWED_HOSTS: 配置实际的域名
#    - CORS_ORIGINS: 配置实际的前端域名
#
# 2. 可选配置：
#    - REDIS_URL: 配置Redis用于性能优化
#    - LOG_LEVEL: 生产环境建议使用WARNING或ERROR
#
# 3. 目录权限：
#    - 确保应用有权限创建和写入：
#      - ./uploads (文件上传)
#      - ./temp (PDF临时文件)  
#      - ./fonts (字体文件)
#      - ./logs (日志文件)
#      - ./generated_pdfs (PDF输出)
#
# 4. 安全建议：
#    - 使用HTTPS部署
#    - 配置防火墙限制数据库访问
#    - 定期备份数据库
#    - 监控日志文件