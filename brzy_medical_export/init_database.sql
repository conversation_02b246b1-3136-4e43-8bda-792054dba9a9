-- ===========================================
-- 必然中医 - 医案导出系统 数据库初始化脚本
-- ===========================================

-- 创建数据库 (如果不存在)
CREATE DATABASE IF NOT EXISTS brzy_medical_export 
    CHARACTER SET utf8mb4 
    COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE brzy_medical_export;

-- 创建认证会话表
CREATE TABLE IF NOT EXISTS auth_sessions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    session_id VARCHAR(100) NOT NULL UNIQUE COMMENT 'Web会话ID',
    session_token VARCHAR(500) NOT NULL COMMENT 'iOS sessionToken(加密存储)',
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
    device_info JSON NULL COMMENT '设备信息',
    current_patient_id VARCHAR(50) NULL COMMENT '当前患者ID',
    patient_name VARCHAR(100) NULL COMMENT '患者姓名', 
    ip_address VARCHAR(45) NULL COMMENT 'IP地址',
    status ENUM('pending', 'authorized', 'expired') NOT NULL DEFAULT 'pending' COMMENT '会话状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    expires_at TIMESTAMP NULL COMMENT '过期时间',
    
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='认证会话表';

-- 创建用户表 (可选，用于存储基本用户信息)
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id VARCHAR(50) NOT NULL UNIQUE COMMENT '用户ID(来自原系统)',
    username VARCHAR(100) NULL COMMENT '用户名',
    name VARCHAR(100) NULL COMMENT '真实姓名',
    status ENUM('active', 'inactive', 'banned') NOT NULL DEFAULT 'active' COMMENT '用户状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_username (username),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息表';

-- 创建PDF生成任务表 (可选，用于跟踪PDF生成状态)
CREATE TABLE IF NOT EXISTS pdf_tasks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    task_id VARCHAR(100) NOT NULL UNIQUE COMMENT '任务ID',
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
    patient_id VARCHAR(50) NOT NULL COMMENT '患者ID',
    patient_name VARCHAR(100) NULL COMMENT '患者姓名',
    status ENUM('processing', 'completed', 'failed') NOT NULL DEFAULT 'processing' COMMENT '任务状态',
    file_path VARCHAR(500) NULL COMMENT '生成文件路径',
    file_size BIGINT NULL COMMENT '文件大小(字节)',
    error_message TEXT NULL COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    
    INDEX idx_task_id (task_id),
    INDEX idx_user_id (user_id),
    INDEX idx_patient_id (patient_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='PDF生成任务表';

-- 创建系统日志表 (可选，用于审计和监控)
CREATE TABLE IF NOT EXISTS system_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id VARCHAR(50) NULL COMMENT '用户ID',
    action VARCHAR(100) NOT NULL COMMENT '操作类型',
    resource VARCHAR(200) NULL COMMENT '操作资源',
    details JSON NULL COMMENT '详细信息',
    ip_address VARCHAR(45) NULL COMMENT 'IP地址',
    user_agent TEXT NULL COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at),
    INDEX idx_ip_address (ip_address)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志表';

-- 插入初始数据 (可选)
-- INSERT INTO users (user_id, username, name) VALUES 
-- ('admin', 'admin', '系统管理员');

-- ===========================================
-- 数据库用户权限设置 (生产环境建议)
-- ===========================================
-- 
-- 1. 创建专用数据库用户:
-- CREATE USER 'brzy_medical'@'localhost' IDENTIFIED BY 'strong_password_here';
-- 
-- 2. 授予必要权限:
-- GRANT SELECT, INSERT, UPDATE, DELETE ON brzy_medical_export.* TO 'brzy_medical'@'localhost';
-- 
-- 3. 刷新权限:
-- FLUSH PRIVILEGES;
--
-- ===========================================
-- 维护建议
-- ===========================================
--
-- 1. 定期清理过期数据:
--    - 删除过期的auth_sessions记录
--    - 删除旧的pdf_tasks记录
--    - 归档旧的system_logs记录
--
-- 2. 性能优化:
--    - 监控慢查询日志
--    - 定期优化表结构
--    - 配置适当的索引
--
-- 3. 备份策略:
--    - 定期全量备份
--    - 配置增量备份
--    - 测试恢复流程