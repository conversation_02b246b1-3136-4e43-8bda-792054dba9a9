# 1Panel 前端环境配置指南

## Node.js 运行环境配置

### 基本信息
```
名称: brzy-frontend
应用: Node.js
版本: 22.2.0
运行目录: /opt/brzy_medical_system/brzy-medical-web
```

### 启动命令配置

**选择"自定义启动命令"**，输入：
```bash
# 生产环境启动命令
npm ci && npm run build && npm run preview -- --host 0.0.0.0 --port 3000

# 或者分步骤启动命令
npm ci
npm run build
npm run preview -- --host 0.0.0.0 --port 3000
```

### 端口配置
```
应用端口: 3000
外部映射端口: 3000
端口外部访问: 开启 ✅
```

### 包管理器配置
```
包管理器: npm
镜像源: https://registry.npmjs.org/
# 或使用国内镜像（可选）
# 镜像源: https://registry.npmmirror.com/
```

### 容器配置
```
容器名称: brzy-frontend-container
```

## 环境变量配置（可选）

如果需要在1Panel中直接配置环境变量，可以添加：

```env
NODE_ENV=production
VITE_API_URL=https://your-domain.com
VITE_WS_URL=wss://your-domain.com
VITE_APP_TITLE=必然中医 - 医案导出系统
```

## 前端构建优化

### package.json 调整

确保 `brzy-medical-web/package.json` 中包含 preview 命令：

```json
{
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview"
  }
}
```

### 构建配置优化

在 `vite.config.js` 中添加生产优化：

```javascript
export default defineConfig({
  // ... 其他配置
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    minify: 'terser',
    rollupOptions: {
      output: {
        manualChunks: {
          vue: ['vue'],
          'element-plus': ['element-plus']
        }
      }
    }
  },
  preview: {
    host: '0.0.0.0',
    port: 3000,
    cors: true
  }
})
```

## 启动流程说明

1. **npm ci**: 安装生产依赖（比 npm install 更快更可靠）
2. **npm run build**: 构建生产版本到 dist 目录
3. **npm run preview**: 启动静态文件服务器预览构建结果

## 常见问题解决

### 1. 构建失败
```bash
# 检查 Node.js 版本
node --version

# 清理缓存重新安装
rm -rf node_modules package-lock.json
npm ci
```

### 2. 端口占用
```bash
# 检查端口占用
lsof -i :3000

# 修改端口（如果需要）
npm run preview -- --port 3001
```

### 3. 内存不足
```bash
# 增加 Node.js 内存限制
NODE_OPTIONS="--max-old-space-size=4096" npm run build
```

## 监控和日志

### 查看应用日志
在1Panel运行环境管理界面可以直接查看：
- 容器日志
- 应用状态
- 资源使用情况

### 手动检查
```bash
# 检查容器状态
docker ps | grep brzy-frontend

# 查看容器日志
docker logs brzy-frontend-container -f

# 进入容器调试
docker exec -it brzy-frontend-container /bin/sh
```

## 性能优化建议

1. **启用 Gzip 压缩**：在 Nginx 反向代理中配置
2. **CDN 加速**：将静态资源上传到 CDN
3. **缓存策略**：配置合适的缓存头
4. **资源懒加载**：优化前端代码的加载策略

## 更新部署流程

当代码更新时，1Panel 会自动：
1. 拉取最新代码
2. 重新安装依赖
3. 重新构建应用
4. 重启容器服务

这种方式完全支持 GitHub Actions 自动化部署。