# 必然中医医案导出系统 - 配置清单

## 📋 快速配置清单

### 🚀 开发环境 (1分钟启动)

```bash
# 1. 克隆或下载项目到本地
cd /path/to/project

# 2. 运行一键启动脚本
./start_dev.sh

# 3. 打开浏览器访问 http://localhost:5173
```

**就这么简单！** 脚本会自动：
- ✅ 检查环境依赖 (Python 3.8+, Node.js 16+)
- ✅ 创建虚拟环境和安装依赖
- ✅ 生成配置文件和随机密钥
- ✅ 启动后端服务 (端口8000)
- ✅ 启动前端服务 (端口5173)

### 🛑 停止服务

```bash
./stop_dev.sh
```

---

## 🔧 手动配置 (高级用户)

### 1. 后端配置文件 (.env)

**必须配置的参数：**

```env
# 🔐 安全密钥 (必须修改)
SECRET_KEY=your-strong-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here

# 🗄️ 数据库连接 (必须配置)
DATABASE_URL=mysql+pymysql://username:password@host:port/database

# 🌐 原始API地址 (必须正确)
ORIGINAL_API_URL=https://api.haoniuzhongyi.top:9090

# 🔗 CORS设置 (必须匹配前端域名)
CORS_ORIGINS=http://localhost:5173,https://your-domain.com
```

**可选配置：**

```env
# 📊 调试模式
DEBUG=true  # 开发环境: true, 生产环境: false

# 📝 日志级别
LOG_LEVEL=INFO  # DEBUG/INFO/WARNING/ERROR

# 💾 Redis缓存 (性能优化)
REDIS_URL=redis://localhost:6379/0

# 📁 文件存储
UPLOAD_DIR=./uploads
PDF_TEMP_DIR=./temp
```

### 2. 前端配置文件 (.env)

```env
# 🔗 后端API地址
VITE_API_URL=http://localhost:8000

# 📡 WebSocket地址  
VITE_WS_URL=ws://localhost:8000

# 📱 应用信息
VITE_APP_TITLE=必然中医 - 医案导出系统
VITE_APP_VERSION=1.0.0
```

### 3. 数据库初始化

```bash
# 方式1: 使用SQL脚本 (推荐)
mysql -u root -p < init_database.sql

# 方式2: 手动创建数据库
mysql -u root -p
CREATE DATABASE brzy_medical_export CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

---

## 🌍 环境差异配置

### 开发环境 (Development)
```env
DEBUG=true
LOG_LEVEL=DEBUG
VITE_API_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000
CORS_ORIGINS=http://localhost:5173
```

### 测试环境 (Staging)
```env
DEBUG=false
LOG_LEVEL=INFO
VITE_API_URL=https://staging-api.yourdomain.com
VITE_WS_URL=wss://staging-api.yourdomain.com
CORS_ORIGINS=https://staging.yourdomain.com
```

### 生产环境 (Production)
```env
DEBUG=false
LOG_LEVEL=WARNING
VITE_API_URL=https://api.yourdomain.com
VITE_WS_URL=wss://api.yourdomain.com
CORS_ORIGINS=https://yourdomain.com
DATABASE_POOL_SIZE=50
```

---

## 🔒 安全配置清单

### ✅ 必须修改的配置

- [ ] `SECRET_KEY` - 生成强随机密钥
- [ ] `JWT_SECRET_KEY` - 生成强随机密钥  
- [ ] `DATABASE_URL` - 配置实际数据库连接
- [ ] `CORS_ORIGINS` - 限制允许的前端域名
- [ ] `DEBUG=false` - 生产环境关闭调试

### ✅ 推荐配置

- [ ] 使用HTTPS (SSL证书)
- [ ] 配置防火墙规则
- [ ] 启用数据库连接池
- [ ] 配置Redis缓存
- [ ] 设置日志轮转

### ✅ 安全检查

```bash
# 检查密钥强度
python3 -c "import secrets; print('✅ 强密钥:', secrets.token_urlsafe(32))"

# 检查端口开放
nmap -p 3306,6379,8000,5173 localhost

# 检查SSL证书
openssl s_client -connect yourdomain.com:443 -servername yourdomain.com
```

---

## 📊 监控配置

### 系统监控
```bash
# CPU和内存使用率
htop

# 磁盘空间
df -h

# 网络连接
netstat -tlnp | grep :8000
```

### 应用监控
```bash
# 后端健康检查
curl http://localhost:8000/health

# 前端访问测试
curl http://localhost:5173

# WebSocket连接测试
curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" http://localhost:8000/ws/test
```

### 日志监控
```bash
# 实时查看后端日志
tail -f backend.log

# 实时查看系统日志
sudo journalctl -f -u brzy-medical-backend

# 查看错误日志
grep "ERROR" logs/app.log
```

---

## 🔧 常见配置问题

### ❌ 数据库连接失败
```
解决方法:
1. 检查MySQL服务是否启动: sudo systemctl status mysql
2. 检查数据库用户权限: mysql -u username -p
3. 检查防火墙设置: sudo ufw status
4. 验证连接字符串格式: mysql+pymysql://user:pass@host:port/db
```

### ❌ CORS错误
```
解决方法:
1. 检查CORS_ORIGINS配置是否包含前端域名
2. 确保域名格式正确 (http://localhost:5173)
3. 重启后端服务使配置生效
```

### ❌ WebSocket连接失败
```
解决方法:
1. 检查WebSocket URL配置 (ws:// 或 wss://)
2. 确保防火墙允许WebSocket连接
3. 检查代理服务器WebSocket配置
```

### ❌ PDF生成失败
```
解决方法:
1. 检查字体文件是否存在
2. 确保temp目录有写权限
3. 检查reportlab依赖是否正确安装
```

---

## 📞 获取帮助

### 🆘 快速诊断
```bash
# 运行系统诊断脚本
./start_dev.sh  # 会自动检查环境和配置
```

### 📧 技术支持
遇到问题时，请提供：
1. 错误日志内容
2. 配置文件内容 (隐去敏感信息)
3. 操作系统和软件版本信息
4. 重现问题的步骤

### 📚 相关文档
- [完整部署指南](./DEPLOYMENT_GUIDE.md)
- [技术架构文档](./docs/医案导出系统技术方案.md)
- [API接口文档](http://localhost:8000/api/docs) (启动后端后访问)