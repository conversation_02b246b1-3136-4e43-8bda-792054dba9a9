# 1Panel 部署指南

## 概述

本指南详细说明如何使用 1Panel + GitHub Actions 实现必然中医医案导出系统的自动化部署。

## 部署架构

```
GitHub Repository
        ↓ (push to main)
GitHub Actions
        ↓ (SSH)
Linux Server
        ↓
1Panel Management
   ├── MySQL 8.0
   ├── Redis 7.x
   ├── Nginx Proxy
   └── Python App
```

## 前置条件

### 服务器要求
- Linux 服务器 (Ubuntu 20.04+ 推荐)
- 4GB+ 内存，2核+ CPU
- 20GB+ 可用磁盘空间
- 已安装 1Panel 面板

### 域名和 SSL
- 已备案的域名
- DNS 解析指向服务器 IP
- SSL 证书 (1Panel 可自动申请)

## 部署步骤

### 1. 准备 1Panel 基础服务

#### 1.1 安装 MySQL
```bash
# 在 1Panel 应用商店中安装 MySQL 8.0
1. 进入 1Panel 应用商店
2. 搜索并安装 MySQL 8.0
3. 配置 root 密码
4. 创建数据库和用户：
```

```sql
-- 登录 MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE brzy_medical_export CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'brzy_user'@'%' IDENTIFIED BY 'your_strong_password';

-- 授权
GRANT ALL PRIVILEGES ON brzy_medical_export.* TO 'brzy_user'@'%';
FLUSH PRIVILEGES;

-- 退出
EXIT;
```

#### 1.2 安装 Redis
```bash
# 在 1Panel 应用商店中安装 Redis 7.x
1. 进入 1Panel 应用商店
2. 搜索并安装 Redis 7.x
3. 配置访问密码
4. 确保服务正常运行
```

#### 1.3 配置 Nginx
在 1Panel 网站管理中创建网站，配置反向代理：

```nginx
# 网站配置
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL 配置由 1Panel 自动管理
    
    # 前端静态文件
    location / {
        root /opt/brzy_medical_system/brzy-medical-web/dist;
        try_files $uri $uri/ /index.html;
        
        # 缓存配置
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API 代理
    location /api/ {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # WebSocket 代理
    location /ws/ {
        proxy_pass http://127.0.0.1:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket 超时配置
        proxy_read_timeout 3600s;
        proxy_send_timeout 3600s;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://127.0.0.1:8080/health;
        access_log off;
    }
}
```

### 2. 配置 GitHub Actions

#### 2.1 设置 GitHub Secrets
在 GitHub 仓库的 Settings > Secrets and variables > Actions 中添加：

```
SERVER_HOST=your.server.ip
SERVER_USER=root
SERVER_SSH_KEY=-----BEGIN OPENSSH PRIVATE KEY-----
...your private key content...
-----END OPENSSH PRIVATE KEY-----
SERVER_PORT=22
```

#### 2.2 生成 SSH 密钥对
```bash
# 在服务器上生成密钥对
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# 添加公钥到 authorized_keys
cat ~/.ssh/id_rsa.pub >> ~/.ssh/authorized_keys

# 私钥内容添加到 GitHub Secrets
cat ~/.ssh/id_rsa
```

### 3. 服务器初始部署

#### 3.1 手动运行部署脚本
```bash
# 下载项目到服务器
git clone https://github.com/your-username/brzy_output_web.git /opt/brzy_medical_system
cd /opt/brzy_medical_system

# 运行部署脚本
./deploy.sh
```

#### 3.2 配置生产环境变量

编辑后端配置文件：
```bash
nano /opt/brzy_medical_system/brzy_medical_export/.env
```

**必须修改的配置**：
```env
# 生成强随机密钥
SECRET_KEY=生成的强随机密钥
JWT_SECRET_KEY=生成的强随机JWT密钥

# 数据库连接 (1Panel MySQL)
DATABASE_URL=mysql+pymysql://brzy_user:your_strong_password@127.0.0.1:3306/brzy_medical_export

# Redis连接 (1Panel Redis)
REDIS_URL=redis://127.0.0.1:6379/0
REDIS_PASSWORD=your_redis_password

# 域名配置
ALLOWED_HOSTS=["your-domain.com", "127.0.0.1"]
CORS_ORIGINS=["https://your-domain.com"]
```

编辑前端配置文件：
```bash
nano /opt/brzy_medical_system/brzy-medical-web/.env
```

```env
# 生产域名
VITE_API_URL=https://your-domain.com
VITE_WS_URL=wss://your-domain.com
```

#### 3.3 生成安全密钥
```bash
# 生成 SECRET_KEY
python3 -c "import secrets; print('SECRET_KEY=' + secrets.token_urlsafe(32))"

# 生成 JWT_SECRET_KEY  
python3 -c "import secrets; print('JWT_SECRET_KEY=' + secrets.token_urlsafe(32))"
```

### 4. 创建 1Panel 运行环境

#### 4.1 Python 应用配置
在 1Panel 运行环境中创建 Python 应用：

```
名称: brzy-backend
应用: Python 3.13.0
运行目录: /opt/brzy_medical_system/brzy_medical_export
启动命令: python -m uvicorn app.main:app --host 0.0.0.0 --port 8080 --workers 2
应用端口: 8080
外部映射端口: 8080 (内网访问)
容器名称: brzy-backend-container
```

#### 4.2 环境变量配置
在 1Panel 运行环境中添加环境变量（可选，也可以使用 .env 文件）：

```
DATABASE_URL=mysql+pymysql://brzy_user:password@127.0.0.1:3306/brzy_medical_export
REDIS_URL=redis://127.0.0.1:6379/0
SECRET_KEY=your_secret_key
JWT_SECRET_KEY=your_jwt_secret_key
```

### 5. 初始化数据库

```bash
# 运行数据库初始化脚本
cd /opt/brzy_medical_system
mysql -u brzy_user -p brzy_medical_export < init_database.sql

# 测试数据库连接
cd brzy_medical_export
source venv/bin/activate
python -c "from app.database import check_database_connection; print('连接状态:', check_database_connection())"
```

### 6. 启动和测试

#### 6.1 启动服务
```bash
# 使用 systemd 管理服务
sudo systemctl start brzy-medical-backend
sudo systemctl enable brzy-medical-backend

# 或在 1Panel 中启动运行环境
```

#### 6.2 健康检查
```bash
# 测试后端健康检查
curl http://localhost:8080/health

# 测试完整访问链路
curl https://your-domain.com/api/health

# 检查 WebSocket 连接
curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" https://your-domain.com/ws/test
```

### 7. 自动化部署流程

#### 7.1 开发流程
```bash
# 开发者推送代码
git add .
git commit -m "feat: 新功能"
git push origin main

# GitHub Actions 自动触发部署
# 1. 构建前端应用
# 2. SSH 到服务器同步代码  
# 3. 重启 1Panel 应用
# 4. 健康检查
```

#### 7.2 监控部署状态
```bash
# 查看 GitHub Actions 日志
# 访问: https://github.com/your-repo/actions

# 查看服务器日志
tail -f /opt/brzy_medical_system/logs/backend/app.log
sudo journalctl -u brzy-medical-backend -f

# 查看 1Panel 应用状态
# 在 1Panel 面板中查看运行环境状态
```

## 运维管理

### 日常维护

#### 查看系统状态
```bash
# 服务状态
sudo systemctl status brzy-medical-backend

# 资源使用
htop
df -h
free -h

# 日志查看
tail -f /opt/brzy_medical_system/logs/backend/app.log
```

#### 定期任务
```bash
# 创建定期清理脚本
sudo crontab -e

# 添加清理任务
# 每天凌晨2点清理过期PDF文件
0 2 * * * find /opt/brzy_medical_system/generated_pdfs -name "*.pdf" -mtime +7 -delete

# 每周备份数据库
0 3 * * 0 mysqldump -u brzy_user -p brzy_medical_export > /opt/backups/db_backup_$(date +\%Y\%m\%d).sql
```

### 故障排除

#### 常见问题

**1. 后端服务无法启动**
```bash
# 检查配置文件
cat /opt/brzy_medical_system/brzy_medical_export/.env

# 检查数据库连接
cd /opt/brzy_medical_system/brzy_medical_export
source venv/bin/activate
python -c "from app.database import check_database_connection; check_database_connection()"

# 查看详细错误
sudo journalctl -u brzy-medical-backend -n 50
```

**2. 前端无法访问**
```bash
# 检查 Nginx 配置
sudo nginx -t

# 重新加载 Nginx
sudo systemctl reload nginx

# 检查静态文件权限
ls -la /opt/brzy_medical_system/brzy-medical-web/dist/
```

**3. WebSocket 连接失败**
```bash
# 测试 WebSocket 代理
curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" http://localhost:8080/ws/test

# 检查 Nginx WebSocket 配置
grep -A 10 "location /ws/" /etc/nginx/sites-available/your-site
```

### 监控和告警

#### 设置监控
```bash
# 安装监控工具
sudo apt install netdata

# 配置告警 (可选)
# 使用 1Panel 内置监控功能
# 或集成第三方监控服务
```

#### 性能优化
```bash
# 数据库优化
mysql -u root -p -e "SHOW VARIABLES LIKE 'innodb_buffer_pool_size';"

# Redis 优化
redis-cli CONFIG GET maxmemory

# 应用优化
# 根据访问量调整 uvicorn workers 数量
# 配置 Nginx 缓存策略
```

## 安全配置

### 防火墙设置
```bash
# 配置 UFW 防火墙
sudo ufw allow 22/tcp      # SSH
sudo ufw allow 80/tcp      # HTTP
sudo ufw allow 443/tcp     # HTTPS
sudo ufw --force enable

# 限制数据库访问
sudo ufw deny 3306/tcp     # MySQL (仅内网访问)
sudo ufw deny 6379/tcp     # Redis (仅内网访问)
```

### SSL 证书
```bash
# 1Panel 自动申请 Let's Encrypt 证书
# 或手动配置：

# 申请证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
0 12 * * * /usr/bin/certbot renew --quiet
```

### 数据备份
```bash
# 创建备份脚本
cat > /opt/scripts/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/opt/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# 数据库备份
mysqldump -u brzy_user -p brzy_medical_export > $BACKUP_DIR/db_$DATE.sql

# 文件备份
tar -czf $BACKUP_DIR/files_$DATE.tar.gz /opt/brzy_medical_system/generated_pdfs

# 清理旧备份 (保留30天)
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
EOF

chmod +x /opt/scripts/backup.sh

# 添加到定时任务
echo "0 1 * * * /opt/scripts/backup.sh" | sudo crontab -
```

## 总结

通过本指南，您可以实现：

1. **完全自动化部署**：代码推送后自动部署到生产环境
2. **统一管理界面**：通过 1Panel 管理所有服务
3. **高可用性**：systemd 服务管理，自动重启
4. **安全性**：SSL 证书、防火墙、权限控制
5. **可监控性**：完整的日志和监控体系

这套方案特别适合中小型项目的生产环境部署，大大降低了运维复杂度。