# 1Panel 后端环境配置指南

## Python 运行环境配置

### 基本信息
```
名称: brzy-backend
应用: Python
版本: 3.13.0
运行目录: /opt/brzy_medical_system/brzy_medical_export
```

### 启动命令配置

**选择"自定义启动命令"**，输入：
```bash
# 生产环境启动命令
pip install -r requirements.txt && python -m uvicorn app.main:app --host 0.0.0.0 --port 8080 --workers 2

# 或者分步骤启动命令（推荐）
pip install --upgrade pip
pip install -r requirements.txt
python -m uvicorn app.main:app --host 0.0.0.0 --port 8080 --workers 2 --access-log
```

### 端口配置
```
应用端口: 8080
外部映射端口: 8080
端口外部访问: 开启 ✅
```

### 容器配置
```
容器名称: brzy-backend-container
```

## 环境变量配置（推荐）

在1Panel环境变量中配置关键参数：

```env
# 应用配置
SECRET_KEY=your-production-secret-key
JWT_SECRET_KEY=your-production-jwt-secret-key
DEBUG=false

# 数据库配置（1Panel MySQL）
DATABASE_URL=mysql+pymysql://brzy_user:password@mysql-container:3306/brzy_medical_export

# Redis配置（1Panel Redis）
REDIS_URL=redis://redis-container:6379/0
REDIS_PASSWORD=your-redis-password

# 外部API
ORIGINAL_API_URL=https://api.haoniuzhongyi.top:9090/easydoctorv2-ws/apiController

# CORS配置
CORS_ORIGINS=["https://your-domain.com"]

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/app/logs/app.log
```

## 依赖优化配置

### requirements.txt 生产优化

确保 `requirements.txt` 包含固定版本：

```txt
# 核心框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.10.5
pydantic-settings==2.7.0

# 数据库
sqlalchemy==2.0.41
pymysql==1.1.0
alembic==1.12.1

# 缓存
redis==5.0.1
aioredis==2.0.1

# 安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
cryptography==41.0.7

# HTTP 客户端
httpx==0.25.2
aiofiles==23.2.1

# PDF 生成
reportlab==4.0.7
pillow>=10.2.0

# WebSocket
websockets==12.0

# 工具库
python-dateutil==2.8.2
pytz==2023.3
```

### 启动脚本优化

创建 `start.sh` 脚本（可选）：

```bash
#!/bin/bash
set -e

echo "🚀 启动必然中医后端服务..."

# 创建必要目录
mkdir -p logs uploads temp generated_pdfs

# 安装依赖
echo "📦 安装Python依赖..."
pip install --upgrade pip --no-cache-dir
pip install -r requirements.txt --no-cache-dir

# 数据库检查
echo "🔍 检查数据库连接..."
python -c "from app.database import check_database_connection; check_database_connection()" || echo "⚠️ 数据库连接失败，请检查配置"

# 启动服务
echo "🌟 启动FastAPI服务..."
exec python -m uvicorn app.main:app \
    --host 0.0.0.0 \
    --port 8080 \
    --workers 2 \
    --access-log \
    --log-level info
```

然后在1Panel中使用：
```bash
chmod +x start.sh && ./start.sh
```

## 数据库连接配置

### 1Panel 内部服务连接

如果使用1Panel部署的MySQL和Redis，使用容器名连接：

```env
# MySQL连接（容器间通信）
DATABASE_URL=mysql+pymysql://brzy_user:password@mysql-container:3306/brzy_medical_export

# Redis连接（容器间通信）
REDIS_URL=redis://redis-container:6379/0
```

### 外部数据库连接

如果使用外部数据库：

```env
# MySQL连接（外部服务器）
DATABASE_URL=mysql+pymysql://brzy_user:password@*************:3306/brzy_medical_export

# Redis连接（外部服务器）
REDIS_URL=redis://*************:6379/0
```

## 性能优化配置

### uvicorn 工作进程配置

```bash
# 根据服务器CPU核心数调整workers
python -m uvicorn app.main:app \
    --host 0.0.0.0 \
    --port 8080 \
    --workers 4 \
    --worker-class uvicorn.workers.UvicornWorker \
    --access-log \
    --log-level info
```

### 内存和并发优化

在环境变量中添加：

```env
# 数据库连接池
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=10

# WebSocket配置
WS_HEARTBEAT_INTERVAL=30
WS_CONNECTION_TIMEOUT=60

# 文件上传限制
MAX_UPLOAD_SIZE=10485760
```

## 健康检查配置

在 `app/main.py` 中确保有健康检查端点：

```python
@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.now()}
```

### 1Panel 健康检查配置

在1Panel中可以配置健康检查：
```
健康检查路径: /health
检查间隔: 30秒
超时时间: 10秒
重试次数: 3次
```

## 日志管理

### 应用日志配置

确保日志目录权限正确：

```bash
# 在启动命令中添加
mkdir -p /app/logs
chmod 755 /app/logs
```

### 1Panel 日志查看

在1Panel管理界面可以：
1. 实时查看容器日志
2. 下载日志文件
3. 设置日志轮转

## 安全配置

### 环境变量安全

1. **敏感信息**：在1Panel环境变量中配置，不要写入代码
2. **密钥轮换**：定期更新SECRET_KEY和JWT_SECRET_KEY
3. **数据库权限**：使用专门的数据库用户，限制权限

### 网络安全

```env
# 限制CORS来源
CORS_ORIGINS=["https://your-domain.com"]

# 限制访问主机
ALLOWED_HOSTS=["your-domain.com", "127.0.0.1"]
```

## 监控和维护

### 应用监控

```python
# 在 main.py 中添加监控端点
@app.get("/metrics")
async def get_metrics():
    return {
        "uptime": uptime(),
        "memory_usage": get_memory_usage(),
        "active_connections": get_active_connections()
    }
```

### 定期维护任务

在1Panel中可以配置定时任务：

```bash
# 每天清理临时文件
0 2 * * * find /app/temp -type f -mtime +1 -delete

# 每周清理过期PDF
0 3 * * 0 find /app/generated_pdfs -name "*.pdf" -mtime +7 -delete
```

## 故障排除

### 常见问题

1. **依赖安装失败**
```bash
# 清理pip缓存
pip cache purge
pip install -r requirements.txt --no-cache-dir
```

2. **数据库连接失败**
```bash
# 检查数据库连接
python -c "from app.database import check_database_connection; print(check_database_connection())"
```

3. **端口冲突**
```bash
# 检查端口占用
netstat -tlnp | grep :8080
```

### 调试模式

临时启用调试模式：
```env
DEBUG=true
LOG_LEVEL=DEBUG
```

这种配置方式完全支持1Panel的容器化管理，同时保持了应用的性能和安全性。