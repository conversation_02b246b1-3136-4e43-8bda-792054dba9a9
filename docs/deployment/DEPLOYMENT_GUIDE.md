# 必然中医 - 医案导出系统部署指南

## 系统概述

本系统为必然中医iOS应用提供医案导出功能，包括：
- Web端扫码登录界面
- 医案数据展示和筛选
- PDF格式医案导出
- 安全的token加密存储

## 系统架构

```
iOS客户端 ──扫码──→ Web前端 ──WebSocket──→ 后端API ──调用──→ 原始API
                     ↓                    ↓
                 Vue.js应用           FastAPI服务
                     ↓                    ↓
                 静态文件服务          MySQL数据库
```

## 环境要求

### 后端环境
- Python 3.8+
- MySQL 5.7+
- Redis 6.0+ (可选，用于缓存)
- 操作系统：Linux/macOS/Windows

### 前端环境  
- Node.js 16+
- npm 7+

### 系统资源
- CPU: 2核心+
- 内存: 4GB+
- 存储: 20GB+
- 网络: 100Mbps+

## 快速部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd brzy_output_web
```

### 2. 后端部署

#### 2.1 安装依赖
```bash
cd brzy_medical_export
pip install -r requirements.txt
```

#### 2.2 配置环境变量
```bash
# 复制配置模板
cp .env.example .env

# 编辑配置文件
nano .env
```

**必须修改的配置：**
```env
# 生成强随机密钥
SECRET_KEY=your-strong-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here

# 配置数据库连接
DATABASE_URL=mysql+pymysql://username:password@host:port/database

# 配置原始API地址
ORIGINAL_API_URL=https://api.haoniuzhongyi.top:9090

# 配置CORS域名
CORS_ORIGINS=https://your-frontend-domain.com
```

#### 2.3 初始化数据库
```bash
# 登录MySQL并执行初始化脚本
mysql -u root -p < init_database.sql

# 或者手动创建数据库
mysql -u root -p
CREATE DATABASE brzy_medical_export CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### 2.4 启动服务
```bash
# 开发环境
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# 生产环境  
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
```

### 3. 前端部署

#### 3.1 安装依赖
```bash
cd brzy-medical-web
npm install
```

#### 3.2 配置环境变量
```bash
# 复制配置模板
cp .env.example .env

# 编辑配置文件
nano .env
```

**配置示例：**
```env
# 开发环境
VITE_API_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000

# 生产环境
VITE_API_URL=https://your-api-domain.com
VITE_WS_URL=wss://your-api-domain.com
```

#### 3.3 构建和启动
```bash
# 开发环境
npm run dev

# 生产环境构建
npm run build

# 生产环境预览
npm run preview
```

## 生产环境部署

### 1. 使用Docker部署 (推荐)

#### 1.1 创建Docker Compose文件
```yaml
# docker-compose.yml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: your_root_password
      MYSQL_DATABASE: brzy_medical_export
      MYSQL_USER: brzy_user
      MYSQL_PASSWORD: your_user_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init_database.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  backend:
    build: ./brzy_medical_export
    environment:
      - DATABASE_URL=mysql+pymysql://brzy_user:your_user_password@mysql:3306/brzy_medical_export
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key
      - JWT_SECRET_KEY=your-jwt-secret-key
      - ORIGINAL_API_URL=https://api.haoniuzhongyi.top:9090
      - CORS_ORIGINS=https://your-domain.com
    ports:
      - "8000:8000"
    depends_on:
      - mysql
      - redis

  frontend:
    build: ./brzy-medical-web
    environment:
      - VITE_API_URL=https://your-api-domain.com
      - VITE_WS_URL=wss://your-api-domain.com
    ports:
      - "80:80"
    depends_on:
      - backend

volumes:
  mysql_data:
```

#### 1.2 启动服务
```bash
docker-compose up -d
```

### 2. 使用Nginx反向代理

#### 2.1 Nginx配置
```nginx
# /etc/nginx/sites-available/brzy-medical
server {
    listen 80;
    server_name your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # 前端静态文件
    location / {
        root /var/www/brzy-medical-web/dist;
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # WebSocket代理
    location /ws/ {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

#### 2.2 启用配置
```bash
sudo ln -s /etc/nginx/sites-available/brzy-medical /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 3. 使用Systemd服务管理

#### 3.1 创建后端服务
```ini
# /etc/systemd/system/brzy-medical-backend.service
[Unit]
Description=BRZY Medical Export Backend
After=network.target mysql.service

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/var/www/brzy_medical_export
Environment=PATH=/var/www/brzy_medical_export/venv/bin
ExecStart=/var/www/brzy_medical_export/venv/bin/uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

#### 3.2 启动服务
```bash
sudo systemctl enable brzy-medical-backend
sudo systemctl start brzy-medical-backend
sudo systemctl status brzy-medical-backend
```

## 安全配置

### 1. 密钥生成
```bash
# 生成强随机密钥
python -c "import secrets; print(secrets.token_urlsafe(32))"
```

### 2. 防火墙配置
```bash
# Ubuntu/Debian
sudo ufw allow 22/tcp      # SSH
sudo ufw allow 80/tcp      # HTTP
sudo ufw allow 443/tcp     # HTTPS
sudo ufw --force enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 3. SSL证书配置
```bash
# 使用Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

## 监控和维护

### 1. 日志配置
```bash
# 查看后端日志
tail -f /var/www/brzy_medical_export/logs/app.log

# 查看系统服务日志
sudo journalctl -u brzy-medical-backend -f

# 查看Nginx日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### 2. 数据库维护
```sql
-- 清理过期会话
DELETE FROM auth_sessions WHERE expires_at < NOW();

-- 清理旧PDF任务
DELETE FROM pdf_tasks WHERE created_at < DATE_SUB(NOW(), INTERVAL 7 DAY);

-- 清理旧日志
DELETE FROM system_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

### 3. 文件清理
```bash
# 清理过期PDF文件
find /var/www/brzy_medical_export/generated_pdfs -name "*.pdf" -mtime +1 -delete

# 清理临时文件
find /var/www/brzy_medical_export/temp -type f -mtime +1 -delete
```

### 4. 性能监控
```bash
# 安装监控工具
sudo apt install htop iotop netstat

# 监控系统资源
htop

# 监控网络连接
netstat -tlnp | grep :8000

# 监控磁盘IO
iotop
```

## 故障排除

### 1. 常见问题

#### 后端无法启动
```bash
# 检查依赖
pip list

# 检查配置
python -c "from app.config import settings; print(settings.database_url)"

# 检查数据库连接
python -c "from app.database import check_database_connection; print(check_database_connection())"
```

#### 前端构建失败
```bash
# 清理缓存
npm cache clean --force
rm -rf node_modules package-lock.json
npm install

# 检查Node版本
node --version
npm --version
```

#### 扫码登录失败
```bash
# 检查WebSocket连接
curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" http://localhost:8000/ws/test

# 检查CORS配置
curl -H "Origin: http://localhost:5173" -H "Access-Control-Request-Method: POST" -H "Access-Control-Request-Headers: Content-Type" -X OPTIONS http://localhost:8000/api/auth/scan
```

### 2. 日志分析
```bash
# 查看详细错误日志
grep "ERROR" /var/www/brzy_medical_export/logs/app.log

# 查看数据库连接错误
grep "database" /var/www/brzy_medical_export/logs/app.log

# 查看WebSocket错误
grep "websocket" /var/www/brzy_medical_export/logs/app.log
```

## 备份和恢复

### 1. 数据库备份
```bash
# 全量备份
mysqldump -u root -p brzy_medical_export > backup_$(date +%Y%m%d_%H%M%S).sql

# 增量备份 (需要开启binlog)
mysqlbinlog --start-datetime="2024-01-01 00:00:00" /var/lib/mysql/mysql-bin.000001
```

### 2. 应用备份
```bash
# 备份应用文件
tar -czf brzy_medical_backup_$(date +%Y%m%d).tar.gz /var/www/brzy_medical_export

# 备份生成的PDF
tar -czf pdf_backup_$(date +%Y%m%d).tar.gz /var/www/brzy_medical_export/generated_pdfs
```

### 3. 恢复流程
```bash
# 恢复数据库
mysql -u root -p brzy_medical_export < backup_20240101_120000.sql

# 恢复应用文件
tar -xzf brzy_medical_backup_20240101.tar.gz -C /

# 重启服务
sudo systemctl restart brzy-medical-backend
```

## 版本更新

### 1. 更新后端
```bash
# 备份当前版本
cp -r /var/www/brzy_medical_export /var/www/brzy_medical_export.backup

# 拉取新代码
cd /var/www/brzy_medical_export
git pull origin main

# 更新依赖
pip install -r requirements.txt

# 重启服务
sudo systemctl restart brzy-medical-backend
```

### 2. 更新前端
```bash
# 备份当前版本
cp -r /var/www/brzy-medical-web /var/www/brzy-medical-web.backup

# 拉取新代码
cd /var/www/brzy-medical-web
git pull origin main

# 重新构建
npm install
npm run build

# 更新Nginx配置 (如有需要)
sudo systemctl reload nginx
```

## 联系支持

如遇到部署问题，请提供以下信息：
1. 操作系统版本
2. Python/Node.js版本
3. 错误日志内容
4. 配置文件内容 (隐去敏感信息)

技术支持邮箱: <EMAIL>