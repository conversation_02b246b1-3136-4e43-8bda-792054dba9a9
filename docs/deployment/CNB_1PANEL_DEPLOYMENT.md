# CNB + 1Panel 容器化部署指南

这份文档详细说明如何使用 Cloud Native Buildpacks (CNB) 和 1Panel 部署必然中医医案导出系统。

## 概述

CNB (Cloud Native Buildpacks) 是一种自动化构建容器镜像的技术，无需编写 Dockerfile，能够自动检测技术栈并生成优化的生产镜像。

### 技术优势

- ✅ **无 Dockerfile**: 自动检测技术栈
- ✅ **安全性强**: 自动安全补丁和漏洞扫描
- ✅ **镜像优化**: 自动层缓存和大小优化
- ✅ **标准化**: 遵循 OCI 容器标准
- ✅ **可重现**: 确保构建一致性

## 前置条件

### 服务器要求
- Ubuntu 20.04+ 或 CentOS 8+
- Docker Engine 20.10+
- 1Panel 管理平台
- 至少 4GB RAM, 20GB 磁盘空间

### 开发工具
- Git
- Pack CLI 0.32.1+
- Docker Hub 或私有镜像仓库账号

## 项目配置文件说明

### 1. 根目录配置 (`project.toml`)
```toml
[project]
id = "brzy-medical-system"
name = "必然中医医案导出系统"
version = "1.0.0"

[build]
include = ["brzy-medical-web/**/*", "brzy_medical_export/**/*"]
exclude = ["**/.git/**", "**/node_modules/**", "**/venv/**"]
```

### 2. 前端配置 (`brzy-medical-web/buildpack.yml`)
```yaml
nodejs:
  version: "18.*"
  npm:
    install-command: "ci"
    include-devdependencies: true
  build:
    command: "npm run build"
    output-dir: "dist"
```

### 3. 后端配置 (`brzy_medical_export/buildpack.yml`)
```yaml
python:
  version: "3.11.*"
  runtime:
    server: "uvicorn"
    app-module: "app.main:app"
    port: 8080
    workers: 4
```

## 部署流程

### 第一步: 准备 1Panel 环境

1. **安装 1Panel**
```bash
curl -sSL https://resource.fit2cloud.com/1panel/package/quick_start.sh -o quick_start.sh && bash quick_start.sh
```

2. **配置 Docker 环境**
```bash
# 确保 Docker 服务运行
sudo systemctl start docker
sudo systemctl enable docker

# 安装 Docker Compose
sudo pip3 install docker-compose
```

3. **创建项目目录**
```bash
sudo mkdir -p /opt/brzy_medical_system
sudo chown $USER:$USER /opt/brzy_medical_system
```

### 第二步: 配置环境变量

在 1Panel 中创建以下环境变量：

```bash
# 数据库配置
MYSQL_ROOT_PASSWORD=your_secure_root_password
MYSQL_PASSWORD=your_secure_user_password

# Redis 配置
REDIS_PASSWORD=your_secure_redis_password

# 应用配置
JWT_SECRET_KEY=your_super_secret_jwt_key_change_in_production
ORIGINAL_API_BASE_URL=https://your-original-api.com

# Docker Registry (如果使用私有仓库)
DOCKER_USERNAME=your_docker_username
DOCKER_PASSWORD=your_docker_password
```

### 第三步: GitHub Actions 自动部署

1. **配置 GitHub Secrets**

在 GitHub 仓库设置中添加以下 Secrets:

```
SERVER_HOST=your_server_ip
SERVER_USER=your_ssh_username
SERVER_PASSWORD=your_ssh_password
SERVER_PORT=22

DOCKER_USERNAME=your_docker_username
DOCKER_PASSWORD=your_docker_password

MYSQL_ROOT_PASSWORD=your_secure_root_password
MYSQL_PASSWORD=your_secure_user_password
REDIS_PASSWORD=your_secure_redis_password
JWT_SECRET_KEY=your_super_secret_jwt_key
ORIGINAL_API_BASE_URL=https://your-original-api.com
```

2. **触发部署**

推送代码到 `release` 或 `main` 分支会自动触发 CNB 构建和部署:

```bash
git push origin release
```

### 第四步: 1Panel 应用配置

1. **创建应用栈**

在 1Panel 中创建新的应用栈，使用以下配置:

```yaml
# 应用名称: brzy-medical-system
# 类型: Docker Compose
# 源码: 使用生成的 docker-compose.cnb.yml
```

2. **配置网络代理**

在 1Panel 网站管理中配置反向代理:

```nginx
# 前端代理
location / {
    proxy_pass http://localhost:5173;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}

# API 代理
location /api {
    proxy_pass http://localhost:8080;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}

# WebSocket 代理
location /ws {
    proxy_pass http://localhost:8080;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
}
```

## 本地开发和测试

### 使用 Pack CLI 本地构建

1. **安装 Pack CLI**
```bash
# macOS
brew install buildpacks/tap/pack

# Linux
curl -sSL "https://github.com/buildpacks/pack/releases/download/v0.32.1/pack-v0.32.1-linux.tgz" | sudo tar -C /usr/local/bin/ --no-same-owner -xzv pack
```

2. **构建前端镜像**
```bash
pack build brzy-medical-frontend:local \
  --builder paketobuildpacks/builder-jammy-base \
  --buildpack paketo-buildpacks/nodejs \
  --path brzy-medical-web \
  --env NODE_ENV=production
```

3. **构建后端镜像**
```bash
pack build brzy-medical-backend:local \
  --builder paketobuildpacks/builder-jammy-base \
  --buildpack paketo-buildpacks/python \
  --path brzy_medical_export \
  --env PYTHON_ENV=production
```

4. **本地测试运行**
```bash
# 设置环境变量
export FRONTEND_IMAGE=brzy-medical-frontend:local
export BACKEND_IMAGE=brzy-medical-backend:local

# 启动服务
docker-compose -f docker-compose.cnb.yml up -d
```

## 监控和维护

### 健康检查端点

系统提供以下健康检查端点:

- `GET /health` - 完整健康状态
- `GET /ready` - 就绪状态检查  
- `GET /live` - 存活状态检查
- `GET /metrics` - 系统指标

### 日志管理

日志位置:
```
/opt/brzy_medical_system/logs/
├── backend/
│   ├── app.log
│   └── uvicorn.log
├── frontend/
│   └── access.log
└── nginx/
    ├── access.log
    └── error.log
```

### 数据备份

使用 1Panel 的备份功能定期备份:
- MySQL 数据
- Redis 数据  
- 上传文件
- 生成的 PDF

## 故障排查

### 常见问题

1. **构建失败**
```bash
# 检查 buildpack 配置
pack inspect-image your-image-name

# 查看构建日志
pack build --verbose
```

2. **容器启动失败**
```bash
# 查看容器日志
docker logs brzy-backend
docker logs brzy-frontend

# 检查健康状态
curl http://localhost:8080/health
curl http://localhost:5173/
```

3. **数据库连接问题**
```bash
# 检查数据库容器
docker exec -it brzy-mysql mysql -u root -p

# 测试连接
docker exec brzy-backend curl -f http://localhost:8080/ready
```

### 性能优化

1. **镜像大小优化**
   - 使用 `.cnbignore` 排除不必要文件
   - 合理配置 buildpack 缓存
   - 定期清理旧镜像

2. **运行时优化**
   - 调整 uvicorn workers 数量
   - 配置 Redis 内存限制
   - 启用 gzip 压缩

## 升级和回滚

### 升级流程
1. 推送新代码触发 GitHub Actions
2. CNB 自动构建新镜像
3. 1Panel 自动拉取并部署
4. 健康检查确认服务正常

### 回滚操作
```bash
# 查看历史镜像
docker images brzy-medical-frontend
docker images brzy-medical-backend

# 回滚到指定版本
docker-compose -f docker-compose.cnb.yml down
export FRONTEND_IMAGE=brzy-medical-frontend:previous-tag
export BACKEND_IMAGE=brzy-medical-backend:previous-tag
docker-compose -f docker-compose.cnb.yml up -d
```

## 安全考虑

1. **镜像安全**
   - 自动漏洞扫描
   - 最小化基础镜像
   - 非 root 用户运行

2. **网络安全**
   - 容器间网络隔离
   - SSL/TLS 证书配置
   - 防火墙规则设置

3. **数据安全**
   - 环境变量加密存储
   - 数据库访问控制
   - 定期安全更新

## 联系和支持

如有问题请联系:
- 技术文档: `docs/` 目录
- Issue 报告: GitHub Issues
- 紧急支持: 项目维护团队