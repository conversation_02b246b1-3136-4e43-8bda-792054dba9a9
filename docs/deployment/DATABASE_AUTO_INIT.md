# 数据库自动初始化功能

## 概述

为了提高生产部署的可靠性，我们在启动脚本中添加了数据库自动初始化功能。这确保了在应用启动时，所有必要的数据库表都会被自动创建。

## 功能特性

### 1. 智能检测
- 自动检测数据库连接状态
- 检查必要表是否存在
- 只在需要时执行初始化

### 2. 多种初始化方式
- **SQLAlchemy 初始化**：使用 Python ORM 创建表（推荐）
- **SQL 脚本初始化**：执行 `init_database.sql` 脚本（备用方案）

### 3. 安全性保证
- 使用 `CREATE TABLE IF NOT EXISTS` 语法，避免覆盖现有数据
- 详细的日志记录和错误处理
- 初始化失败时应用拒绝启动

## 工作流程

```
应用启动
    ↓
检查数据库连接
    ↓
检查必要表是否存在
    ↓
[如果表不完整]
    ↓
尝试 SQLAlchemy 初始化
    ↓
[如果失败] 尝试 SQL 脚本初始化
    ↓
验证初始化结果
    ↓
启动应用服务
```

## 涉及的表

系统会自动创建以下表：

1. **auth_sessions** - 认证会话表
2. **users** - 用户信息表
3. **pdf_tasks** - PDF生成任务表
4. **system_logs** - 系统日志表

## 使用方法

### 自动初始化（推荐）

数据库初始化会在应用启动时自动执行，无需手动操作：

```bash
# 启动应用（会自动初始化数据库）
./scripts/start-backend.sh
```

### 手动初始化

如果需要单独运行数据库初始化：

```bash
# 单独执行数据库初始化
python3 /workspace/scripts/init-database.py
```

## 配置要求

### 环境变量
确保以下环境变量正确配置：

```env
# 数据库连接字符串
DATABASE_URL=mysql+pymysql://user:password@host:port/database

# 数据库连接池配置
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=10
```

### 文件权限
确保初始化脚本有执行权限：

```bash
chmod +x /workspace/scripts/init-database.py
```

## 日志输出

### 正常初始化
```
🔧 Starting database initialization...
✅ Database connection successful
✅ All required tables already exist (4/4)
Database initialization not needed
```

### 首次初始化
```
🔧 Starting database initialization...
✅ Database connection successful
📋 No tables found, performing full initialization
Attempting SQLAlchemy initialization...
✅ Database initialized successfully with SQLAlchemy
✅ All required tables exist: ['auth_sessions', 'users', 'pdf_tasks', 'system_logs']
🎉 Database initialization completed successfully
```

### 部分初始化
```
🔧 Starting database initialization...
✅ Database connection successful
⚠️  Found 2/4 tables
Missing tables will be created
Attempting SQLAlchemy initialization...
✅ Database initialized successfully with SQLAlchemy
✅ All required tables exist: ['auth_sessions', 'users', 'pdf_tasks', 'system_logs']
🎉 Database initialization completed successfully
```

## 故障排除

### 常见问题

#### 1. 数据库连接失败
```
❌ Database connection failed, cannot proceed
```

**解决方案**：
- 检查数据库服务是否启动
- 验证 `DATABASE_URL` 配置
- 检查网络连接和防火墙设置

#### 2. 权限不足
```
❌ SQL script initialization failed: Access denied
```

**解决方案**：
- 确保数据库用户有创建表的权限
- 检查数据库用户配置

#### 3. 表创建失败
```
❌ Both initialization methods failed
```

**解决方案**：
- 检查数据库版本兼容性
- 查看详细错误日志
- 尝试手动执行 SQL 脚本

### 调试模式

启用调试模式以获取更多信息：

```bash
# 设置调试模式
export DEBUG=true

# 运行初始化
python3 /workspace/scripts/init-database.py
```

## 最佳实践

### 1. 部署前验证
```bash
# 在部署前测试数据库初始化
python3 /workspace/scripts/init-database.py
```

### 2. 定期备份
```bash
# 在初始化前备份数据库
mysqldump -u user -p database > backup_$(date +%Y%m%d).sql
```

### 3. 监控日志
```bash
# 监控应用启动日志
tail -f /workspace/logs/app.log
```

## 技术实现

### 核心文件
- `scripts/init-database.py` - 主要初始化脚本
- `scripts/start-backend.sh` - 启动脚本（集成初始化）
- `app/database.py` - 数据库连接和初始化函数
- `init_database.sql` - SQL 初始化脚本

### 关键函数
- `check_database_connection()` - 检查数据库连接
- `init_db()` - SQLAlchemy 表初始化
- `check_tables_exist()` - 检查表是否存在
- `verify_initialization()` - 验证初始化结果

## 安全注意事项

### 1. 权限控制
- 数据库用户应该有最小必要权限
- 避免使用 root 用户连接应用

### 2. 密码安全
- 数据库密码通过环境变量传递
- 避免在日志中记录敏感信息

### 3. 初始化幂等性
- 使用 `CREATE TABLE IF NOT EXISTS` 确保安全
- 不会覆盖现有数据

## 总结

数据库自动初始化功能大大提高了部署的可靠性和自动化程度，特别适合以下场景：

1. **首次部署**：自动创建所有必要表
2. **版本升级**：自动创建新增表
3. **环境迁移**：简化数据库配置过程
4. **容器化部署**：确保容器启动时数据库就绪

这个功能使得部署过程更加自动化和可靠，减少了人为错误的可能性。