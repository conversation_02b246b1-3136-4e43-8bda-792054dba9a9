# API 修复指南

## 发现的问题

### 1. 医案列表接口 method_code 错误
- 当前使用：`000117`（获取处方列表）
- 应该使用：`000233`（获取医案列表）
- 错误表现：返回 "系统异常" (code: 0006)

### 2. 设备信息不完整
- device_id 被截断：`60BA14A4-58A0-4E`
- 完整值应该是：`60BA14A4-58A0-4E3F-831C-F4B0F62EB8D4`
- 影响：imei 和 mac 参数不正确，导致 API 验证失败

### 3. 医案详情接口未实现
- 应该使用 method_code：`000228`

## 修复方案

### 1. 修复前端设备信息传递

**文件：** iOS 客户端代码
```objective-c
// 确保传递完整的 device_id
NSMutableDictionary *deviceInfo = [NSMutableDictionary dictionary];
[deviceInfo setObject:@"60BA14A4-58A0-4E3F-831C-F4B0F62EB8D4" forKey:@"device_id"];
[deviceInfo setObject:@"60BA14A4-58A0-4E3F-831C-F4B0F62EB8D4" forKey:@"imei"];
[deviceInfo setObject:@"0F535656-8B87-4A08-825E-6654D2F32344" forKey:@"mac"];
```

### 2. 修复后端 API 代理服务

**文件：** `brzy_medical_export/app/services/api_proxy.py`

```python
# 添加新的医案相关方法
async def get_medical_record_list(
    self,
    session_token: str,
    user_id: str,
    patient_id: str,
    page: int = 1,
    size: int = 20,
    device_info: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """获取医案列表"""
    params = {
        "patientId": patient_id,
        "page": page,
        "pageSize": size
    }
    
    data = await self._make_request(
        method_code="000233",  # 正确的医案列表接口
        session_token=session_token,
        user_id=user_id,
        params=params,
        device_info=device_info
    )
    
    return data

async def get_medical_record_detail(
    self,
    session_token: str,
    user_id: str,
    record_id: str,
    device_info: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """获取医案详情"""
    params = {
        "recordId": record_id
    }
    
    data = await self._make_request(
        method_code="000228",  # 医案详情接口
        session_token=session_token,
        user_id=user_id,
        params=params,
        device_info=device_info
    )
    
    return data
```

### 3. 修复医案 API 端点

**文件：** `brzy_medical_export/app/api/medical.py`

```python
@router.get("/{patient_id}", response_model=MedicalRecordListResponse)
async def get_medical_records(
    patient_id: str,
    record_type: Optional[RecordType] = Query(None, description="Filter by record type"),
    start_date: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    session: AuthSession = Depends(get_current_session),
    db: Session = Depends(get_db)
):
    """获取医案列表"""
    try:
        api_proxy = APIProxyService()
        
        # 解析设备信息
        device_info = None
        if session.device_info:
            import json
            try:
                device_info = json.loads(session.device_info)
            except:
                device_info = None
        
        # 调用医案列表接口
        result = await api_proxy.get_medical_record_list(
            session_token=session.session_token,
            user_id=session.user_id,
            patient_id=patient_id,
            page=page,
            size=page_size,
            device_info=device_info
        )
        
        # 处理返回数据
        records = []
        for item in result.get("list", []):
            record = MedicalRecordSummary(
                record_id=item.get("id", ""),
                record_type=RecordType.MEDICAL_RECORD,
                title=item.get("title", "医案"),
                record_date=item.get("createTime"),
                doctor_name=item.get("doctorName", ""),
                summary=item.get("summary", ""),
                status=item.get("status", "")
            )
            records.append(record)
        
        return MedicalRecordListResponse(
            code=200,
            message="Success",
            data=records,
            total=result.get("total", len(records))
        )
        
    except Exception as e:
        logger.error(f"Error getting medical records: {e}")
        raise HTTPException(status_code=500, detail=str(e))
```

### 4. 修复设备信息处理

**文件：** `brzy_medical_export/app/services/api_proxy.py`

修改 `_make_request` 方法中的设备信息处理：

```python
# 使用设备信息
if device_info:
    app_version = device_info.get("app_version", "4.6.3")
    # 确保使用完整的设备标识符
    imei = device_info.get("imei")
    mac = device_info.get("mac")
    
    # 如果 imei 或 mac 不存在或太短，使用 device_id
    if not imei or len(imei) < 20:
        imei = device_info.get("device_id", "60BA14A4-58A0-4E3F-831C-F4B0F62EB8D4")
    if not mac or len(mac) < 20:
        mac = device_info.get("device_id", "0F535656-8B87-4A08-825E-6654D2F32344")
        
    # 确保 imei 和 mac 是完整的
    if len(imei) < 20:
        imei = "60BA14A4-58A0-4E3F-831C-F4B0F62EB8D4"
    if len(mac) < 20:
        mac = "0F535656-8B87-4A08-825E-6654D2F32344"
        
    logger.debug(f"Using device info - imei: {imei}, mac: {mac}")
else:
    app_version = "4.6.3"
    imei = "60BA14A4-58A0-4E3F-831C-F4B0F62EB8D4"
    mac = "0F535656-8B87-4A08-825E-6654D2F32344"
```

## 测试步骤

1. 重启后端服务
2. 清除浏览器缓存和登录状态
3. 重新扫码登录，确保传递完整的设备信息
4. 测试医案列表功能
5. 查看后端日志，确认：
   - method_code 是 `000233`
   - imei 和 mac 是完整的 UUID 格式
   - API 返回成功（code: 0000）

## 预期结果

修复后应该能够：
1. 成功获取医案列表
2. 成功获取医案详情
3. 设备验证通过
4. 不再出现 "系统异常" 错误