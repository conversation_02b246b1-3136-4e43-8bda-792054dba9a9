# 必然中医数据模型详解

## 概述

本文档详细说明了必然中医iOS应用中的核心数据模型，这些模型定义了医案导出系统需要处理的数据结构。

## 一、患者相关模型

### 1.1 BRPatientModel（处方患者模型）

用于处方模块中选择患者的数据模型。

```objective-c
@interface BRPatientModel : NSObject

@property (nonatomic, copy) NSString *patientId;    // 患者ID（映射自服务器"id"字段）
@property (nonatomic, copy) NSString *name;         // 患者姓名
@property (nonatomic, copy) NSString *age;          // 年龄
@property (nonatomic, copy) NSString *sex;          // 性别（1:男, 2:女）
@property (nonatomic, copy) NSString *isSelf;       // 是否本人（0:否, 1:是）
@property (nonatomic, copy) NSString *isPregnant;   // 是否怀孕（0:否, 1:是）
@property (nonatomic, copy) NSString *birthday;     // 生日（格式：yyyy-MM-dd）

@end
```

### 1.2 PatientInfoModel（患者详细信息）

包含患者的详细信息，用于患者信息展示页面。

```objective-c
@interface PatientInfoModel : NSObject

@property (nonatomic, copy) NSString *code;         // 返回码
@property (nonatomic, copy) NSString *errorMsg;     // 错误信息
@property (nonatomic, copy) NSString *age;          // 年龄
@property (nonatomic, assign) IMContactGender sex;  // 性别枚举
@property (nonatomic, copy) NSString *name;         // 姓名
@property (nonatomic, copy) NSString *handImgUrl;   // 头像URL
@property (nonatomic, copy) NSString *orderNum;     // 订单号
@property (nonatomic, copy) NSString *payOrderNum;  // 支付订单号
@property (nonatomic, copy) NSString *inPrice;      // 价格
@property (nonatomic, copy) NSString *tag;          // 标签（逗号分隔）
@property (nonatomic, copy) NSString *remark;       // 备注
@property (nonatomic, strong) NSArray *tags;        // 标签数组

@end
```

### 1.3 IMContact（IM联系人模型）

即时通讯模块中的联系人模型，包含患者的基础信息。

```objective-c
@interface IMContact : NSObject

@property (nonatomic, copy) NSString *rosterUserId;     // 联系人ID（患者ID）
@property (nonatomic, copy) NSString *nickName;         // 昵称
@property (nonatomic, copy) NSString *headImgUrl;       // 头像URL
@property (nonatomic, copy) NSString *remark;           // 备注名称
@property (nonatomic, copy) NSString *mobile;           // 手机号
@property (nonatomic, assign) IMContactGender gender;   // 性别
@property (nonatomic, copy) NSString *age;              // 年龄
@property (nonatomic, strong) NSArray *tags;            // 标签数组
@property (nonatomic, copy) NSString *birthday;         // 生日
@property (nonatomic, assign) BOOL isPregnant;          // 是否怀孕

@end
```

## 二、处方相关模型

### 2.1 BRPrescriptionModel（完整处方模型）

包含一个完整处方的所有信息。

```objective-c
@interface BRPrescriptionModel : NSObject

// ===== 基本信息 =====
@property (nonatomic, copy) NSString *prescriptionId;   // 处方ID
@property (nonatomic, copy) NSString *createTime;       // 创建时间
@property (nonatomic, copy) NSString *updateTime;       // 更新时间
@property (nonatomic, copy) NSString *status;           // 状态（1:已开方，2:已支付，3:已发药）

// ===== 患者信息 =====
@property (nonatomic, copy) NSString *patientId;        // 患者ID
@property (nonatomic, copy) NSString *takerName;        // 服药者姓名
@property (nonatomic, copy) NSString *takerId;          // 服药者ID
@property (nonatomic, copy) NSString *takerAge;         // 服药者年龄
@property (nonatomic, copy) NSString *takerSex;         // 服药者性别（1:男，2:女）
@property (nonatomic, copy) NSString *takerIsPregnant;  // 服药者是否怀孕

// ===== 诊断信息 =====
@property (nonatomic, copy) NSString *preDescription;   // 辨证，病情描述
@property (nonatomic, copy) NSString *preName;          // 药方名称
@property (nonatomic, copy) NSString *diagnoses;        // 诊断（多个诊断用逗号分隔）

// ===== 用药信息 =====
@property (nonatomic, strong) id drugDetailList;        // 药材列表（NSArray<BRMedicineModel>）
@property (nonatomic, copy) NSString *drugType;         // 药材类型（1:中药，2:西药）
@property (nonatomic, copy) NSString *useType;          // 用药方式（1:煎汤，2:外用）
@property (nonatomic, copy) NSString *dayPreNum;        // 每日几剂
@property (nonatomic, copy) NSString *preTimes;         // 每剂几次
@property (nonatomic, copy) NSString *totalPreNum;      // 共几剂
@property (nonatomic, copy) NSString *useTime;          // 用药时间（1:饭前，2:饭后）
@property (nonatomic, copy) NSString *boilyWay;         // 煎药方式

// ===== 费用信息 =====
@property (nonatomic, copy) NSString *drugPrice;        // 药费
@property (nonatomic, copy) NSString *consultationfee;  // 诊费
@property (nonatomic, copy) NSString *totalPrice;       // 总费用

// ===== 其他信息 =====
@property (nonatomic, copy) NSString *instructions;     // 补充说明
@property (nonatomic, copy) NSString *note;             // 按语（医生笔记）
@property (nonatomic, copy) NSString *contraindication; // 禁忌（如忌辛辣、忌生冷）
@property (nonatomic, copy) NSString *sendAfterDay;     // 几天后发送复诊提醒
@property (nonatomic, copy) NSString *isSecrecy;        // 是否向患者保密（0:否，1:是）
@property (nonatomic, copy) NSString *isSaveTemplate;   // 是否保存为模板
@property (nonatomic, copy) NSString *userId;           // 医生ID
@property (nonatomic, copy) NSString *userName;         // 医生姓名

// ===== 药店信息 =====
@property (nonatomic, copy) NSString *drugStoreId;      // 药店ID
@property (nonatomic, copy) NSString *drugStoreName;    // 药店名称

@end
```

### 2.2 BRMedicineModel（药材模型）

单个药材的详细信息。

```objective-c
@interface BRMedicineModel : NSObject

@property (nonatomic, copy) NSString *drugId;       // 药材ID
@property (nonatomic, copy) NSString *drugName;     // 药材名称
@property (nonatomic, copy) NSString *dose;         // 剂量（如：10）
@property (nonatomic, copy) NSString *unit;         // 单位（如：g）
@property (nonatomic, copy) NSString *isExist;      // 是否有货（0:无货，1:有货）
@property (nonatomic, copy) NSString *useMethod;    // 处理方式（先煎、后下、包煎等）
@property (nonatomic, copy) NSString *index;        // 排序索引
@property (nonatomic, copy) NSString *price;        // 单价
@property (nonatomic, copy) NSString *totalPrice;   // 小计
@property (nonatomic, strong) id replaceData;       // 替代药材数据

// 额外属性
@property (nonatomic, copy) NSString *specification; // 规格
@property (nonatomic, copy) NSString *origin;        // 产地
@property (nonatomic, copy) NSString *manufacturer;  // 生产厂家

@end
```

## 三、消息相关模型

### 3.1 IMMessage（即时消息模型）

聊天消息的完整模型，包含各种消息类型。

```objective-c
@interface IMMessage : NSObject

// ===== 基本属性 =====
@property (nonatomic, copy) NSString *messageId;        // 本地消息ID
@property (nonatomic, copy) NSString *serverMessageId;  // 服务器消息ID
@property (nonatomic, copy) NSString *from;             // 发送方ID
@property (nonatomic, copy) NSString *to;               // 接收方ID
@property (nonatomic, copy) NSString *sessionId;        // 会话ID（患者ID）
@property (nonatomic, strong) NSDate *createTime;       // 创建时间
@property (nonatomic, strong) NSDate *serverTime;       // 服务器时间
@property (nonatomic, assign) IMContentType contentType; // 消息类型
@property (nonatomic, assign) BOOL isRead;              // 是否已读

// ===== 消息内容 =====
@property (nonatomic, copy) NSString *text;             // 文本内容
@property (nonatomic, copy) NSString *imageUrl;         // 图片URL
@property (nonatomic, copy) NSString *audioUrl;         // 音频URL
@property (nonatomic, assign) NSInteger audioDuration;  // 音频时长（秒）
@property (nonatomic, copy) NSString *linkUrl;          // 链接地址

// ===== 问诊单相关 =====
@property (nonatomic, copy) NSString *wzdId;            // 问诊单ID
@property (nonatomic, copy) NSString *title;            // 标题
@property (nonatomic, copy) NSString *wzdType;          // 问诊单类型
@property (nonatomic, copy) NSString *wzdVersion;       // 版本号
@property (nonatomic, strong) NSArray *questions;       // 问题列表
@property (nonatomic, strong) NSArray *answers;         // 答案列表

// ===== 处方相关 =====
@property (nonatomic, copy) NSString *prescriptionId;   // 处方ID
@property (nonatomic, copy) NSString *prescriptionName; // 处方名称

// ===== 扩展字段 =====
@property (nonatomic, copy) NSString *extra1;           // 扩展字段1
@property (nonatomic, copy) NSString *extra2;           // 扩展字段2
@property (nonatomic, copy) NSString *extra3;           // 扩展字段3

@end
```

### 3.2 消息类型枚举

```objective-c
typedef NS_ENUM(NSUInteger, IMContentType) {
    IMContentTypeText           = 1,    // 文本消息
    IMContentTypeImage          = 2,    // 图片消息
    IMContentTypeAudio          = 3,    // 语音消息
    IMContentTypeSystemTips     = 4,    // 系统提示
    IMContentTypeCustomSystem   = 5,    // 自定义系统消息
    IMContentTypeWZDQuestion    = 6,    // 问诊单问题
    IMContentTypeWZDAnswer      = 7,    // 问诊单回答
    IMContentTypeFZDQuestion    = 8,    // 复诊单问题
    IMContentTypeFZDAnswer      = 9,    // 复诊单回答
    IMContentTypeDate           = 10,   // 日期分隔
    IMContentTypeSessionStart   = 11,   // 会话开始
    IMContentTypeSessionEnd     = 12,   // 会话结束
    IMContentTypePrescription   = 13,   // 处方消息
    IMContentTypeRevoke         = 14,   // 撤回消息
    IMContentTypePatientInfo    = 15,   // 患者信息卡片
    IMContentTypeSupplementQ    = 16,   // 补充问题
    IMContentTypeSupplementA    = 17,   // 补充回答
};
```

## 四、医案文档模型

### 4.1 PatientsDocumentModel（医案文档模型）

用于展示患者的医案文档列表。

```objective-c
@interface PatientsDocumentModel : NSObject

@property (nonatomic, copy) NSString *documentId;       // 文档ID
@property (nonatomic, copy) NSString *patientId;        // 患者ID
@property (nonatomic, copy) NSString *name;             // 患者姓名
@property (nonatomic, copy) NSString *age;              // 年龄
@property (nonatomic, assign) IMContactGender sex;      // 性别
@property (nonatomic, assign) PatientConceiveType conceive; // 怀孕状态
@property (nonatomic, copy) NSString *symptom;          // 症状描述
@property (nonatomic, assign) PatientDocumentType type; // 文档类型
@property (nonatomic, copy) NSString *url;              // 文档链接
@property (nonatomic, copy) NSString *createTime;       // 创建时间
@property (nonatomic, copy) NSString *updateTime;       // 更新时间
@property (nonatomic, copy) NSString *doctorName;       // 医生姓名
@property (nonatomic, copy) NSString *department;       // 科室

// 问诊单特有
@property (nonatomic, copy) NSString *chiefComplaint;   // 主诉
@property (nonatomic, copy) NSString *presentIllness;   // 现病史
@property (nonatomic, copy) NSString *pastHistory;      // 既往史

// 处方特有
@property (nonatomic, copy) NSString *diagnosis;        // 诊断
@property (nonatomic, copy) NSString *prescription;     // 处方内容
@property (nonatomic, copy) NSString *totalAmount;      // 总金额

@end
```

### 4.2 文档类型枚举

```objective-c
typedef NS_ENUM(NSUInteger, PatientDocumentType) {
    PatientDocumentTypeWzd  = 1,    // 问诊单
    PatientDocumentTypeFzd  = 2,    // 复诊单
    PatientDocumentTypeYY   = 3,    // 用药记录
    PatientDocumentTypeBG   = 4,    // 报告单
    PatientDocumentTypeJZ   = 5,    // 就诊记录
};
```

## 五、会话相关模型

### 5.1 IMSession（会话模型）

```objective-c
@interface IMSession : NSObject

@property (nonatomic, copy) NSString *sessionId;            // 会话ID（等同于患者ID）
@property (nonatomic, copy) NSString *name;                 // 会话名称（患者姓名）
@property (nonatomic, copy) NSString *avatarUrl;            // 头像URL
@property (nonatomic, strong) NSDate *lastMessageTime;      // 最后消息时间
@property (nonatomic, copy) NSString *lastMessageContent;   // 最后消息内容
@property (nonatomic, assign) NSInteger unreadCount;        // 未读消息数
@property (nonatomic, assign) ChatStatusType chatStatusType; // 会话状态
@property (nonatomic, assign) BOOL isTop;                   // 是否置顶
@property (nonatomic, copy) NSString *remark;               // 备注

@end
```

### 5.2 会话状态枚举

```objective-c
typedef NS_ENUM(NSUInteger, ChatStatusType) {
    ChatStatusTypeNormal        = 0,    // 正常状态
    ChatStatusTypeWzdSended     = 1,    // 问诊单已发送
    ChatStatusTypeWzdFilled     = 2,    // 问诊单已填写
    ChatStatusTypeFzdSended     = 3,    // 复诊单已发送
    ChatStatusTypeFzdFilled     = 4,    // 复诊单已填写
    ChatStatusTypeOrderNoPay    = 5,    // 订单未支付
    ChatStatusTypeOrderPayed    = 6,    // 订单已支付
    ChatStatusTypeDeliverGoods  = 7,    // 已发药
    ChatStatusTypeFinished      = 8,    // 已完成
};
```

## 六、数据库表结构

### 6.1 消息表（IMTableMessage）

```sql
CREATE TABLE IF NOT EXISTS IMTableMessage (
    messageId TEXT PRIMARY KEY,
    serverMessageId TEXT,
    sessionId TEXT NOT NULL,
    fromUserId TEXT NOT NULL,
    toUserId TEXT NOT NULL,
    contentType INTEGER NOT NULL,
    content TEXT,
    createTime INTEGER NOT NULL,
    serverTime INTEGER,
    isRead INTEGER DEFAULT 0,
    extra TEXT
);
```

### 6.2 会话表（IMTableSession）

```sql
CREATE TABLE IF NOT EXISTS IMTableSession (
    sessionId TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    avatarUrl TEXT,
    lastMessageTime INTEGER,
    lastMessageContent TEXT,
    unreadCount INTEGER DEFAULT 0,
    chatStatusType INTEGER DEFAULT 0,
    isTop INTEGER DEFAULT 0,
    remark TEXT
);
```

### 6.3 联系人表（IMTableContact）

```sql
CREATE TABLE IF NOT EXISTS IMTableContact (
    rosterUserId TEXT PRIMARY KEY,
    nickName TEXT,
    headImgUrl TEXT,
    remark TEXT,
    mobile TEXT,
    gender INTEGER,
    age TEXT,
    tags TEXT,
    birthday TEXT,
    isPregnant INTEGER DEFAULT 0,
    updateTime INTEGER
);
```

### 6.4 临时处方表（BRTemporaryPrescription）

```sql
CREATE TABLE IF NOT EXISTS BRTemporaryPrescription (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    patientId TEXT NOT NULL,
    prescriptionData TEXT NOT NULL,
    createTime INTEGER NOT NULL,
    updateTime INTEGER NOT NULL,
    status INTEGER DEFAULT 0
);
```

## 七、数据导出需求分析

基于以上数据模型，医案导出系统需要包含以下内容：

### 7.1 患者基本信息
- 姓名、性别、年龄、生日
- 联系电话
- 标签、备注
- 头像

### 7.2 诊疗记录
- 问诊单（主诉、现病史、既往史）
- 复诊单（复诊情况、调整方案）
- 诊断信息

### 7.3 处方信息
- 处方名称、创建时间
- 药材清单（名称、剂量、用法）
- 用药说明、禁忌
- 费用信息

### 7.4 沟通记录
- 文字消息
- 图片消息
- 语音消息（转文字）
- 系统提示

### 7.5 时间线
- 按时间顺序展示所有医疗活动
- 标注重要节点（首诊、复诊、开方等）

## 八、数据处理注意事项

### 8.1 数据完整性
- 处理可能的空值情况
- 验证数据关联关系
- 保证时间顺序正确

### 8.2 隐私保护
- 脱敏处理敏感信息
- 根据设置隐藏保密内容
- 记录数据访问日志

### 8.3 格式转换
- 日期时间格式统一
- 枚举值转换为可读文本
- 图片URL处理

### 8.4 性能优化
- 分批加载大量数据
- 使用缓存减少重复查询
- 异步处理耗时操作