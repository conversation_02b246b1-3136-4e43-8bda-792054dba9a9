# iOS端扫码登录功能实施指南

## 项目信息

- **项目名称**：必然中医 (BRZY)
- **当前版本**：4.6.3
- **最低iOS版本**：iOS 13.0
- **开发语言**：Objective-C
- **Bundle ID**：com.yykkj.biranzhongyi

## 功能概述

在聊天界面右上角添加扫码按钮，允许医生扫描Web端二维码进行授权登录，以便在Web端查看和下载患者医案。

## 实施步骤

### Step 1: 修改聊天界面UI

#### 1.1 修改 ChatViewController.m

在 `configUI` 方法中添加扫码按钮：

```objective-c
// ChatViewController.m
// 找到 configUI 方法（约第194行）
- (void)configUI {
    // ... 现有代码 ...
    
    // 修改原有的单个按钮为多个按钮
    // 创建电话按钮
    UIImage *mobileImage = [[UIImage imageNamed:@"navi_mobile_btn"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
    UIBarButtonItem *phoneBarButtonItem = [[UIBarButtonItem alloc] initWithImage:mobileImage style:UIBarButtonItemStylePlain target:self action:@selector(clickMobileButton:)];
    
    // 创建扫码按钮（使用已有的二维码图标）
    UIImage *scanImage = [[UIImage imageNamed:@"navi_barcode_btn"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
    UIBarButtonItem *scanBarButtonItem = [[UIBarButtonItem alloc] initWithImage:scanImage style:UIBarButtonItemStylePlain target:self action:@selector(clickScanButton:)];
    
    // 设置多个右侧按钮（注意顺序，数组中的第一个元素显示在最右边）
    self.navigationItem.rightBarButtonItems = @[phoneBarButtonItem, scanBarButtonItem];
}
```

### Step 2: 实现扫码授权逻辑

#### 2.1 添加扫码按钮响应方法

```objective-c
// ChatViewController.m
// 在 clickMobileButton: 方法后添加新方法

#pragma mark - 扫码登录Web
- (void)clickScanButton:(UIButton *)sender {
    // 防止重复点击
    if (self.isMobileButtonClicked) {
        return;
    }
    self.isMobileButtonClicked = YES;
    
    // 延迟恢复点击状态
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        self.isMobileButtonClicked = NO;
    });
    
    // 生成授权码
    NSString *authCode = [self generateAuthCode];
    
    // 创建扫码控制器
    ScanQrcodeViewController *scanVC = [[ScanQrcodeViewController alloc] init];
    
    // 设置扫码完成回调
    __weak typeof(self) weakSelf = self;
    scanVC.scanCompleteBlock = ^(NSString *qrCode) {
        [weakSelf handleWebLoginQRCode:qrCode authCode:authCode];
    };
    
    // 设置扫码提示文字
    scanVC.tipText = @"扫描Web端二维码进行登录";
    
    [self.navigationController pushViewController:scanVC animated:YES];
}

// 生成授权码
- (NSString *)generateAuthCode {
    // 时间戳（精确到毫秒）
    NSTimeInterval timestamp = [[NSDate date] timeIntervalSince1970] * 1000;
    
    // 4位随机数
    NSInteger random = arc4random_uniform(9999);
    
    // 用户ID
    NSString *userId = [UserManager shareInstance].getUserId;
    
    // 设备标识
    NSString *deviceId = [UIDevice currentDevice].identifierForVendor.UUIDString;
    NSString *shortDeviceId = [deviceId substringToIndex:8];
    
    // 组合授权码：时间戳_随机数_用户ID_设备标识
    return [NSString stringWithFormat:@"%.0f_%04ld_%@_%@", timestamp, (long)random, userId, shortDeviceId];
}

// 处理扫码结果
- (void)handleWebLoginQRCode:(NSString *)qrCode authCode:(NSString *)authCode {
    // 解析二维码内容
    // 预期格式：brzy-web://login?session=xxxxx&server=https://export.haoniuzhongyi.top
    NSURL *url = [NSURL URLWithString:qrCode];
    
    if (![url.scheme isEqualToString:@"brzy-web"] || ![url.host isEqualToString:@"login"]) {
        [self.view makeToast:@"无效的登录二维码" duration:2 position:CSToastPositionCenter];
        return;
    }
    
    // 解析参数
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    NSURLComponents *components = [NSURLComponents componentsWithURL:url resolvingAgainstBaseURL:NO];
    for (NSURLQueryItem *item in components.queryItems) {
        params[item.name] = item.value;
    }
    
    NSString *sessionId = params[@"session"];
    NSString *serverUrl = params[@"server"];
    
    if (!sessionId || !serverUrl) {
        [self.view makeToast:@"二维码信息不完整" duration:2 position:CSToastPositionCenter];
        return;
    }
    
    // 显示确认对话框
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"Web端登录确认" 
                                                                  message:@"是否允许Web端查看和下载医案？" 
                                                           preferredStyle:UIAlertControllerStyleAlert];
    
    UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"取消" 
                                                           style:UIAlertActionStyleCancel 
                                                         handler:nil];
    
    UIAlertAction *confirmAction = [UIAlertAction actionWithTitle:@"确认登录" 
                                                            style:UIAlertActionStyleDefault 
                                                          handler:^(UIAlertAction * _Nonnull action) {
        [self confirmWebLogin:sessionId serverUrl:serverUrl authCode:authCode];
    }];
    
    [alert addAction:cancelAction];
    [alert addAction:confirmAction];
    
    [self presentViewController:alert animated:YES completion:nil];
}

// 确认Web登录
- (void)confirmWebLogin:(NSString *)sessionId serverUrl:(NSString *)serverUrl authCode:(NSString *)authCode {
    // 显示加载提示
    [MBProgressHUD showHUDAddedTo:self.view animated:YES];
    
    // 构建请求参数
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:authCode forKey:@"auth_code"];
    [params setObject:[Config shareInstance].sessionToken forKey:@"session_token"];
    [params setObject:sessionId forKey:@"web_session_id"];
    
    // 设备信息
    NSMutableDictionary *deviceInfo = [NSMutableDictionary dictionary];
    [deviceInfo setObject:[UIApplication sharedApplication].appVersion forKey:@"app_version"];
    [deviceInfo setObject:[[UIDevice currentDevice] systemVersion] forKey:@"os_version"];
    [deviceInfo setObject:[[UIDevice currentDevice] model] forKey:@"device_model"];
    [deviceInfo setObject:@"iOS" forKey:@"platform"];
    [params setObject:deviceInfo forKey:@"device_info"];
    
    // 患者信息（当前聊天的患者）
    if (self.session && self.session.sessionId) {
        [params setObject:self.session.sessionId forKey:@"current_patient_id"];
    }
    
    // 发送授权请求
    NSString *authUrl = [NSString stringWithFormat:@"%@/api/auth/scan", serverUrl];
    
    __weak typeof(self) weakSelf = self;
    [HTTPRequest POST:authUrl parameters:params progress:nil success:^(NSURLSessionDataTask *task, id responseObject) {
        [MBProgressHUD hideHUDForView:weakSelf.view animated:YES];
        
        if ([responseObject[@"code"] integerValue] == 200) {
            [weakSelf.view makeToast:@"授权成功，请在Web端操作" duration:2 position:CSToastPositionCenter];
            
            // 可选：记录授权历史
            [weakSelf saveAuthHistory:serverUrl];
        } else {
            NSString *errorMsg = responseObject[@"message"] ?: @"授权失败";
            [weakSelf.view makeToast:errorMsg duration:2 position:CSToastPositionCenter];
        }
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        [MBProgressHUD hideHUDForView:weakSelf.view animated:YES];
        [weakSelf.view makeToast:@"网络请求失败，请重试" duration:2 position:CSToastPositionCenter];
        NSLog(@"Web登录授权失败: %@", error.localizedDescription);
    }];
}

// 保存授权历史（可选）
- (void)saveAuthHistory:(NSString *)serverUrl {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    NSMutableArray *history = [[defaults objectForKey:@"WebAuthHistory"] mutableCopy];
    if (!history) {
        history = [NSMutableArray array];
    }
    
    NSDictionary *record = @{
        @"server": serverUrl,
        @"time": [NSDate date],
        @"patient_id": self.session.sessionId ?: @"",
        @"patient_name": self.session.name ?: @""
    };
    
    [history insertObject:record atIndex:0];
    
    // 只保留最近10条记录
    if (history.count > 10) {
        [history removeObjectsInRange:NSMakeRange(10, history.count - 10)];
    }
    
    [defaults setObject:history forKey:@"WebAuthHistory"];
    [defaults synchronize];
}
```

#### 2.2 修改 ScanQrcodeViewController

添加扫码完成回调和提示文字支持：

```objective-c
// ScanQrcodeViewController.h
// 添加属性
@property (nonatomic, copy) void(^scanCompleteBlock)(NSString *qrCode);
@property (nonatomic, copy) NSString *tipText;

// ScanQrcodeViewController.m
// 在 viewDidLoad 中设置提示文字
- (void)viewDidLoad {
    [super viewDidLoad];
    
    // ... 现有代码 ...
    
    // 设置自定义提示文字
    if (self.tipText && self.tipText.length > 0) {
        self.tipLabel.text = self.tipText;
    }
}

// 修改扫码成功处理方法
- (void)scanSuccessWithString:(NSString *)metadataString {
    // 停止扫描
    [self stopQRCodeReading];
    
    // 如果设置了回调，优先使用回调
    if (self.scanCompleteBlock) {
        [self.navigationController popViewControllerAnimated:YES];
        self.scanCompleteBlock(metadataString);
        return;
    }
    
    // 否则使用原有的处理逻辑
    [self scanOrDontScanRequestQrCode:metadataString];
}
```

### Step 3: 添加网络请求错误处理

#### 3.1 创建专门的错误处理类

```objective-c
// BRWebAuthError.h
#import <Foundation/Foundation.h>

typedef NS_ENUM(NSInteger, BRWebAuthErrorCode) {
    BRWebAuthErrorCodeInvalidQRCode = 1001,      // 无效的二维码
    BRWebAuthErrorCodeExpiredQRCode = 1002,      // 二维码已过期
    BRWebAuthErrorCodeNetworkError = 1003,       // 网络错误
    BRWebAuthErrorCodeServerError = 1004,        // 服务器错误
    BRWebAuthErrorCodeAuthFailed = 1005,         // 授权失败
};

@interface BRWebAuthError : NSObject

+ (NSString *)errorMessageForCode:(BRWebAuthErrorCode)code;
+ (void)handleError:(NSError *)error inView:(UIView *)view;

@end

// BRWebAuthError.m
#import "BRWebAuthError.h"
#import <Toast/Toast.h>

@implementation BRWebAuthError

+ (NSString *)errorMessageForCode:(BRWebAuthErrorCode)code {
    switch (code) {
        case BRWebAuthErrorCodeInvalidQRCode:
            return @"无效的登录二维码";
        case BRWebAuthErrorCodeExpiredQRCode:
            return @"二维码已过期，请刷新后重试";
        case BRWebAuthErrorCodeNetworkError:
            return @"网络连接失败，请检查网络";
        case BRWebAuthErrorCodeServerError:
            return @"服务器错误，请稍后重试";
        case BRWebAuthErrorCodeAuthFailed:
            return @"授权失败，请重新扫码";
        default:
            return @"未知错误";
    }
}

+ (void)handleError:(NSError *)error inView:(UIView *)view {
    NSString *message = @"操作失败";
    
    if (error.code >= 1001 && error.code <= 1005) {
        message = [self errorMessageForCode:error.code];
    } else if (error.userInfo[NSLocalizedDescriptionKey]) {
        message = error.userInfo[NSLocalizedDescriptionKey];
    }
    
    [view makeToast:message duration:2 position:CSToastPositionCenter];
}

@end
```

### Step 4: 添加配置和常量定义

#### 4.1 在 BRDefines.h 添加相关常量

```objective-c
// BRDefines.h
// 添加Web授权相关常量

// Web授权相关
#define kWebAuthScheme              @"brzy-web"           // Web端二维码协议
#define kWebAuthHost                @"login"              // 登录主机名
#define kWebAuthTimeout             30 * 60               // 授权超时时间（30分钟）
#define kWebAuthHistoryKey          @"WebAuthHistory"     // 授权历史存储键

// 通知名称
#define kNotificationWebAuthSuccess @"NotificationWebAuthSuccess"  // Web授权成功通知
#define kNotificationWebAuthFailed  @"NotificationWebAuthFailed"   // Web授权失败通知
```

### Step 5: 安全性增强

#### 5.1 添加授权码加密

```objective-c
// 在 Utils 类中添加加密方法
// Utils.h
+ (NSString *)encryptAuthCode:(NSString *)authCode;
+ (BOOL)validateAuthCode:(NSString *)authCode;

// Utils.m
#import <CommonCrypto/CommonDigest.h>

+ (NSString *)encryptAuthCode:(NSString *)authCode {
    // 添加盐值
    NSString *salt = @"BRZY_WEB_AUTH_2024";
    NSString *combined = [NSString stringWithFormat:@"%@_%@", authCode, salt];
    
    // 生成SHA256哈希
    const char *cstr = [combined cStringUsingEncoding:NSUTF8StringEncoding];
    NSData *data = [NSData dataWithBytes:cstr length:combined.length];
    
    uint8_t digest[CC_SHA256_DIGEST_LENGTH];
    CC_SHA256(data.bytes, (CC_LONG)data.length, digest);
    
    NSMutableString *hash = [NSMutableString stringWithCapacity:CC_SHA256_DIGEST_LENGTH * 2];
    for (int i = 0; i < CC_SHA256_DIGEST_LENGTH; i++) {
        [hash appendFormat:@"%02x", digest[i]];
    }
    
    return hash;
}

+ (BOOL)validateAuthCode:(NSString *)authCode {
    // 验证授权码格式：时间戳_随机数_用户ID_设备ID
    NSArray *components = [authCode componentsSeparatedByString:@"_"];
    if (components.count != 4) {
        return NO;
    }
    
    // 验证时间戳（不能超过5分钟）
    NSTimeInterval timestamp = [components[0] doubleValue] / 1000.0;
    NSTimeInterval now = [[NSDate date] timeIntervalSince1970];
    if (fabs(now - timestamp) > 300) { // 5分钟
        return NO;
    }
    
    // 验证用户ID
    NSString *userId = components[2];
    if (![userId isEqualToString:[UserManager shareInstance].getUserId]) {
        return NO;
    }
    
    return YES;
}
```

### Step 6: 添加日志记录

```objective-c
// 在适当位置添加日志记录
#ifdef DEBUG
    NSLog(@"[WebAuth] 生成授权码: %@", authCode);
    NSLog(@"[WebAuth] 扫描二维码: %@", qrCode);
    NSLog(@"[WebAuth] 授权请求URL: %@", authUrl);
    NSLog(@"[WebAuth] 授权响应: %@", responseObject);
#endif
```

## 测试要点

### 功能测试
1. ✅ 扫码按钮正常显示
2. ✅ 点击扫码按钮能打开扫码界面
3. ✅ 扫描有效二维码能正确解析
4. ✅ 扫描无效二维码有错误提示
5. ✅ 授权确认对话框正常显示
6. ✅ 授权成功/失败有相应提示

### 兼容性测试
1. ✅ iOS 13.0 及以上版本正常运行
2. ✅ iPhone/iPad 设备适配
3. ✅ 暗黑模式下UI正常显示

### 安全性测试
1. ✅ 授权码时效性验证
2. ✅ 重复扫码处理
3. ✅ 网络异常处理
4. ✅ Token安全传输

### 性能测试
1. ✅ 扫码响应速度
2. ✅ 网络请求超时处理
3. ✅ 内存泄漏检查

## 注意事项

### 开发注意
1. 保持与现有代码风格一致
2. 使用项目已有的网络请求封装
3. 复用现有的扫码功能
4. 遵循项目的错误处理规范

### 安全注意
1. 不要在日志中打印敏感信息
2. 授权码必须有时效性
3. 使用HTTPS传输数据
4. 验证二维码来源

### 用户体验
1. 提供清晰的操作提示
2. 错误信息要友好易懂
3. 操作流程要简洁
4. 支持取消和重试

## 后续优化

### 可选功能
1. 授权历史查看
2. 授权设备管理
3. 自动过期清理
4. 批量患者授权

### 性能优化
1. 二维码预解析
2. 请求结果缓存
3. 失败重试机制
4. 请求队列管理

### 体验优化
1. 扫码动画效果
2. 授权成功动画
3. 快捷操作入口
4. 操作引导提示