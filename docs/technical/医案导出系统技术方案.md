# 必然中医医案导出系统技术方案

## 项目背景

由于后端系统无法进行变更，需要通过独立的FastAPI + Web项目来实现医案下载功能。医生可以在iOS客户端扫码登录Web端，查看并下载患者的医案信息。

## 系统架构

### 整体架构设计

```
┌─────────────┐     ┌──────────────┐     ┌─────────────┐     ┌──────────────┐
│   iOS App   │────▶│   FastAPI    │────▶│   Web App   │────▶│ 原始后端API  │
│  (扫码入口) │     │ (中间服务)   │     │ (展示下载)  │     │  (数据源)    │
└─────────────┘     └──────────────┘     └─────────────┘     └──────────────┘
```

### 认证流程

1. **iOS端生成授权码**：包含时间戳、随机数、用户ID
2. **扫码传递授权信息**：传递授权码和sessionToken到FastAPI
3. **FastAPI验证并生成临时token**：验证授权码有效性，生成30分钟有效期的临时token
4. **Web端使用临时token访问数据**：通过FastAPI代理请求原始API

## 技术栈选择

### iOS端
- **现有基础**：Objective-C
- **扫码功能**：AVFoundation（已实现）
- **网络请求**：AFNetworking 3.0.4（已集成）
- **最低支持版本**：iOS 13.0

### FastAPI后端
- **核心框架**：FastAPI 0.104.1
- **异步服务器**：Uvicorn 0.24.0
- **数据库**：MySQL + SQLAlchemy 2.0.23
- **缓存**：Redis 5.0.1
- **PDF生成**：ReportLab 4.0.7
- **二维码**：qrcode 7.4.2
- **JWT认证**：python-jose 3.3.0

### Web前端
- **框架**：Vue.js 3 + Vite
- **UI组件库**：Element Plus
- **状态管理**：Pinia
- **HTTP客户端**：Axios
- **WebSocket**：用于实时扫码状态同步

## 数据库设计

### 授权会话表（auth_sessions）
```sql
CREATE TABLE auth_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    auth_code VARCHAR(100) UNIQUE NOT NULL COMMENT '授权码',
    user_id VARCHAR(50) NOT NULL COMMENT '医生用户ID',
    session_token VARCHAR(500) NOT NULL COMMENT '原始sessionToken',
    temp_token VARCHAR(100) COMMENT '临时访问token',
    status ENUM('pending', 'authorized', 'expired') DEFAULT 'pending' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    expires_at TIMESTAMP COMMENT '过期时间',
    web_user_agent TEXT COMMENT 'Web端设备信息',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    INDEX idx_user_id (user_id),
    INDEX idx_temp_token (temp_token),
    INDEX idx_status_expires (status, expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='授权会话记录表';
```

### 患者数据缓存表（patient_cache）
```sql
CREATE TABLE patient_cache (
    user_id VARCHAR(50) NOT NULL COMMENT '医生用户ID',
    patient_id VARCHAR(50) NOT NULL COMMENT '患者ID',
    data JSON COMMENT '患者数据JSON',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (user_id, patient_id),
    INDEX idx_updated (updated_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='患者数据缓存表';
```

### 医案记录缓存表（medical_records_cache）
```sql
CREATE TABLE medical_records_cache (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(50) NOT NULL COMMENT '医生用户ID',
    patient_id VARCHAR(50) NOT NULL COMMENT '患者ID',
    record_type ENUM('wzd', 'fzd', 'prescription', 'message') COMMENT '记录类型',
    record_id VARCHAR(100) NOT NULL COMMENT '记录ID',
    data JSON COMMENT '记录数据JSON',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_record (user_id, patient_id, record_type, record_id),
    INDEX idx_patient (user_id, patient_id),
    INDEX idx_type_updated (record_type, updated_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='医案记录缓存表';
```

## API接口设计

### FastAPI端点

#### 1. 授权相关
```python
# 扫码授权
POST /api/auth/scan
{
    "auth_code": "1234567890_9999_userId",
    "session_token": "xxxxx",
    "device_info": {
        "app_version": "4.6.3",
        "os_version": "iOS 17.0"
    }
}

# 检查授权状态（Web端轮询）
GET /api/auth/status/{auth_code}

# 刷新临时token
POST /api/auth/refresh
{
    "temp_token": "current_temp_token"
}
```

#### 2. 患者管理
```python
# 获取患者列表
GET /api/patients?page=1&size=20&search=张三
Headers: Authorization: Bearer {temp_token}

# 获取患者详情
GET /api/patients/{patient_id}
Headers: Authorization: Bearer {temp_token}
```

#### 3. 医案数据
```python
# 获取患者医案列表
GET /api/medical-records/{patient_id}?type=all&start_date=2024-01-01
Headers: Authorization: Bearer {temp_token}

# 获取单个医案详情
GET /api/medical-records/{patient_id}/{record_id}
Headers: Authorization: Bearer {temp_token}
```

#### 4. PDF生成
```python
# 生成医案PDF
POST /api/pdf/generate
Headers: Authorization: Bearer {temp_token}
{
    "patient_id": "123456",
    "record_ids": ["rec1", "rec2", "rec3"],
    "options": {
        "include_images": true,
        "include_prescriptions": true,
        "date_range": {
            "start": "2024-01-01",
            "end": "2024-12-31"
        }
    }
}

# 下载PDF
GET /api/pdf/download/{task_id}
Headers: Authorization: Bearer {temp_token}
```

## 实施计划

### 第一阶段：iOS端改造（1-2天）

#### 任务1：添加扫码按钮
- 修改 `ChatViewController.m` 的 `configUI` 方法
- 使用 `rightBarButtonItems` 添加扫码按钮
- 实现 `clickScanButton:` 响应方法

#### 任务2：实现授权逻辑
```objective-c
// ChatViewController.m 新增方法
- (void)clickScanButton:(UIButton *)sender {
    // 生成授权码
    NSString *authCode = [self generateAuthCode];
    
    // 创建扫码控制器
    ScanQrcodeViewController *scanVC = [[ScanQrcodeViewController alloc] init];
    scanVC.scanCompleteBlock = ^(NSString *qrCode) {
        [self handleWebLoginQRCode:qrCode authCode:authCode];
    };
    
    [self.navigationController pushViewController:scanVC animated:YES];
}

- (NSString *)generateAuthCode {
    NSTimeInterval timestamp = [[NSDate date] timeIntervalSince1970];
    NSString *random = [NSString stringWithFormat:@"%d", arc4random_uniform(9999)];
    NSString *userId = [UserManager shareInstance].getUserId;
    return [NSString stringWithFormat:@"%ld_%@_%@", (long)timestamp, random, userId];
}

- (void)handleWebLoginQRCode:(NSString *)qrCode authCode:(NSString *)authCode {
    // 解析二维码内容，获取FastAPI地址
    NSDictionary *params = @{
        @"auth_code": authCode,
        @"session_token": [Config shareInstance].sessionToken,
        @"device_info": @{
            @"app_version": [UIApplication sharedApplication].appVersion,
            @"os_version": [[UIDevice currentDevice] systemVersion]
        }
    };
    
    // 发送授权请求到FastAPI
    [HTTPRequest POST:qrCodeURL parameters:params ...];
}
```

### 第二阶段：FastAPI项目搭建（2-3天）

#### 项目结构
```
brzy_medical_export/
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI主入口
│   ├── config.py            # 配置管理
│   ├── database.py          # 数据库连接
│   ├── models/              # 数据模型
│   │   ├── __init__.py
│   │   ├── auth.py          # 认证相关模型
│   │   ├── patient.py       # 患者数据模型
│   │   └── medical.py       # 医案数据模型
│   ├── schemas/             # Pydantic模型
│   │   ├── __init__.py
│   │   ├── auth.py
│   │   ├── patient.py
│   │   └── medical.py
│   ├── api/                 # API端点
│   │   ├── __init__.py
│   │   ├── auth.py          # 认证接口
│   │   ├── patients.py      # 患者接口
│   │   ├── medical.py       # 医案接口
│   │   └── pdf.py           # PDF生成接口
│   ├── services/            # 业务逻辑
│   │   ├── __init__.py
│   │   ├── auth_service.py  # 认证服务
│   │   ├── api_proxy.py     # API代理服务
│   │   ├── cache_service.py # 缓存服务
│   │   └── pdf_service.py   # PDF生成服务
│   └── utils/               # 工具函数
│       ├── __init__.py
│       ├── security.py      # 安全相关
│       └── pdf_generator.py # PDF生成器
├── tests/                   # 测试文件
├── alembic/                 # 数据库迁移
├── requirements.txt         # 依赖列表
├── .env                     # 环境变量
└── docker-compose.yml       # Docker配置
```

#### 核心代码示例

**认证服务**
```python
# app/services/auth_service.py
from datetime import datetime, timedelta
from jose import jwt
import secrets

class AuthService:
    def __init__(self, db_session, redis_client):
        self.db = db_session
        self.redis = redis_client
        
    async def create_auth_session(self, auth_code: str, session_token: str, user_id: str):
        # 验证授权码格式
        if not self._validate_auth_code(auth_code):
            raise ValueError("Invalid auth code")
            
        # 生成临时token
        temp_token = self._generate_temp_token()
        expires_at = datetime.utcnow() + timedelta(minutes=30)
        
        # 保存到数据库
        auth_session = AuthSession(
            auth_code=auth_code,
            user_id=user_id,
            session_token=session_token,
            temp_token=temp_token,
            expires_at=expires_at,
            status='pending'
        )
        self.db.add(auth_session)
        self.db.commit()
        
        # 缓存到Redis
        self.redis.setex(
            f"auth:{temp_token}",
            1800,  # 30分钟
            json.dumps({
                "user_id": user_id,
                "session_token": session_token
            })
        )
        
        return temp_token
        
    def _generate_temp_token(self):
        return secrets.token_urlsafe(32)
```

**API代理服务**
```python
# app/services/api_proxy.py
import httpx
from app.config import settings

class APIProxyService:
    def __init__(self):
        self.base_url = settings.ORIGINAL_API_URL
        
    async def get_patient_list(self, session_token: str, user_id: str, page: int = 1):
        params = {
            "method_code": "000xxx",  # 实际的方法代码
            "userId": user_id,
            "sessionToken": session_token,
            "page": page,
            "pageSize": 20
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(self.base_url, json=params)
            return response.json()
            
    async def get_medical_records(self, session_token: str, patient_id: str):
        # 获取处方记录
        prescriptions = await self._get_prescriptions(session_token, patient_id)
        
        # 获取问诊单
        wzd_records = await self._get_wzd_records(session_token, patient_id)
        
        # 获取聊天记录
        messages = await self._get_chat_messages(session_token, patient_id)
        
        return {
            "prescriptions": prescriptions,
            "wzd_records": wzd_records,
            "messages": messages
        }
```

### 第三阶段：Web端开发（2-3天）

#### 项目结构
```
brzy-medical-web/
├── src/
│   ├── api/                 # API请求封装
│   │   ├── auth.js
│   │   ├── patients.js
│   │   └── medical.js
│   ├── components/          # 组件
│   │   ├── QRCodeLogin.vue  # 二维码登录
│   │   ├── PatientList.vue  # 患者列表
│   │   ├── MedicalRecord.vue # 医案详情
│   │   └── PDFPreview.vue   # PDF预览
│   ├── views/               # 页面
│   │   ├── Login.vue
│   │   ├── Dashboard.vue
│   │   └── MedicalExport.vue
│   ├── stores/              # 状态管理
│   │   ├── auth.js
│   │   └── patient.js
│   ├── utils/               # 工具函数
│   │   ├── websocket.js
│   │   └── pdf.js
│   ├── router/              # 路由
│   ├── App.vue
│   └── main.js
├── public/
├── package.json
├── vite.config.js
└── .env
```

#### 核心组件示例

**二维码登录组件**
```vue
<!-- src/components/QRCodeLogin.vue -->
<template>
  <div class="qr-login">
    <h2>扫码登录</h2>
    <div class="qr-container">
      <div v-if="!isExpired" class="qr-code">
        <qrcode-vue :value="qrCodeUrl" :size="200" level="H" />
        <div v-if="isScanned" class="scan-success">
          <el-icon><SuccessFilled /></el-icon>
          <p>扫码成功，请在手机上确认</p>
        </div>
      </div>
      <div v-else class="qr-expired">
        <p>二维码已过期</p>
        <el-button @click="refreshQRCode">刷新二维码</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import QrcodeVue from 'qrcode.vue'
import { useAuthStore } from '@/stores/auth'
import { connectWebSocket } from '@/utils/websocket'

const authStore = useAuthStore()
const qrCodeUrl = ref('')
const isExpired = ref(false)
const isScanned = ref(false)
let ws = null

const generateQRCode = async () => {
  const authCode = Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  qrCodeUrl.value = `brzy://scan-login?code=${authCode}&server=${import.meta.env.VITE_API_URL}`
  
  // 建立WebSocket连接监听扫码状态
  ws = connectWebSocket(authCode, {
    onScanned: () => {
      isScanned.value = true
    },
    onAuthorized: (data) => {
      authStore.setToken(data.tempToken)
      authStore.setUserInfo(data.userInfo)
      router.push('/dashboard')
    },
    onExpired: () => {
      isExpired.value = true
    }
  })
}

onMounted(() => {
  generateQRCode()
})

onUnmounted(() => {
  if (ws) ws.close()
})
</script>
```

### 第四阶段：PDF生成功能（2天）

#### PDF生成服务
```python
# app/utils/pdf_generator.py
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import io

class MedicalPDFGenerator:
    def __init__(self):
        # 注册中文字体
        pdfmetrics.registerFont(TTFont('SimSun', 'fonts/SimSun.ttf'))
        self.styles = getSampleStyleSheet()
        self.styles.add(ParagraphStyle(
            name='ChineseTitle',
            fontName='SimSun',
            fontSize=16,
            alignment=1  # 居中
        ))
        self.styles.add(ParagraphStyle(
            name='ChineseBody',
            fontName='SimSun',
            fontSize=10
        ))
        
    def generate_medical_record_pdf(self, patient_info, medical_records):
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4)
        story = []
        
        # 标题
        title = Paragraph(f"{patient_info['name']}医案记录", self.styles['ChineseTitle'])
        story.append(title)
        story.append(Spacer(1, 20))
        
        # 患者基本信息
        patient_data = [
            ['姓名', patient_info['name'], '性别', patient_info['sex']],
            ['年龄', patient_info['age'], '电话', patient_info.get('mobile', '未填写')],
        ]
        patient_table = Table(patient_data, colWidths=[60, 100, 60, 100])
        patient_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'SimSun'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.grey),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('BACKGROUND', (2, 0), (2, -1), colors.lightgrey),
        ]))
        story.append(patient_table)
        story.append(Spacer(1, 20))
        
        # 处方记录
        for record in medical_records['prescriptions']:
            story.append(self._create_prescription_section(record))
            story.append(Spacer(1, 15))
            
        # 问诊记录
        for record in medical_records['wzd_records']:
            story.append(self._create_wzd_section(record))
            story.append(Spacer(1, 15))
            
        doc.build(story)
        buffer.seek(0)
        return buffer
        
    def _create_prescription_section(self, prescription):
        elements = []
        
        # 处方标题
        title = Paragraph(f"处方 - {prescription['create_time']}", self.styles['ChineseBody'])
        elements.append(title)
        
        # 诊断
        diagnosis = Paragraph(f"诊断：{prescription['diagnosis']}", self.styles['ChineseBody'])
        elements.append(diagnosis)
        
        # 药材列表
        drug_data = [['药材名称', '剂量', '单位', '备注']]
        for drug in prescription['drugs']:
            drug_data.append([
                drug['name'],
                drug['dose'],
                drug['unit'],
                drug.get('note', '')
            ])
            
        drug_table = Table(drug_data, colWidths=[150, 60, 60, 150])
        drug_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'SimSun'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 1, colors.grey),
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
        ]))
        elements.append(drug_table)
        
        return elements
```

### 第五阶段：安全性和性能优化（1天）

#### 安全措施
1. **HTTPS强制**：所有通信使用HTTPS
2. **Token时效性**：临时token 30分钟过期
3. **访问日志**：记录所有数据访问行为
4. **IP限制**：可选的IP白名单功能
5. **数据脱敏**：导出时隐藏敏感信息

#### 性能优化
1. **Redis缓存**：缓存热点数据
2. **分页加载**：大数据量分页处理
3. **异步处理**：PDF生成异步任务队列
4. **CDN加速**：静态资源CDN分发
5. **数据压缩**：启用gzip压缩

## 部署方案

### Docker部署配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  fastapi:
    build: ./fastapi
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=mysql://user:pass@db:3306/brzy_export
      - REDIS_URL=redis://redis:6379
      - ORIGINAL_API_URL=https://api.haoniuzhongyi.top:9090
    depends_on:
      - db
      - redis
      
  web:
    build: ./web
    ports:
      - "80:80"
    environment:
      - VITE_API_URL=https://export-api.haoniuzhongyi.top
      
  db:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=root123
      - MYSQL_DATABASE=brzy_export
    volumes:
      - mysql_data:/var/lib/mysql
      
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
      
volumes:
  mysql_data:
  redis_data:
```

### Nginx配置
```nginx
# nginx.conf
server {
    listen 443 ssl http2;
    server_name export.haoniuzhongyi.top;
    
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    
    # Web前端
    location / {
        proxy_pass http://web:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://fastapi:8000/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    
    # WebSocket支持
    location /ws/ {
        proxy_pass http://fastapi:8000/ws/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

## 监控和维护

### 日志系统
- **应用日志**：使用Python logging模块
- **访问日志**：Nginx access log
- **错误日志**：集中错误日志收集
- **审计日志**：敏感操作审计追踪

### 监控指标
- **API响应时间**：P50/P90/P99延迟
- **错误率**：4xx/5xx错误比例
- **并发用户数**：实时在线用户统计
- **PDF生成队列**：任务队列长度监控

### 备份策略
- **数据库备份**：每日全量备份，保留7天
- **Redis备份**：AOF持久化
- **日志归档**：30天自动归档

## 风险评估与应对

### 技术风险
| 风险项 | 可能性 | 影响 | 应对措施 |
|-------|-------|------|---------|
| 原API限流 | 中 | 高 | 实现请求队列和重试机制 |
| PDF生成性能瓶颈 | 中 | 中 | 使用任务队列异步处理 |
| 数据同步延迟 | 低 | 中 | 增加缓存更新频率 |
| 安全漏洞 | 低 | 高 | 定期安全审计和更新 |

### 业务风险
| 风险项 | 可能性 | 影响 | 应对措施 |
|-------|-------|------|---------|
| 用户接受度低 | 中 | 中 | 提供详细使用指南 |
| 数据准确性 | 低 | 高 | 数据校验和用户确认 |
| 合规性问题 | 低 | 高 | 咨询法务确保合规 |

## 项目里程碑

### 第一周
- [x] 技术方案确定
- [ ] iOS端扫码功能开发
- [ ] FastAPI项目搭建
- [ ] 数据库设计实现

### 第二周
- [ ] API接口开发完成
- [ ] Web前端基础功能
- [ ] PDF生成功能实现
- [ ] 集成测试

### 第三周
- [ ] 安全性测试
- [ ] 性能优化
- [ ] 部署上线
- [ ] 用户培训

## 总结

本方案通过独立的FastAPI中间层服务，巧妙地绕过了后端无法修改的限制，实现了医案导出功能。整体架构清晰，技术栈成熟，实施风险可控。预计3周内可以完成全部开发和部署工作。