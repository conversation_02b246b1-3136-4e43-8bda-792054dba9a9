# iOS API 请求参数对比分析

## 问题分析

从后端日志分析，发现了关键问题：

### 1. 设备标识符被截断
- **实际值**: `60BA14A4-58A0-4E` (17个字符)
- **期望值**: `60BA14A4-58A0-4E3F-831C-F4B0F62EB8D4` (36个字符)

### 2. API 请求参数对比

#### 失败的请求 (000117 - 处方列表)
```json
{
  "method_code": "000117",
  "userId": "0000000025073",
  "sessionToken": "1748765765876-BEPuIUTkoNZXqCEK",
  "app_version": "4.6.3",
  "imei": "60BA14A4-58A0-4E",        // ❌ 被截断
  "mac": "60BA14A4-58A0-4E",         // ❌ 被截断
  "sys_type": "2",
  "userType": "1",
  "terminalType": "biran",
  "patientId": "9000000087735",
  "page": "1",
  "pageSize": "20",
  "doctorId": "0000000025073",
  "tel": "88025668998"
}
```

#### 成功的请求 (000116 - 患者详情)
```json
{
  "method_code": "000116",
  "userId": "0000000025073",
  "sessionToken": "1748765765876-BEPuIUTkoNZXqCEK",
  "app_version": "4.6.3",
  "imei": "60BA14A4-58A0-4E",        // ✓ 即使截断也能工作
  "mac": "60BA14A4-58A0-4E",         // ✓ 即使截断也能工作
  "sys_type": "2",
  "userType": "1",
  "terminalType": "biran",
  "patientId": "9000000087735",
  "doctorId": "0000000025073",
  "tel": "88025668998"
}
```

## 关键发现

1. **请求方式**: GET 请求，参数在 URL Query String 中（✓ 正确）
2. **参数键名**: 全部使用驼峰命名法（✓ 正确）
3. **必需参数**: 都包含了所有必需参数（✓ 正确）
4. **设备标识符**: imei 和 mac 被截断（❌ 问题所在）

## 不同 API 的敏感度差异

- **000116 (患者详情)**: 对设备标识符长度不敏感，能正常返回数据
- **000117 (处方列表)**: 对设备标识符长度敏感，返回"系统异常"
- **000233 (医案列表)**: 预期同样对设备标识符长度敏感
- **000228 (医案详情)**: 预期同样对设备标识符长度敏感

## 解决方案

### 方案一：修复 iOS 端（推荐）
确保 iOS 端传递完整的 36 字符 UUID：
```objective-c
// 获取完整的设备标识符
NSString *deviceId = [[[UIDevice currentDevice] identifierForVendor] UUIDString];
// 输出: 60BA14A4-58A0-4E3F-831C-F4B0F62EB8D4
```

### 方案二：后端强制修正（已实施）
在 `api_proxy.py` 中检测并修正被截断的标识符：
```python
if imei and len(imei) < 36:
    logger.warning(f"IMEI truncated: {imei} (length: {len(imei)}), using default")
    imei = "60BA14A4-58A0-4E3F-831C-F4B0F62EB8D4"

if mac and len(mac) < 36:
    logger.warning(f"MAC truncated: {mac} (length: {len(mac)}), using default")
    mac = "0F535656-8B87-4A08-825E-6654D2F32344"
```

## 验证方法

1. **检查日志**：
   ```bash
   grep "truncated" backend.log
   ```

2. **测试 API**：
   - 测试 000233 (医案列表)
   - 测试 000228 (医案详情)

3. **预期结果**：
   - 看到 "IMEI truncated" 警告
   - API 正常返回数据（使用默认的完整 UUID）