# Redis 缓存服务解决方案

## 问题分析

扫码错误的根本原因是 Redis 服务未运行，导致：
1. 缓存服务连接失败：`Connection closed by server`
2. Session 无法正确存储和获取
3. 设备信息（device_id）被截断：`60BA14A4-58A0-4E`

## 解决方案

### 方案一：安装并启动 Redis（推荐）

1. **安装 Redis**
```bash
# macOS
brew install redis

# 或使用 MacPorts
sudo port install redis
```

2. **启动 Redis 服务**
```bash
# 启动 Redis
redis-server

# 或作为后台服务启动
brew services start redis
```

3. **验证 Redis 运行状态**
```bash
redis-cli ping
# 应返回: PONG
```

### 方案二：使缓存服务可选（临时方案）

如果暂时无法安装 Redis，可以修改代码使缓存服务变为可选：

1. **修改 auth_service.py**
让系统在 Redis 不可用时继续工作，使用数据库作为唯一存储。

2. **修改 cache_service.py**
添加连接重试和故障转移逻辑。

## 其他相关问题

### 1. WebSocket 消息类型警告
日志显示：`Unknown message type: auth`

这只是警告，不影响功能。前端发送了 `auth` 类型的消息，但后端 WebSocket 处理器不识别这个类型。

### 2. 设备ID截断问题
设备信息被截断的问题已在之前的修复中解决，但需要 Redis 正常运行才能生效。

## 建议操作步骤

1. **立即操作**：安装并启动 Redis
2. **验证服务**：确认 Redis 正常运行
3. **测试扫码**：重新测试扫码登录功能
4. **监控日志**：观察是否还有缓存相关错误

## 监控命令

```bash
# 查看 Redis 状态
redis-cli info server

# 监控 Redis 连接
redis-cli monitor

# 查看后端日志中的缓存错误
tail -f backend.log | grep -i "cache"
```