# iOS 端设备信息传递修复指南

## 问题描述

当前 iOS 端在扫码认证时传递的设备信息被截断：
- `device_id`: "60BA14A4-58A0-4E" (只有17个字符)
- `imei`: "60BA14A4-58A0-4E" (同样被截断)
- `mac`: "60BA14A4-58A0-4E" (同样被截断)

完整的 UUID 应该是 36 个字符，格式如：`60BA14A4-58A0-4E00-8000-000000000000`

## 修复方案

### 1. iOS 端扫码认证请求修复

在 iOS 端发送扫码认证请求时，确保传递完整的设备信息：

```swift
// 扫码认证请求结构
struct ScanAuthRequest {
    let auth_code: String
    let session_token: String
    let web_session_id: String
    let device_info: DeviceInfo
    let current_patient_id: String?
    let current_patient_name: String?
}

// 设备信息结构
struct DeviceInfo {
    let app_version: String
    let os_version: String
    let device_model: String?
    let platform: String?
    let device_id: String?    // 完整的设备标识符
    let imei: String?         // 完整的 IMEI
    let mac: String?          // 完整的 MAC 地址
    let tel: String?          // 手机号
}

// 构建设备信息
func buildDeviceInfo() -> DeviceInfo {
    return DeviceInfo(
        app_version: "4.6.3",
        os_version: UIDevice.current.systemVersion,
        device_model: UIDevice.current.model,
        platform: "iOS",
        device_id: UIDevice.current.identifierForVendor?.uuidString ?? "",  // 获取完整的 UUID
        imei: getDeviceIMEI() ?? UIDevice.current.identifierForVendor?.uuidString ?? "",
        mac: getDeviceMAC() ?? generateRandomUUID(),  // 如果获取不到 MAC，生成随机 UUID
        tel: getUserPhoneNumber() ?? ""
    )
}

// 生成随机 UUID (备用方案)
func generateRandomUUID() -> String {
    return UUID().uuidString
}
```

### 2. 发送认证请求

```swift
func sendScanAuthRequest(authCode: String, sessionToken: String, webSessionId: String) {
    let url = "http://your-backend-url/api/auth/scan"
    
    let deviceInfo = buildDeviceInfo()
    
    let requestBody = ScanAuthRequest(
        auth_code: authCode,
        session_token: sessionToken,
        web_session_id: webSessionId,
        device_info: deviceInfo,
        current_patient_id: currentPatientId,
        current_patient_name: currentPatientName
    )
    
    // 发送 POST 请求
    var request = URLRequest(url: URL(string: url)!)
    request.httpMethod = "POST"
    request.setValue("application/json", forHTTPHeaderField: "Content-Type")
    request.httpBody = try? JSONEncoder().encode(requestBody)
    
    URLSession.shared.dataTask(with: request) { data, response, error in
        // 处理响应
    }.resume()
}
```

### 3. 调试日志

在 iOS 端添加调试日志以验证设备信息：

```swift
func logDeviceInfo(_ deviceInfo: DeviceInfo) {
    print("=== Device Info Debug ===")
    print("app_version: \(deviceInfo.app_version)")
    print("os_version: \(deviceInfo.os_version)")
    print("device_model: \(deviceInfo.device_model ?? "N/A")")
    print("platform: \(deviceInfo.platform ?? "N/A")")
    print("device_id: \(deviceInfo.device_id ?? "N/A") - Length: \(deviceInfo.device_id?.count ?? 0)")
    print("imei: \(deviceInfo.imei ?? "N/A") - Length: \(deviceInfo.imei?.count ?? 0)")
    print("mac: \(deviceInfo.mac ?? "N/A") - Length: \(deviceInfo.mac?.count ?? 0)")
    print("tel: \(deviceInfo.tel ?? "N/A")")
    print("========================")
}
```

## 后端兼容性处理

虽然主要问题在 iOS 端，但后端也应该做好兼容性处理：

```python
# 在 api_proxy.py 中的设备信息处理
if device_info:
    app_version = device_info.get("app_version", "4.6.3")
    
    # 处理可能被截断的设备标识符
    device_id = device_info.get("device_id", "")
    imei = device_info.get("imei", device_id)
    mac = device_info.get("mac", device_id)
    
    # 如果设备标识符太短，尝试补全或使用默认值
    if len(imei) < 36:
        logger.warning(f"IMEI too short: {imei}, using default")
        imei = "60BA14A4-58A0-4E3F-831C-F4B0F62EB8D4"
    
    if len(mac) < 36:
        logger.warning(f"MAC too short: {mac}, using default")
        mac = "0F535656-8B87-4A08-825E-6654D2F32344"
```

## 测试步骤

1. **iOS 端测试**
   - 打印设备信息日志，确认长度和格式
   - 使用网络调试工具（如 Charles）查看实际发送的请求内容

2. **后端测试**
   - 查看后端日志，确认收到的设备信息
   - 验证 API 请求中的 imei 和 mac 参数

3. **端到端测试**
   - iOS 扫码登录
   - 检查后端日志中的设备信息
   - 验证医案列表和详情接口是否正常工作

## 注意事项

1. iOS 14+ 对设备标识符有限制，可能无法获取真实的 MAC 地址
2. 使用 `identifierForVendor` 作为设备标识符是推荐的做法
3. 确保设备信息在整个会话期间保持一致
4. 考虑将设备信息缓存在 iOS 端，避免每次都重新生成

## 相关文件

- iOS 端：扫码认证相关代码
- 后端：`app/api/auth.py`, `app/services/api_proxy.py`
- 数据模型：`app/schemas/auth.py`