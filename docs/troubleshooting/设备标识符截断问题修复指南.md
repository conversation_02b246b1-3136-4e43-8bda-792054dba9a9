# 设备标识符截断问题修复指南

## 问题描述

在 iOS 端扫码授权后，Web 端调用某些 API（如 000117 获取处方列表）时返回"系统异常"错误。

### 根本原因

iOS 设备标识符（UUID）在传递过程中被截断：
- 原始 UUID：`60BA14A4-58A0-4E3F-831C-F4B0F62EB8D4`（36字符）
- 截断后：`60BA14A4-58A0-4E`（17字符）

某些 API 接口对设备标识符的长度有严格要求，导致请求失败。

## 问题分析

### 1. 日志证据
```
device_info: {
  "app_version": "4.6.3",
  "os_version": "18.3.2", 
  "device_model": "iPhone",
  "platform": "iOS",
  "device_id": "60BA14A4-58A0-4E",
  "imei": "60BA14A4-58A0-4E",    // 被截断
  "mac": "60BA14A4-58A0-4E",     // 被截断
  "tel": null
}
```

### 2. API 对比
- **000116（获取患者详情）**：✅ 成功（对设备标识符长度不敏感）
- **000117（获取处方列表）**：❌ 失败（需要完整的设备标识符）

### 3. 后端保护机制失效
虽然 `api_proxy.py` 有检测截断的逻辑，但检测条件不够严格。

## 修复方案

### 方案一：iOS 端修复（推荐）

#### 1. 修改设备信息收集逻辑

**文件：** iOS 项目中的授权相关代码

```objective-c
// 原代码（可能导致截断）
NSString *deviceId = [UIDevice currentDevice].identifierForVendor.UUIDString;
NSString *shortDeviceId = [deviceId substringToIndex:8];  // 不要截断！

// 修复后
NSString *deviceId = [UIDevice currentDevice].identifierForVendor.UUIDString;
// 使用完整的 deviceId，不要截断

// 设备信息
NSMutableDictionary *deviceInfo = [NSMutableDictionary dictionary];
[deviceInfo setObject:appVersion forKey:@"app_version"];
[deviceInfo setObject:osVersion forKey:@"os_version"];
[deviceInfo setObject:deviceModel forKey:@"device_model"];
[deviceInfo setObject:@"iOS" forKey:@"platform"];
[deviceInfo setObject:deviceId forKey:@"device_id"];  // 完整 UUID
[deviceInfo setObject:deviceId forKey:@"imei"];       // 完整 UUID
[deviceInfo setObject:deviceId forKey:@"mac"];        // 完整 UUID
```

#### 2. 验证设备标识符格式

```objective-c
// 添加 UUID 格式验证
- (BOOL)isValidUUID:(NSString *)uuid {
    NSString *uuidRegex = @"^[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}$";
    NSPredicate *uuidTest = [NSPredicate predicateWithFormat:@"SELF MATCHES[c] %@", uuidRegex];
    return [uuidTest evaluateWithObject:uuid];
}

// 使用验证
NSString *deviceId = [UIDevice currentDevice].identifierForVendor.UUIDString;
if (![self isValidUUID:deviceId]) {
    NSLog(@"警告：设备ID格式无效：%@", deviceId);
    // 使用默认值或处理错误
}
```

### 方案二：后端增强修复（已实施）

#### 1. 更严格的检测逻辑

**文件：** `/app/services/api_proxy.py`

```python
# 已更新的代码
if device_info:
    app_version = device_info.get("app_version", "4.6.3")
    imei = device_info.get("imei") or device_info.get("device_id", "")
    mac = device_info.get("mac") or device_info.get("device_id", "")
    
    # iOS UUIDs 应该是 36 字符长 (8-4-4-4-12 格式)
    if not imei or len(imei) < 36:
        if imei:
            logger.warning(f"IMEI truncated or invalid: {imei} (length: {len(imei)}), using default")
        imei = "60BA14A4-58A0-4E3F-831C-F4B0F62EB8D4"
    
    if not mac or len(mac) < 36:
        if mac:
            logger.warning(f"MAC truncated or invalid: {mac} (length: {len(mac)}), using default")
        mac = "0F535656-8B87-4A08-825E-6654D2F32344"
```

#### 2. 添加 UUID 格式验证

```python
import re

def is_valid_uuid(uuid_string):
    """验证 UUID 格式"""
    uuid_pattern = re.compile(
        r'^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$'
    )
    return bool(uuid_pattern.match(uuid_string))

# 在 api_proxy.py 中使用
if not is_valid_uuid(imei):
    logger.warning(f"Invalid UUID format for IMEI: {imei}")
    imei = "60BA14A4-58A0-4E3F-831C-F4B0F62EB8D4"
```

### 方案三：数据库层面修复

对于已存储的截断数据：

```sql
-- 查找被截断的设备信息
SELECT * FROM auth_sessions 
WHERE LENGTH(JSON_EXTRACT(device_info, '$.mac')) < 36;

-- 更新为默认值
UPDATE auth_sessions 
SET device_info = JSON_SET(
    device_info,
    '$.imei', '60BA14A4-58A0-4E3F-831C-F4B0F62EB8D4',
    '$.mac', '0F535656-8B87-4A08-825E-6654D2F32344'
)
WHERE LENGTH(JSON_EXTRACT(device_info, '$.mac')) < 36;
```

## 测试验证

### 1. iOS 端验证
```objective-c
// 在发送请求前打印设备信息
NSLog(@"Device Info: %@", deviceInfo);
// 确保 imei 和 mac 都是 36 字符的完整 UUID
```

### 2. 后端验证
```python
# 在 api_proxy.py 中添加日志
logger.info(f"Device identifiers - IMEI: {imei} (len: {len(imei)}), MAC: {mac} (len: {len(mac)})")
```

### 3. API 测试
测试以下接口确保都能正常工作：
- 000116：获取患者详情 ✅
- 000117：获取处方列表 ✅
- 000233：获取医案列表 ✅
- 000228：获取医案详情 ✅

## 长期建议

1. **标准化设备标识符处理**
   - iOS 端统一使用完整 UUID
   - 后端统一验证 UUID 格式
   - 文档化设备标识符要求

2. **API 参数验证**
   - 在 API 网关层添加参数验证
   - 对设备标识符长度进行统一检查
   - 返回更明确的错误信息

3. **监控和告警**
   - 监控截断的设备标识符
   - 设置告警阈值
   - 定期审查日志

## 总结

设备标识符截断问题是导致部分 API 调用失败的根本原因。通过：
1. iOS 端避免截断设备 UUID
2. 后端增强验证和自动修复
3. 数据库层面的数据清理

可以彻底解决这个问题，确保所有 API 调用正常工作。