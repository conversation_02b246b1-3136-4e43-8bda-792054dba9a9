#!/bin/bash

# ===========================================
# 必然中医 - 医案导出系统 生产环境停止脚本
# ===========================================

set -e

echo "🛑 停止必然中医医案导出系统 (生产环境)"
echo "================================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
RUNTIME_DIR="$PROJECT_ROOT/runtime"

# PID文件路径
BACKEND_PID_FILE="$RUNTIME_DIR/pids/backend.pid"
FRONTEND_PID_FILE="$RUNTIME_DIR/pids/frontend.pid"

# 停止函数
stop_service() {
    local service_name=$1
    local pid_file=$2
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        
        if ps -p "$pid" > /dev/null 2>&1; then
            echo -e "${YELLOW}🛑 停止${service_name}服务 (PID: $pid)...${NC}"
            
            # 优雅停止
            kill "$pid"
            
            # 等待进程停止
            local count=0
            while ps -p "$pid" > /dev/null 2>&1 && [ $count -lt 10 ]; do
                sleep 1
                count=$((count + 1))
                echo -e "${BLUE}⏳ 等待${service_name}服务停止... ($count/10)${NC}"
            done
            
            # 如果进程仍然存在，强制杀死
            if ps -p "$pid" > /dev/null 2>&1; then
                echo -e "${YELLOW}🔨 强制停止${service_name}服务...${NC}"
                kill -9 "$pid"
                sleep 1
            fi
            
            if ps -p "$pid" > /dev/null 2>&1; then
                echo -e "${RED}❌ ${service_name}服务停止失败${NC}"
                return 1
            else
                echo -e "${GREEN}✅ ${service_name}服务已停止${NC}"
                rm -f "$pid_file"
                return 0
            fi
        else
            echo -e "${YELLOW}⚠️ ${service_name}服务进程不存在 (PID: $pid)${NC}"
            rm -f "$pid_file"
            return 0
        fi
    else
        echo -e "${BLUE}ℹ️ ${service_name}服务未运行${NC}"
        return 0
    fi
}

# 检查是否有服务在运行
if [ ! -f "$BACKEND_PID_FILE" ] && [ ! -f "$FRONTEND_PID_FILE" ]; then
    echo -e "${BLUE}ℹ️ 没有检测到运行中的服务${NC}"
    exit 0
fi

# 停止后端服务
echo -e "${BLUE}🔧 停止后端服务...${NC}"
stop_service "后端" "$BACKEND_PID_FILE"
BACKEND_STOP_STATUS=$?

# 停止前端服务
echo -e "${BLUE}🔧 停止前端服务...${NC}"
stop_service "前端" "$FRONTEND_PID_FILE"
FRONTEND_STOP_STATUS=$?

# 额外清理：查找并停止可能的孤儿进程
echo -e "${BLUE}🧹 清理孤儿进程...${NC}"

# 查找并停止gunicorn进程
GUNICORN_PIDS=$(pgrep -f "gunicorn.*app.main:app" 2>/dev/null || true)
if [ -n "$GUNICORN_PIDS" ]; then
    echo -e "${YELLOW}🛑 发现Gunicorn进程，正在停止...${NC}"
    echo "$GUNICORN_PIDS" | xargs kill -TERM 2>/dev/null || true
    sleep 2
    # 强制停止仍在运行的进程
    REMAINING_PIDS=$(pgrep -f "gunicorn.*app.main:app" 2>/dev/null || true)
    if [ -n "$REMAINING_PIDS" ]; then
        echo "$REMAINING_PIDS" | xargs kill -9 2>/dev/null || true
    fi
fi

# 查找并停止serve进程
SERVE_PIDS=$(pgrep -f "serve.*dist" 2>/dev/null || true)
if [ -n "$SERVE_PIDS" ]; then
    echo -e "${YELLOW}🛑 发现serve进程，正在停止...${NC}"
    echo "$SERVE_PIDS" | xargs kill -TERM 2>/dev/null || true
    sleep 1
    # 强制停止仍在运行的进程
    REMAINING_SERVE_PIDS=$(pgrep -f "serve.*dist" 2>/dev/null || true)
    if [ -n "$REMAINING_SERVE_PIDS" ]; then
        echo "$REMAINING_SERVE_PIDS" | xargs kill -9 2>/dev/null || true
    fi
fi

# 查找并停止uvicorn进程
UVICORN_PIDS=$(pgrep -f "uvicorn.*app.main:app" 2>/dev/null || true)
if [ -n "$UVICORN_PIDS" ]; then
    echo -e "${YELLOW}🛑 发现Uvicorn进程，正在停止...${NC}"
    echo "$UVICORN_PIDS" | xargs kill -TERM 2>/dev/null || true
    sleep 1
    # 强制停止仍在运行的进程
    REMAINING_UVICORN_PIDS=$(pgrep -f "uvicorn.*app.main:app" 2>/dev/null || true)
    if [ -n "$REMAINING_UVICORN_PIDS" ]; then
        echo "$REMAINING_UVICORN_PIDS" | xargs kill -9 2>/dev/null || true
    fi
fi

echo -e "${GREEN}✅ 孤儿进程清理完成${NC}"

# 清理临时文件
echo -e "${BLUE}🧹 清理临时文件...${NC}"
if [ -d "$PROJECT_ROOT/brzy_medical_export/temp" ]; then
    rm -rf "$PROJECT_ROOT/brzy_medical_export/temp/*" 2>/dev/null || true
fi

echo "================================================"

# 显示结果
if [ $BACKEND_STOP_STATUS -eq 0 ] && [ $FRONTEND_STOP_STATUS -eq 0 ]; then
    echo -e "${GREEN}🎉 所有服务已成功停止！${NC}"
    exit_code=0
else
    echo -e "${YELLOW}⚠️ 部分服务停止时遇到问题${NC}"
    exit_code=1
fi

echo ""
echo -e "${BLUE}📊 服务状态检查:${NC}"

# 检查端口是否还在被占用
if netstat -an 2>/dev/null | grep -q ":8000.*LISTEN" || lsof -i :8000 2>/dev/null | grep -q LISTEN; then
    echo -e "  🔴 端口8000仍在使用"
else
    echo -e "  🟢 端口8000已释放"
fi

if netstat -an 2>/dev/null | grep -q ":5000.*LISTEN" || lsof -i :5000 2>/dev/null | grep -q LISTEN; then
    echo -e "  🔴 端口5000仍在使用"
else
    echo -e "  🟢 端口5000已释放"
fi

echo ""
echo -e "${BLUE}🛠️ 启动命令:${NC}"
echo -e "  🚀 启动后端: ${YELLOW}$PROJECT_ROOT/scripts/start_prod_backend.sh${NC}"
echo -e "  🚀 启动前端: ${YELLOW}$PROJECT_ROOT/scripts/start_prod_frontend.sh${NC}"
echo -e "  🚀 启动全部: ${YELLOW}$PROJECT_ROOT/scripts/start_prod.sh${NC}"
echo ""
echo -e "${GREEN}🎯 生产环境已停止！${NC}"

exit $exit_code