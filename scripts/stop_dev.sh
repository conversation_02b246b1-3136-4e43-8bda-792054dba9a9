#!/bin/bash

# ===========================================
# 必然中医 - 医案导出系统 开发环境停止脚本
# ===========================================

set -e

echo "🛑 停止必然中医医案导出系统 (开发环境)"
echo "================================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# 停止后端服务
echo -e "${YELLOW}🔧 停止后端服务...${NC}"
if [ -f "$PROJECT_ROOT/runtime/pids/backend.pid" ]; then
    BACKEND_PID=$(cat "$PROJECT_ROOT/runtime/pids/backend.pid")
    if ps -p $BACKEND_PID > /dev/null 2>&1; then
        kill $BACKEND_PID
        echo -e "${GREEN}✅ 后端服务已停止 (PID: $BACKEND_PID)${NC}"
    else
        echo -e "${YELLOW}⚠️  后端服务进程不存在${NC}"
    fi
    rm -f "$PROJECT_ROOT/runtime/pids/backend.pid"
else
    echo -e "${YELLOW}⚠️  后端PID文件不存在${NC}"
    # 尝试通过端口杀死进程
    BACKEND_PID=$(lsof -ti:8000 2>/dev/null | head -1)
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID
        echo -e "${GREEN}✅ 通过端口8000找到并停止后端服务 (PID: $BACKEND_PID)${NC}"
    fi
fi

# 停止前端服务
echo -e "${YELLOW}🔧 停止前端服务...${NC}"
if [ -f "$PROJECT_ROOT/runtime/pids/frontend.pid" ]; then
    FRONTEND_PID=$(cat "$PROJECT_ROOT/runtime/pids/frontend.pid")
    if ps -p $FRONTEND_PID > /dev/null 2>&1; then
        kill $FRONTEND_PID
        echo -e "${GREEN}✅ 前端服务已停止 (PID: $FRONTEND_PID)${NC}"
    else
        echo -e "${YELLOW}⚠️  前端服务进程不存在${NC}"
    fi
    rm -f "$PROJECT_ROOT/runtime/pids/frontend.pid"
else
    echo -e "${YELLOW}⚠️  前端PID文件不存在${NC}"
    # 尝试通过端口杀死进程
    FRONTEND_PID=$(lsof -ti:5173 2>/dev/null | head -1)
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID
        echo -e "${GREEN}✅ 通过端口5173找到并停止前端服务 (PID: $FRONTEND_PID)${NC}"
    fi
fi

# 等待进程完全停止
sleep 2

echo "================================================"
echo -e "${GREEN}🎉 开发环境已停止！${NC}"
echo ""
echo -e "${BLUE}📝 日志文件位置:${NC}"
echo -e "  📄 后端应用日志: ${YELLOW}$PROJECT_ROOT/logs/backend/app.log${NC}"
echo -e "  📄 后端服务日志: ${YELLOW}$PROJECT_ROOT/logs/backend/uvicorn.log${NC}"
echo -e "  📄 前端日志: ${YELLOW}$PROJECT_ROOT/logs/frontend/dev.log${NC}"
echo ""
echo -e "${BLUE}🚀 重新启动:${NC}"
echo -e "  💡 启动开发环境: ${YELLOW}$PROJECT_ROOT/scripts/start_dev.sh${NC}"