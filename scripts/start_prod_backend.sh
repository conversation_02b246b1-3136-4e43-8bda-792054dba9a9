#!/bin/bash

# ===========================================
# 必然中医 - 医案导出系统 后端生产环境启动脚本
# ===========================================

set -e

echo "🚀 启动必然中医医案导出系统后端 (生产环境)"
echo "================================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BACKEND_DIR="$PROJECT_ROOT/brzy_medical_export"
RUNTIME_DIR="$PROJECT_ROOT/runtime"
LOGS_DIR="$PROJECT_ROOT/logs/backend"

# 创建必要目录
mkdir -p "$RUNTIME_DIR/pids" "$LOGS_DIR"

# 检查命令是否存在
check_command() {
    local cmd=$1
    if [ "$cmd" = "pip" ]; then
        # 特殊处理 pip，检查 pip 或 pip3
        if ! (command -v pip &> /dev/null || command -v pip3 &> /dev/null); then
            echo -e "${RED}❌ pip 未安装，请先安装 pip 或 pip3${NC}"
            exit 1
        fi
    else
        if ! command -v $cmd &> /dev/null; then
            echo -e "${RED}❌ $cmd 未安装，请先安装 $cmd${NC}"
            exit 1
        fi
    fi
}

echo -e "${BLUE}🔍 检查环境依赖...${NC}"
check_command python3
check_command pip

# 检查Python版本
PYTHON_VERSION=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
if ! python3 -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)"; then
    echo -e "${RED}❌ Python版本太低，需要3.8+，当前版本：$PYTHON_VERSION${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 环境依赖检查通过${NC}"

# 进入后端目录
cd "$BACKEND_DIR"

# 检查生产环境配置文件
if [ ! -f ".env.production" ]; then
    echo -e "${RED}❌ 生产环境配置文件不存在: .env.production${NC}"
    echo -e "${YELLOW}📝 请创建 .env.production 文件并配置生产环境参数${NC}"
    echo -e "${BLUE}参考配置（请根据实际环境修改）：${NC}"
    echo "APP_NAME=BRZY Medical Export"
    echo "APP_VERSION=1.0.0"
    echo "DEBUG=false"
    echo "SECRET_KEY=your-production-secret-key"
    echo "ALLOWED_HOSTS=your-domain.com,api.your-domain.com"
    echo "DATABASE_URL=mysql+pymysql://username:password@host:port/database"
    echo "REDIS_URL=redis://host:port/0"
    echo "ORIGINAL_API_URL=https://api.haoniuzhongyi.top:9090"
    echo "JWT_SECRET_KEY=your-production-jwt-secret"
    echo "CORS_ORIGINS=https://your-frontend-domain.com"
    echo "LOG_LEVEL=WARNING"
    exit 1
fi

echo -e "${GREEN}✅ 生产环境配置文件已存在${NC}"

# 检查虚拟环境
if [ ! -d "venv" ]; then
    echo -e "${YELLOW}🔧 创建Python虚拟环境...${NC}"
    python3 -m venv venv
fi

# 激活虚拟环境并检查依赖
echo -e "${YELLOW}📦 激活虚拟环境并检查依赖...${NC}"
source venv/bin/activate

# 安装/更新依赖
if [ ! -f "venv/.dependencies_installed" ]; then
    echo -e "${YELLOW}📦 安装Python依赖...${NC}"
    pip install --upgrade pip
    pip install -r requirements.txt
    
    # 安装生产环境额外依赖
    pip install gunicorn
    
    # 标记依赖已安装
    touch venv/.dependencies_installed
    
    echo -e "${GREEN}✅ 依赖安装完成${NC}"
else
    echo -e "${GREEN}✅ 依赖已安装${NC}"
fi

# 创建必要目录
echo -e "${YELLOW}📁 创建必要目录...${NC}"
mkdir -p logs uploads temp fonts generated_pdfs

# 检查数据库连接
echo -e "${BLUE}🔍 检查数据库连接...${NC}"
if python3 -c "
import os
os.environ['ENV_FILE'] = '.env.production'
from app.database import check_database_connection
check_database_connection()
" 2>/dev/null; then
    echo -e "${GREEN}✅ 数据库连接正常${NC}"
else
    echo -e "${RED}❌ 数据库连接失败，请检查生产环境配置${NC}"
    echo -e "${YELLOW}   1. 检查 .env.production 中的 DATABASE_URL 配置${NC}"
    echo -e "${YELLOW}   2. 确保数据库服务器已启动并可访问${NC}"
    echo -e "${YELLOW}   3. 确保数据库和用户已正确创建${NC}"
    exit 1
fi

# 运行数据库迁移
echo -e "${BLUE}🔄 运行数据库迁移...${NC}"
if command -v alembic &> /dev/null && [ -d "alembic" ]; then
    export ENV_FILE=.env.production
    alembic upgrade head
    echo -e "${GREEN}✅ 数据库迁移完成${NC}"
else
    echo -e "${YELLOW}⚠️ 跳过数据库迁移（alembic未配置）${NC}"
fi

# 停止已存在的后端服务
BACKEND_PID_FILE="$RUNTIME_DIR/pids/backend.pid"
if [ -f "$BACKEND_PID_FILE" ]; then
    OLD_PID=$(cat "$BACKEND_PID_FILE")
    if ps -p "$OLD_PID" > /dev/null 2>&1; then
        echo -e "${YELLOW}🛑 停止已存在的后端服务 (PID: $OLD_PID)...${NC}"
        kill "$OLD_PID"
        sleep 3
        
        # 强制结束
        if ps -p "$OLD_PID" > /dev/null 2>&1; then
            kill -9 "$OLD_PID"
        fi
    fi
    rm -f "$BACKEND_PID_FILE"
fi

# 启动后端服务使用Gunicorn (生产环境推荐)
echo -e "${YELLOW}🔧 启动后端服务 (端口: 8000)...${NC}"
export ENV_FILE=.env.production

# 使用Gunicorn启动服务
nohup gunicorn app.main:app \
    --bind 0.0.0.0:8000 \
    --workers 4 \
    --worker-class uvicorn.workers.UvicornWorker \
    --timeout 120 \
    --keep-alive 5 \
    --max-requests 1000 \
    --max-requests-jitter 50 \
    --log-level warning \
    --access-logfile "$LOGS_DIR/access.log" \
    --error-logfile "$LOGS_DIR/error.log" \
    --daemon &

# 等待进程启动并获取主进程PID
sleep 2

# 获取Gunicorn主进程PID
BACKEND_PID=$(pgrep -f "gunicorn.*app.main:app" | head -1)

if [ -n "$BACKEND_PID" ]; then
    echo "$BACKEND_PID" > "$RUNTIME_DIR/pids/backend.pid"
else
    echo -e "${RED}❌ 无法获取后端服务PID${NC}"
    exit 1
fi
echo "后端服务 PID: $BACKEND_PID"

# 等待服务启动
echo -e "${BLUE}⏳ 等待后端服务启动...${NC}"
sleep 5

# 检查服务是否启动成功
MAX_RETRIES=12
RETRY_COUNT=0

while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
    if curl -s http://localhost:8000/health > /dev/null; then
        echo -e "${GREEN}✅ 后端服务启动成功${NC}"
        break
    else
        RETRY_COUNT=$((RETRY_COUNT + 1))
        echo -e "${YELLOW}⏳ 等待后端服务启动... ($RETRY_COUNT/$MAX_RETRIES)${NC}"
        sleep 5
    fi
done

if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
    echo -e "${RED}❌ 后端服务启动失败，请检查日志${NC}"
    echo -e "${YELLOW}  错误日志: $LOGS_DIR/error.log${NC}"
    echo -e "${YELLOW}  访问日志: $LOGS_DIR/access.log${NC}"
    exit 1
fi

echo "================================================"
echo -e "${GREEN}🎉 后端生产环境启动完成！${NC}"
echo ""
echo -e "${BLUE}📊 服务信息:${NC}"
echo -e "  🔗 后端地址: ${GREEN}http://localhost:8000${NC}"
echo -e "  🔗 API文档:  ${GREEN}http://localhost:8000/api/docs${NC}"
echo -e "  🔗 健康检查: ${GREEN}http://localhost:8000/health${NC}"
echo -e "  🆔 进程PID:  ${YELLOW}$BACKEND_PID${NC}"
echo ""
echo -e "${BLUE}📝 日志文件:${NC}"
echo -e "  📄 应用日志: ${YELLOW}$BACKEND_DIR/logs/app.log${NC}"
echo -e "  📄 访问日志: ${YELLOW}$LOGS_DIR/access.log${NC}"
echo -e "  📄 错误日志: ${YELLOW}$LOGS_DIR/error.log${NC}"
echo ""
echo -e "${BLUE}🛠️ 常用命令:${NC}"
echo -e "  🔍 查看应用日志: ${YELLOW}tail -f $BACKEND_DIR/logs/app.log${NC}"
echo -e "  🔍 查看错误日志: ${YELLOW}tail -f $LOGS_DIR/error.log${NC}"
echo -e "  🔍 查看访问日志: ${YELLOW}tail -f $LOGS_DIR/access.log${NC}"
echo -e "  🛑 停止服务: ${YELLOW}$PROJECT_ROOT/scripts/stop_prod.sh${NC}"
echo -e "  🔄 重启服务: ${YELLOW}$PROJECT_ROOT/scripts/start_prod_backend.sh${NC}"
echo ""
echo -e "${GREEN}🎯 后端生产环境已就绪！${NC}"