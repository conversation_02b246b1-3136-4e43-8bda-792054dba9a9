#!/bin/bash

# ===========================================
# 必然中医 - 医案导出系统 生产环境统一启动脚本
# ===========================================

set -e

echo "🚀 启动必然中医医案导出系统 (生产环境)"
echo "================================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
SCRIPTS_DIR="$PROJECT_ROOT/scripts"

# 检查脚本文件是否存在
check_script() {
    local script_path=$1
    local script_name=$2
    
    if [ ! -f "$script_path" ]; then
        echo -e "${RED}❌ ${script_name}脚本不存在: $script_path${NC}"
        exit 1
    fi
    
    if [ ! -x "$script_path" ]; then
        echo -e "${YELLOW}⚠️ ${script_name}脚本没有执行权限，正在添加...${NC}"
        chmod +x "$script_path"
    fi
}

echo -e "${BLUE}🔍 检查启动脚本...${NC}"
check_script "$SCRIPTS_DIR/start_prod_backend.sh" "后端启动"
check_script "$SCRIPTS_DIR/start_prod_frontend.sh" "前端启动"
check_script "$SCRIPTS_DIR/stop_prod.sh" "停止"
echo -e "${GREEN}✅ 启动脚本检查完成${NC}"

# 检查配置文件
echo -e "${BLUE}🔍 检查生产环境配置文件...${NC}"

BACKEND_CONFIG="$PROJECT_ROOT/brzy_medical_export/.env.production"
FRONTEND_CONFIG="$PROJECT_ROOT/brzy-medical-web/.env.production"

if [ ! -f "$BACKEND_CONFIG" ]; then
    echo -e "${RED}❌ 后端生产环境配置文件不存在${NC}"
    echo -e "${YELLOW}📝 请先创建: $BACKEND_CONFIG${NC}"
    echo -e "${BLUE}可以参考: $PROJECT_ROOT/brzy_medical_export/.env.example${NC}"
    exit 1
fi

if [ ! -f "$FRONTEND_CONFIG" ]; then
    echo -e "${RED}❌ 前端生产环境配置文件不存在${NC}"
    echo -e "${YELLOW}📝 请先创建: $FRONTEND_CONFIG${NC}"
    echo -e "${BLUE}可以参考: $PROJECT_ROOT/brzy-medical-web/.env.example${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 生产环境配置文件检查完成${NC}"

# 停止已存在的服务
echo -e "${BLUE}🛑 停止已存在的服务...${NC}"
"$SCRIPTS_DIR/stop_prod.sh"

if [ $? -ne 0 ]; then
    echo -e "${YELLOW}⚠️ 停止服务时遇到问题，继续启动...${NC}"
fi

echo ""
echo -e "${BLUE}🚀 开始启动生产环境...${NC}"
echo "================================================"

# 启动后端服务
echo -e "${YELLOW}1️⃣ 启动后端服务...${NC}"
"$SCRIPTS_DIR/start_prod_backend.sh"

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 后端服务启动失败${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 后端服务启动成功${NC}"
echo ""

# 等待后端完全启动
echo -e "${BLUE}⏳ 等待后端服务完全启动...${NC}"
sleep 5

# 验证后端服务健康状态
MAX_RETRIES=6
RETRY_COUNT=0

while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
    if curl -s http://localhost:8000/health > /dev/null; then
        echo -e "${GREEN}✅ 后端服务健康检查通过${NC}"
        break
    else
        RETRY_COUNT=$((RETRY_COUNT + 1))
        echo -e "${YELLOW}⏳ 等待后端服务响应... ($RETRY_COUNT/$MAX_RETRIES)${NC}"
        sleep 5
    fi
done

if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
    echo -e "${RED}❌ 后端服务健康检查失败${NC}"
    echo -e "${YELLOW}🛑 停止所有服务...${NC}"
    "$SCRIPTS_DIR/stop_prod.sh"
    exit 1
fi

# 启动前端服务
echo ""
echo -e "${YELLOW}2️⃣ 启动前端服务...${NC}"
"$SCRIPTS_DIR/start_prod_frontend.sh"

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 前端服务启动失败${NC}"
    echo -e "${YELLOW}🛑 停止所有服务...${NC}"
    "$SCRIPTS_DIR/stop_prod.sh"
    exit 1
fi

echo -e "${GREEN}✅ 前端服务启动成功${NC}"

# 等待前端完全启动
echo ""
echo -e "${BLUE}⏳ 等待前端服务完全启动...${NC}"
sleep 5

# 验证前端服务
if curl -s http://localhost:5000 > /dev/null; then
    echo -e "${GREEN}✅ 前端服务健康检查通过${NC}"
else
    echo -e "${YELLOW}⚠️ 前端服务可能未完全启动，请检查日志${NC}"
fi

# 获取PID信息
BACKEND_PID_FILE="$PROJECT_ROOT/runtime/pids/backend.pid"
FRONTEND_PID_FILE="$PROJECT_ROOT/runtime/pids/frontend.pid"

BACKEND_PID=""
FRONTEND_PID=""

if [ -f "$BACKEND_PID_FILE" ]; then
    BACKEND_PID=$(cat "$BACKEND_PID_FILE")
fi

if [ -f "$FRONTEND_PID_FILE" ]; then
    FRONTEND_PID=$(cat "$FRONTEND_PID_FILE")
fi

echo ""
echo "================================================"
echo -e "${GREEN}🎉 生产环境启动完成！${NC}"
echo ""
echo -e "${BLUE}📊 服务状态:${NC}"
echo -e "  🔗 前端地址: ${GREEN}http://localhost:5000${NC}"
echo -e "  🔗 后端地址: ${GREEN}http://localhost:8000${NC}"
echo -e "  🔗 API文档:  ${GREEN}http://localhost:8000/api/docs${NC}"
echo -e "  🔗 健康检查: ${GREEN}http://localhost:8000/health${NC}"

if [ -n "$BACKEND_PID" ]; then
    echo -e "  🆔 后端PID:  ${YELLOW}$BACKEND_PID${NC}"
fi

if [ -n "$FRONTEND_PID" ]; then
    echo -e "  🆔 前端PID:  ${YELLOW}$FRONTEND_PID${NC}"
fi

echo ""
echo -e "${BLUE}📝 重要日志文件:${NC}"
echo -e "  📄 后端应用日志: ${YELLOW}$PROJECT_ROOT/brzy_medical_export/logs/app.log${NC}"
echo -e "  📄 后端错误日志: ${YELLOW}$PROJECT_ROOT/logs/backend/error.log${NC}"
echo -e "  📄 后端访问日志: ${YELLOW}$PROJECT_ROOT/logs/backend/access.log${NC}"
echo -e "  📄 前端服务日志: ${YELLOW}$PROJECT_ROOT/logs/frontend/production.log${NC}"
echo ""
echo -e "${BLUE}🛠️ 常用管理命令:${NC}"
echo -e "  🔍 查看后端日志: ${YELLOW}tail -f $PROJECT_ROOT/logs/backend/error.log${NC}"
echo -e "  🔍 查看前端日志: ${YELLOW}tail -f $PROJECT_ROOT/logs/frontend/production.log${NC}"
echo -e "  🛑 停止所有服务: ${YELLOW}$PROJECT_ROOT/scripts/stop_prod.sh${NC}"
echo -e "  🔄 重启所有服务: ${YELLOW}$PROJECT_ROOT/scripts/start_prod.sh${NC}"
echo -e "  🏥 健康检查: ${YELLOW}curl http://localhost:8000/health${NC}"
echo ""
echo -e "${BLUE}📱 使用说明:${NC}"
echo "  1. 打开浏览器访问 http://localhost:5000"
echo "  2. 在必然中医iOS应用中进入患者聊天界面"
echo "  3. 点击聊天界面右上角的扫码按钮"
echo "  4. 扫描网页上显示的二维码"
echo "  5. 在手机上确认授权"
echo "  6. 网页将自动跳转到医案列表"
echo "  7. 选择要导出的医案，点击导出按钮生成PDF"
echo ""
echo -e "${BLUE}🔒 安全提醒:${NC}"
echo "  • 确保已正确配置防火墙规则"
echo "  • 定期检查和备份数据库"
echo "  • 监控服务器资源使用情况"
echo "  • 定期查看日志文件排查异常"
echo ""
echo -e "${GREEN}🎯 生产环境已就绪，可以提供服务！${NC}"