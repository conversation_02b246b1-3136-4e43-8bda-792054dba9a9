#!/bin/bash

# ===========================================
# 必然中医 - 医案导出系统 开发环境启动脚本
# ===========================================

set -e

echo "🚀 启动必然中医医案导出系统 (开发环境)"
echo "================================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BACKEND_DIR="$PROJECT_ROOT/brzy_medical_export"
FRONTEND_DIR="$PROJECT_ROOT/brzy-medical-web"

# 检查命令是否存在
check_command() {
    local cmd=$1
    if [ "$cmd" = "pip" ]; then
        # 特殊处理 pip，检查 pip 或 pip3
        if ! (command -v pip &> /dev/null || command -v pip3 &> /dev/null); then
            echo -e "${RED}❌ pip 未安装，请先安装 pip 或 pip3${NC}"
            exit 1
        fi
    else
        if ! command -v $cmd &> /dev/null; then
            echo -e "${RED}❌ $cmd 未安装，请先安装 $cmd${NC}"
            exit 1
        fi
    fi
}

# 检查环境依赖
echo -e "${BLUE}🔍 检查环境依赖...${NC}"
check_command python3
# pip 将在虚拟环境中使用，所以这里只检查 python3
check_command node
check_command npm

# 检查Python版本
PYTHON_VERSION=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
if ! python3 -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)"; then
    echo -e "${RED}❌ Python版本太低，需要3.8+，当前版本：$PYTHON_VERSION${NC}"
    exit 1
fi

# 检查Node版本
NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo -e "${RED}❌ Node版本太低，需要16+，当前版本：$(node --version)${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 环境依赖检查通过${NC}"

# 创建开发环境配置文件
create_dev_config() {
    local env_file="$1"
    local env_example="$1.example"
    
    if [ ! -f "$env_file" ]; then
        if [ -f "$env_example" ]; then
            echo -e "${YELLOW}📝 创建配置文件: $env_file${NC}"
            cp "$env_example" "$env_file"
            
            # 生成随机密钥
            SECRET_KEY=$(python3 -c "import secrets; print(secrets.token_urlsafe(32))")
            JWT_SECRET_KEY=$(python3 -c "import secrets; print(secrets.token_urlsafe(32))")
            
            # 替换配置文件中的占位符
            if [[ "$OSTYPE" == "darwin"* ]]; then
                # macOS
                sed -i '' "s/your-secret-key-here-change-this-in-production/$SECRET_KEY/g" "$env_file"
                sed -i '' "s/your-jwt-secret-key-change-this-in-production/$JWT_SECRET_KEY/g" "$env_file"
                sed -i '' "s/DEBUG=false/DEBUG=true/g" "$env_file"
            else
                # Linux
                sed -i "s/your-secret-key-here-change-this-in-production/$SECRET_KEY/g" "$env_file"
                sed -i "s/your-jwt-secret-key-change-this-in-production/$JWT_SECRET_KEY/g" "$env_file"
                sed -i "s/DEBUG=false/DEBUG=true/g" "$env_file"
            fi
            
            echo -e "${GREEN}✅ 配置文件已创建并设置随机密钥${NC}"
        else
            echo -e "${RED}❌ 配置模板文件不存在: $env_example${NC}"
            exit 1
        fi
    else
        echo -e "${GREEN}✅ 配置文件已存在: $env_file${NC}"
    fi
}

# 安装后端依赖
echo -e "${BLUE}📦 安装后端依赖...${NC}"
cd "$BACKEND_DIR"

# 创建虚拟环境
if [ ! -d "venv" ]; then
    echo -e "${YELLOW}🔧 创建Python虚拟环境...${NC}"
    python3 -m venv venv
fi

# 激活虚拟环境并安装依赖
echo -e "${YELLOW}📦 激活虚拟环境并安装依赖...${NC}"
source venv/bin/activate

# 确保使用虚拟环境中的 pip
export PATH="$PWD/venv/bin:$PATH"
./venv/bin/python -m pip install --upgrade pip
./venv/bin/python -m pip install -r requirements.txt

# 创建后端配置文件
create_dev_config ".env"

# 创建必要目录
echo -e "${YELLOW}📁 创建必要目录...${NC}"
mkdir -p logs uploads temp fonts generated_pdfs

# 创建项目级别的目录结构
cd "$PROJECT_ROOT"
mkdir -p logs/backend logs/frontend runtime/pids

echo -e "${GREEN}✅ 后端环境准备完成${NC}"

# 安装前端依赖
echo -e "${BLUE}📦 安装前端依赖...${NC}"
cd "$FRONTEND_DIR"

if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}📦 安装Node.js依赖...${NC}"
    npm install
else
    echo -e "${GREEN}✅ Node.js依赖已安装${NC}"
fi

# 创建前端配置文件
create_dev_config ".env"

echo -e "${GREEN}✅ 前端环境准备完成${NC}"

# 检查数据库连接 (可选)
echo -e "${BLUE}🔍 检查数据库连接...${NC}"
cd "$BACKEND_DIR"
source venv/bin/activate
export PATH="$PWD/venv/bin:$PATH"

if ./venv/bin/python -c "from app.database import check_database_connection; check_database_connection()" 2>/dev/null; then
    echo -e "${GREEN}✅ 数据库连接正常${NC}"
else
    echo -e "${YELLOW}⚠️  数据库连接失败，请检查配置并确保数据库已启动${NC}"
    echo -e "${YELLOW}   1. 检查 MySQL 是否已启动${NC}"
    echo -e "${YELLOW}   2. 检查 .env 文件中的 DATABASE_URL 配置${NC}"
    echo -e "${YELLOW}   3. 运行初始化脚本: mysql -u root -p < init_database.sql${NC}"
fi

# 启动服务
echo -e "${BLUE}🚀 启动开发服务器...${NC}"
echo "================================================"

# 后台启动后端服务
echo -e "${YELLOW}🔧 启动后端服务 (端口: 8000)...${NC}"
cd "$BACKEND_DIR"
source venv/bin/activate
export PATH="$PWD/venv/bin:$PATH"
nohup ./venv/bin/python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload > ../logs/backend/uvicorn.log 2>&1 &
BACKEND_PID=$!
echo "后端服务 PID: $BACKEND_PID"

# 等待后端启动
sleep 3

# 检查后端是否启动成功
if curl -s http://localhost:8000/health > /dev/null; then
    echo -e "${GREEN}✅ 后端服务启动成功${NC}"
else
    echo -e "${RED}❌ 后端服务启动失败，请检查日志: ../backend.log${NC}"
    exit 1
fi

# 启动前端服务
echo -e "${YELLOW}🔧 启动前端服务 (端口: 5173)...${NC}"
cd "$FRONTEND_DIR"
npm run dev > ../logs/frontend/dev.log 2>&1 &
FRONTEND_PID=$!
echo "前端服务 PID: $FRONTEND_PID"

# 保存PID用于停止服务
echo "$BACKEND_PID" > ../runtime/pids/backend.pid
echo "$FRONTEND_PID" > ../runtime/pids/frontend.pid

# 等待前端启动
sleep 5

echo "================================================"
echo -e "${GREEN}🎉 开发环境启动完成！${NC}"
echo ""
echo -e "${BLUE}📊 服务状态:${NC}"
echo -e "  🔗 前端地址: ${GREEN}http://localhost:5173${NC}"
echo -e "  🔗 后端地址: ${GREEN}http://localhost:8000${NC}"
echo -e "  🔗 API文档:  ${GREEN}http://localhost:8000/api/docs${NC}"
echo ""
echo -e "${BLUE}📝 日志文件:${NC}"
echo -e "  📄 后端应用日志: ${YELLOW}$PROJECT_ROOT/logs/backend/app.log${NC}"
echo -e "  📄 后端服务日志: ${YELLOW}$PROJECT_ROOT/logs/backend/uvicorn.log${NC}"
echo -e "  📄 前端日志: ${YELLOW}$PROJECT_ROOT/logs/frontend/dev.log${NC}"
echo ""
echo -e "${BLUE}🛠️  常用命令:${NC}"
echo -e "  🔍 查看后端应用日志: ${YELLOW}tail -f $PROJECT_ROOT/logs/backend/app.log${NC}"
echo -e "  🔍 查看后端服务日志: ${YELLOW}tail -f $PROJECT_ROOT/logs/backend/uvicorn.log${NC}"
echo -e "  🔍 查看前端日志: ${YELLOW}tail -f $PROJECT_ROOT/logs/frontend/dev.log${NC}"
echo -e "  🛑 停止所有服务: ${YELLOW}$PROJECT_ROOT/scripts/stop_dev.sh${NC}"
echo ""
echo -e "${BLUE}📱 使用说明:${NC}"
echo "  1. 打开浏览器访问 http://localhost:5173"
echo "  2. 在必然中医iOS应用中进入患者聊天界面"
echo "  3. 点击聊天界面右上角的扫码按钮"
echo "  4. 扫描网页上显示的二维码"
echo "  5. 在手机上确认授权"
echo "  6. 网页将自动跳转到医案列表"
echo "  7. 选择要导出的医案，点击导出按钮生成PDF"
echo ""
echo -e "${GREEN}🎯 开发环境已就绪，可以开始开发了！${NC}"