#!/bin/bash

# ===========================================
# 必然中医 - 医案导出系统 前端生产环境启动脚本
# ===========================================

set -e

echo "🚀 启动必然中医医案导出系统前端 (生产环境)"
echo "================================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
FRONTEND_DIR="$PROJECT_ROOT/brzy-medical-web"
RUNTIME_DIR="$PROJECT_ROOT/runtime"
LOGS_DIR="$PROJECT_ROOT/logs/frontend"

# 创建必要目录
mkdir -p "$RUNTIME_DIR/pids" "$LOGS_DIR"

# 检查Node.js和npm
check_command() {
    if ! command -v $1 &> /dev/null; then
        echo -e "${RED}❌ $1 未安装，请先安装 $1${NC}"
        exit 1
    fi
}

echo -e "${BLUE}🔍 检查环境依赖...${NC}"
check_command node
check_command npm

# 检查Node版本
NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo -e "${RED}❌ Node版本太低，需要16+，当前版本：$(node --version)${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 环境依赖检查通过${NC}"

# 进入前端目录
cd "$FRONTEND_DIR"

# 检查生产环境配置文件
if [ ! -f ".env.production" ]; then
    echo -e "${RED}❌ 生产环境配置文件不存在: .env.production${NC}"
    echo -e "${YELLOW}📝 请创建 .env.production 文件并配置生产环境参数${NC}"
    echo -e "${BLUE}参考配置：${NC}"
    echo "VITE_API_URL=https://your-api-domain.com"
    echo "VITE_WS_URL=wss://your-api-domain.com"
    echo "NODE_ENV=production"
    echo "VITE_APP_TITLE=必然中医 - 医案导出系统"
    echo "VITE_APP_VERSION=1.0.0"
    exit 1
fi

echo -e "${GREEN}✅ 生产环境配置文件已存在${NC}"

# 检查是否已构建
if [ ! -d "dist" ]; then
    echo -e "${YELLOW}🔧 构建前端生产版本...${NC}"
    npm install
    npm run build
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 前端构建失败${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 前端构建完成${NC}"
else
    echo -e "${GREEN}✅ 前端已构建${NC}"
fi

# 检查是否已安装serve
if ! command -v serve &> /dev/null; then
    echo -e "${YELLOW}📦 安装serve工具...${NC}"
    npm install -g serve
fi

# 停止已存在的前端服务
FRONTEND_PID_FILE="$RUNTIME_DIR/pids/frontend.pid"
if [ -f "$FRONTEND_PID_FILE" ]; then
    OLD_PID=$(cat "$FRONTEND_PID_FILE")
    if ps -p "$OLD_PID" > /dev/null 2>&1; then
        echo -e "${YELLOW}🛑 停止已存在的前端服务 (PID: $OLD_PID)...${NC}"
        kill "$OLD_PID"
        sleep 2
        
        # 强制结束
        if ps -p "$OLD_PID" > /dev/null 2>&1; then
            kill -9 "$OLD_PID"
        fi
    fi
    rm -f "$FRONTEND_PID_FILE"
fi

# 启动前端服务 (默认端口5000)
echo -e "${YELLOW}🔧 启动前端服务 (端口: 5000)...${NC}"
nohup serve -s dist -l 5000 > "$LOGS_DIR/production.log" 2>&1 &
FRONTEND_PID=$!

# 保存PID
echo "$FRONTEND_PID" > "$FRONTEND_PID_FILE"
echo "前端服务 PID: $FRONTEND_PID"

# 等待服务启动
echo -e "${BLUE}⏳ 等待前端服务启动...${NC}"
sleep 5

# 检查服务是否启动成功
if curl -s http://localhost:5000 > /dev/null; then
    echo -e "${GREEN}✅ 前端服务启动成功${NC}"
else
    echo -e "${RED}❌ 前端服务启动失败，请检查日志: $LOGS_DIR/production.log${NC}"
    exit 1
fi

echo "================================================"
echo -e "${GREEN}🎉 前端生产环境启动完成！${NC}"
echo ""
echo -e "${BLUE}📊 服务信息:${NC}"
echo -e "  🔗 前端地址: ${GREEN}http://localhost:5000${NC}"
echo -e "  📄 服务日志: ${YELLOW}$LOGS_DIR/production.log${NC}"
echo -e "  🆔 进程PID:  ${YELLOW}$FRONTEND_PID${NC}"
echo ""
echo -e "${BLUE}🛠️ 常用命令:${NC}"
echo -e "  🔍 查看日志: ${YELLOW}tail -f $LOGS_DIR/production.log${NC}"
echo -e "  🛑 停止服务: ${YELLOW}$PROJECT_ROOT/scripts/stop_prod.sh${NC}"
echo -e "  🔄 重启服务: ${YELLOW}$PROJECT_ROOT/scripts/start_prod_frontend.sh${NC}"
echo ""
echo -e "${GREEN}🎯 前端生产环境已就绪！${NC}"