#!/bin/bash

# CNB Local Build Script for BRZY Medical System
# 使用 Cloud Native Buildpacks 进行本地构建和测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数定义
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查必要依赖..."
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装或未在 PATH 中"
        exit 1
    fi
    
    # 检查 Pack CLI
    if ! command -v pack &> /dev/null; then
        log_warning "Pack CLI 未安装，尝试安装..."
        
        # 根据系统类型安装 Pack CLI
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            if command -v brew &> /dev/null; then
                brew install buildpacks/tap/pack
            else
                log_error "请先安装 Homebrew 或手动安装 Pack CLI"
                exit 1
            fi
        elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
            # Linux
            curl -sSL "https://github.com/buildpacks/pack/releases/download/v0.32.1/pack-v0.32.1-linux.tgz" | sudo tar -C /usr/local/bin/ --no-same-owner -xzv pack
        else
            log_error "不支持的操作系统，请手动安装 Pack CLI"
            exit 1
        fi
    fi
    
    # 检查 Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_warning "Docker Compose 未安装"
        if command -v pip3 &> /dev/null; then
            sudo pip3 install docker-compose
        else
            log_error "请手动安装 Docker Compose"
            exit 1
        fi
    fi
    
    log_success "依赖检查完成"
}

# 构建前端镜像
build_frontend() {
    log_info "构建前端镜像..."
    
    cd brzy-medical-web
    
    # 确保依赖已安装
    if [ ! -d "node_modules" ]; then
        log_info "安装前端依赖..."
        npm install
    fi
    
    # 使用 CNB 构建镜像
    pack build brzy-medical-frontend:local \
        --builder paketobuildpacks/builder-jammy-base \
        --buildpack paketo-buildpacks/nodejs \
        --env NODE_ENV=production \
        --env VITE_APP_ENV=production \
        --env BP_NODE_VERSION="18.*" \
        --env BP_WEB_SERVER=nginx \
        --env BP_WEB_SERVER_ROOT=dist \
        --env BP_WEB_SERVER_ENABLE_PUSH_STATE=true \
        --verbose
    
    cd ..
    
    log_success "前端镜像构建完成"
}

# 构建后端镜像
build_backend() {
    log_info "构建后端镜像..."
    
    cd brzy_medical_export
    
    # 确保 Python 虚拟环境存在
    if [ ! -d "venv" ]; then
        log_info "创建 Python 虚拟环境..."
        python3 -m venv venv
        source venv/bin/activate
        pip install --upgrade pip
        pip install -r requirements.txt
    fi
    
    # 使用 CNB 构建镜像
    pack build brzy-medical-backend:local \
        --builder paketobuildpacks/builder-jammy-base \
        --buildpack paketo-buildpacks/python \
        --env PYTHON_ENV=production \
        --env FASTAPI_ENV=production \
        --env BP_PYTHON_VERSION="3.11.*" \
        --env BP_PIP_VERSION="23.*" \
        --env PYTHONDONTWRITEBYTECODE=1 \
        --env PYTHONUNBUFFERED=1 \
        --verbose
    
    cd ..
    
    log_success "后端镜像构建完成"
}

# 启动本地测试环境
start_local_environment() {
    log_info "启动本地测试环境..."
    
    # 设置环境变量
    export FRONTEND_IMAGE=brzy-medical-frontend:local
    export BACKEND_IMAGE=brzy-medical-backend:local
    export MYSQL_ROOT_PASSWORD=brzy_root_123
    export MYSQL_PASSWORD=brzy_pass_123
    export REDIS_PASSWORD=redis_pass_123
    export JWT_SECRET_KEY=local-development-jwt-key-not-for-production
    export ORIGINAL_API_BASE_URL=https://api.example.com
    
    # 创建必要的目录
    mkdir -p data/{uploads,generated_pdfs,temp,mysql,redis}
    mkdir -p logs/{backend,frontend,nginx}
    
    # 启动服务
    docker-compose -f docker-compose.cnb.yml up -d mysql redis
    
    log_info "等待数据库启动..."
    sleep 30
    
    # 启动应用服务
    docker-compose -f docker-compose.cnb.yml up -d backend frontend
    
    log_info "等待应用启动..."
    sleep 20
    
    # 健康检查
    check_health
}

# 健康检查
check_health() {
    log_info "执行健康检查..."
    
    # 检查后端
    if curl -f http://localhost:8080/health > /dev/null 2>&1; then
        log_success "后端服务健康"
    else
        log_error "后端服务不健康"
        docker logs brzy-backend --tail 20
        return 1
    fi
    
    # 检查前端
    if curl -f http://localhost:5173/ > /dev/null 2>&1; then
        log_success "前端服务健康"
    else
        log_error "前端服务不健康"
        docker logs brzy-frontend --tail 20
        return 1
    fi
    
    log_success "所有服务正常运行"
    log_info "前端地址: http://localhost:5173"
    log_info "后端 API: http://localhost:8080"
    log_info "健康检查: http://localhost:8080/health"
}

# 停止服务
stop_services() {
    log_info "停止所有服务..."
    docker-compose -f docker-compose.cnb.yml down
    log_success "服务已停止"
}

# 清理资源
cleanup() {
    log_info "清理本地镜像和容器..."
    
    # 停止服务
    stop_services
    
    # 删除本地镜像
    docker rmi brzy-medical-frontend:local brzy-medical-backend:local 2>/dev/null || true
    
    # 清理悬挂镜像
    docker image prune -f
    
    log_success "清理完成"
}

# 显示帮助信息
show_help() {
    cat << EOF
CNB Local Build Script for BRZY Medical System

用法: $0 [选项]

选项:
    build       构建前端和后端镜像
    frontend    只构建前端镜像
    backend     只构建后端镜像
    start       启动本地测试环境
    stop        停止所有服务
    restart     重启服务
    health      执行健康检查
    cleanup     清理镜像和容器
    logs        查看服务日志
    help        显示此帮助信息

示例:
    $0 build        # 构建所有镜像
    $0 start        # 启动测试环境
    $0 health       # 检查服务健康状态
    $0 cleanup      # 清理资源

EOF
}

# 查看日志
show_logs() {
    log_info "显示服务日志..."
    docker-compose -f docker-compose.cnb.yml logs -f
}

# 主函数
main() {
    case "${1:-help}" in
        "build")
            check_dependencies
            build_frontend
            build_backend
            ;;
        "frontend")
            check_dependencies
            build_frontend
            ;;
        "backend")
            check_dependencies
            build_backend
            ;;
        "start")
            check_dependencies
            start_local_environment
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            stop_services
            sleep 5
            start_local_environment
            ;;
        "health")
            check_health
            ;;
        "cleanup")
            cleanup
            ;;
        "logs")
            show_logs
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 脚本入口
main "$@"