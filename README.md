# 必然中医 - 医案导出系统

> 为必然中医 iOS 应用提供医案数据导出功能的 Web 端解决方案

## 🌟 项目特性

- 📱 **扫码登录**：通过必然中医 App 扫码安全登录
- 📋 **医案展示**：清晰展示患者医案数据
- 🔍 **智能筛选**：多维度筛选和搜索功能  
- 📄 **PDF 导出**：专业格式的医案 PDF 生成
- 🔐 **安全加密**：token 安全存储和传输
- ⚡ **实时通信**：WebSocket 实时状态更新

## 🏗️ 技术架构

```
📱 iOS 客户端 ──扫码──→ 🌐 Web 前端 ──WebSocket──→ ⚙️ 后端 API ──调用──→ 🔗 原始 API
                      ↓                    ↓
                   Vue.js 应用          FastAPI 服务
                      ↓                    ↓
                  静态文件服务           MySQL 数据库
```

### 前端技术栈
- **Vue 3** + **Vite** - 现代化前端开发
- **Element Plus** - 企业级 UI 组件库
- **Pinia** - 状态管理
- **Axios** - HTTP 客户端
- **QRCode.vue** - 二维码生成

### 后端技术栈
- **FastAPI** - 高性能异步 Web 框架
- **SQLAlchemy** - ORM 数据库操作
- **MySQL** - 主数据库
- **Redis** - 缓存和会话存储
- **WebSocket** - 实时通信
- **ReportLab** - PDF 生成

## 项目结构

```
brzy_output_web/
├── README.md                           # 项目说明文档
├── 
├── docs/                              # 📁 文档目录
│   ├── technical/                     # 技术文档
│   ├── deployment/                    # 部署文档
│   ├── api/                          # API文档
│   └── troubleshooting/              # 故障排除
│
├── logs/                             # 📁 日志目录
│   ├── backend/                      # 后端日志
│   ├── frontend/                     # 前端日志
│   └── scripts/                      # 脚本日志
│
├── runtime/                          # 📁 运行时文件
│   ├── pids/                        # 进程ID文件
│   └── sockets/                     # Unix套接字文件
│
├── scripts/                         # 📁 脚本目录
│   ├── start_dev.sh                 # 开发环境启动脚本
│   └── stop_dev.sh                  # 开发环境停止脚本
│
├── config/                          # 📁 配置模板
│
├── brzy_medical_export/             # 后端项目
│   ├── app/                         # 应用代码
│   ├── uploads/                     # 文件上传目录
│   ├── temp/                        # 临时文件目录
│   ├── fonts/                       # 字体文件目录
│   ├── generated_pdfs/              # PDF输出目录
│   └── requirements.txt             # Python依赖
│
└── brzy-medical-web/               # 前端项目
    ├── src/                         # 源代码
    ├── public/                      # 静态资源
    └── package.json                 # Node.js依赖
```

## 🚀 快速开始

### 环境要求

- **Python 3.8+**
- **Node.js 16+**
- **MySQL 5.7+**
- **Redis 6.0+** (可选)

### 本地开发

#### 1. 克隆项目
```bash
git clone <repository-url>
cd brzy_output_web
```

#### 2. 一键启动开发环境
```bash
# 使用提供的启动脚本（推荐）
./scripts/start_dev.sh

# 脚本会自动：
# - 检查环境依赖
# - 创建 Python 虚拟环境
# - 安装前后端依赖
# - 生成配置文件和密钥
# - 启动前后端服务
```

#### 3. 手动启动（可选）

**启动后端**：
```bash
cd brzy_medical_export
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
cp .env.example .env  # 编辑配置文件
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

**启动前端**：
```bash
cd brzy-medical-web
npm install
cp .env.example .env  # 编辑配置文件
npm run dev
```

#### 4. 访问应用
- 🌐 **前端地址**：http://localhost:5173
- 🔗 **后端地址**：http://localhost:8000
- 📚 **API 文档**：http://localhost:8000/api/docs

### 停止服务
```bash
# 使用停止脚本
./scripts/stop_dev.sh
```

## 📱 使用说明

### 扫码登录流程

1. **打开 Web 端**：访问 http://localhost:5173
2. **打开必然中医 App**：进入患者聊天界面
3. **扫码登录**：点击聊天界面右上角扫码按钮，扫描网页二维码
4. **确认授权**：在手机上确认授权
5. **自动跳转**：网页自动跳转到医案列表

### 医案导出

1. **选择患者**：在医案列表中选择目标患者
2. **筛选医案**：使用时间范围、关键词等筛选条件
3. **预览医案**：点击医案查看详细内容
4. **导出 PDF**：点击导出按钮生成专业格式 PDF

## 🔧 配置说明

### 后端配置 (.env)

```env
# 应用配置
SECRET_KEY=your-secret-key
JWT_SECRET_KEY=your-jwt-secret-key
DEBUG=true

# 数据库
DATABASE_URL=mysql+pymysql://user:password@host:port/database

# Redis (可选)
REDIS_URL=redis://host:port/db

# 原始 API
ORIGINAL_API_URL=https://api.haoniuzhongyi.top:9090/easydoctorv2-ws/apiController

# CORS
CORS_ORIGINS=["http://localhost:5173"]
```

### 前端配置 (.env)

```env
# API 地址
VITE_API_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000

# 应用信息
VITE_APP_TITLE=必然中医 - 医案导出系统
```

## 🎯 部署方案

### 方案 1：CNB 云原生构建 + 1Panel 部署（推荐）

使用 CNB (Cloud Native Build) 进行云原生构建，支持配置外置化：

**🔄 构建流程**：
- **触发方式**：推送代码到 `release` 分支
- **构建平台**：CNB 云原生构建
- **制品库**：docker.cnb.cool
- **前端制品**：`docker.cnb.cool/{namespace}/brzy-medical-frontend:{version}`
- **后端制品**：`docker.cnb.cool/{namespace}/brzy-medical-backend:{version}`

**🏗️ 配置外置化**：
- 构建时不包含敏感配置（`.env*` 文件）
- 运行时通过环境变量动态注入配置
- 同一制品支持多环境部署

**🚀 1Panel 部署**：
```bash
# 1. 在 1Panel 中创建应用
# 2. 配置拉取 CNB 制品库镜像
# 3. 设置运行时环境变量
# 4. 一键部署启动
```

**优势**：
- ☁️ 云原生标准构建
- 🎨 1Panel 可视化管理
- 🔒 配置安全外置
- 📦 前后端独立部署
- 🚀 快速环境复制

### 方案 2：传统 Nginx + Systemd

适合对部署有完全控制需求的场景：

```bash
# 详见：docs/deployment/DEPLOYMENT_GUIDE.md
```

### 方案 3：Docker 容器化

适合微服务架构和容器化环境：

```bash
# 使用 Docker Compose
docker-compose up -d
```

## 📁 项目结构

```
brzy_output_web/
├── brzy-medical-web/          # Vue 前端应用
│   ├── src/
│   │   ├── api/              # API 接口
│   │   ├── components/       # Vue 组件
│   │   ├── views/           # 页面视图
│   │   ├── stores/          # Pinia 状态管理
│   │   └── utils/           # 工具函数
│   ├── public/              # 静态资源
│   └── dist/               # 构建输出
├── brzy_medical_export/       # FastAPI 后端应用
│   ├── app/
│   │   ├── api/            # API 路由
│   │   ├── models/         # 数据模型
│   │   ├── schemas/        # Pydantic 模式
│   │   ├── services/       # 业务逻辑
│   │   └── utils/          # 工具函数
│   ├── fonts/              # PDF 字体文件
│   ├── generated_pdfs/     # 生成的 PDF 文件
│   └── logs/              # 日志文件
├── scripts/                   # 部署和管理脚本
├── docs/                     # 项目文档
└── .github/workflows/        # GitHub Actions 配置
```

## 📊 监控和日志

### 日志文件位置
```bash
# 后端日志
tail -f logs/backend/app.log
tail -f logs/backend/uvicorn.log

# 前端日志  
tail -f logs/frontend/dev.log

# 系统服务日志
sudo journalctl -u brzy-medical-backend -f
```

### 健康检查
```bash
# 后端健康检查
curl http://localhost:8000/health

# 完整系统检查
curl https://your-domain.com/api/health
```

## 🛡️ 安全特性

- 🔐 **JWT Token 认证**：安全的用户身份验证
- 🛡️ **CORS 保护**：跨域请求安全控制
- 🔒 **数据加密传输**：HTTPS/WSS 加密通信
- 📱 **设备绑定**：扫码登录设备标识验证
- 🗝️ **密钥轮换**：支持定期密钥更新

## 🚨 故障排除

### 常见问题

**1. 扫码登录失败**
```bash
# 检查 WebSocket 连接
curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" http://localhost:8000/ws/test

# 检查 CORS 配置
grep CORS_ORIGINS brzy_medical_export/.env
```

**2. PDF 生成失败**
```bash
# 检查字体文件
ls -la brzy_medical_export/fonts/

# 检查权限
chmod 755 brzy_medical_export/generated_pdfs/
```

**3. 数据库连接失败**
```bash
# 测试数据库连接
cd brzy_medical_export
python -c "from app.database import check_database_connection; print(check_database_connection())"
```

### 获取帮助

- 📚 [完整部署指南](docs/deployment/DEPLOYMENT_GUIDE.md)
- 🎯 [1Panel 部署指南](docs/deployment/1PANEL_DEPLOYMENT_GUIDE.md)
- 🔧 [故障排除指南](docs/troubleshooting/)
- 📖 [API 文档](docs/api/)

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 📞 技术支持

- 📧 **邮箱**：<EMAIL>
- 📱 **QQ 群**：123456789
- 🌐 **官网**：https://your-domain.com

---

<div align="center">

**⭐ 如果这个项目对您有帮助，请给我们一个 Star ⭐**

Made with ❤️ by BRZY Medical Team

</div>