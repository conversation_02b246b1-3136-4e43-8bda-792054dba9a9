# 日志文件
*.log
logs/
backend.log
frontend.log
brzy_medical_export/logs/

# 进程ID文件
*.pid
runtime/pids/
backend.pid
frontend.pid

# 系统文件
.DS_Store
Thumbs.db

# Python 缓存文件
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.py[cod]
*$py.class

# 虚拟环境
env/
venv/
.env
.venv/
ENV/
env.bak/
venv.bak/

# 配置文件（包含敏感信息）
.env.local
.env.*.local
*.env.local

# 依赖文件
node_modules/
package-lock.json.bak

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 构建文件
dist/
build/
*.egg-info/

# 生成的PDF文件
generated_pdfs/
uploads/
temp/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 临时文件
*.tmp
*.temp
nohup.out

# 测试覆盖率
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# 分析文件
log-analysis/
.aider*

# 运行时文件
*.lock
*.pid
*.sock
