#!/bin/bash

# ===========================================
# 必然中医 - 医案导出系统 1Panel部署脚本
# ===========================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
DEPLOY_PATH="/opt/brzy_medical_system"
BACKUP_DIR="/opt/backups/brzy_medical"
SERVICE_NAME="brzy-medical-backend"

echo -e "${BLUE}🚀 必然中医医案导出系统 - 1Panel生产环境部署${NC}"
echo "================================================"

# 检查是否为root用户或有sudo权限
check_permissions() {
    if [[ $EUID -eq 0 ]]; then
        SUDO=""
    elif sudo -n true 2>/dev/null; then
        SUDO="sudo"
    else
        echo -e "${RED}❌ 需要root权限或sudo权限才能执行此脚本${NC}"
        exit 1
    fi
}

# 创建备份
create_backup() {
    echo -e "${YELLOW}📦 创建系统备份...${NC}"
    
    $SUDO mkdir -p "$BACKUP_DIR"
    
    if [ -d "$DEPLOY_PATH" ]; then
        BACKUP_NAME="brzy_medical_backup_$(date +%Y%m%d_%H%M%S)"
        echo -e "备份到: $BACKUP_DIR/$BACKUP_NAME"
        $SUDO cp -r "$DEPLOY_PATH" "$BACKUP_DIR/$BACKUP_NAME"
        echo -e "${GREEN}✅ 备份完成${NC}"
    else
        echo -e "${YELLOW}⚠️  首次部署，跳过备份${NC}"
    fi
}

# 准备部署目录
prepare_deploy_directory() {
    echo -e "${YELLOW}📁 准备部署目录...${NC}"
    
    # 创建主目录
    $SUDO mkdir -p "$DEPLOY_PATH"
    $SUDO chown $USER:$USER "$DEPLOY_PATH"
    
    # 创建子目录
    mkdir -p "$DEPLOY_PATH"/{logs/{backend,frontend},runtime/pids,generated_pdfs,uploads,temp}
    
    echo -e "${GREEN}✅ 目录准备完成${NC}"
}

# 部署应用代码
deploy_application() {
    echo -e "${YELLOW}📥 部署应用代码...${NC}"
    
    cd "$DEPLOY_PATH"
    
    # 克隆或更新代码
    if [ -d ".git" ]; then
        echo "更新现有代码库..."
        git fetch origin main
        git reset --hard origin/main
    else
        echo "克隆代码库..."
        # 需要替换为实际的仓库地址
        read -p "请输入Git仓库地址: " REPO_URL
        git clone "$REPO_URL" .
    fi
    
    echo -e "${GREEN}✅ 代码部署完成${NC}"
}

# 配置生产环境
configure_production_env() {
    echo -e "${YELLOW}⚙️ 配置生产环境...${NC}"
    
    # 后端环境配置
    if [ -f "brzy_medical_export/.env.production" ]; then
        cp brzy_medical_export/.env.production brzy_medical_export/.env
        echo "✅ 后端生产配置已应用"
        
        # 提示用户修改关键配置
        echo -e "${YELLOW}⚠️  请编辑后端配置文件: $DEPLOY_PATH/brzy_medical_export/.env${NC}"
        echo -e "${YELLOW}   必须修改以下配置项:${NC}"
        echo -e "   - SECRET_KEY"
        echo -e "   - JWT_SECRET_KEY" 
        echo -e "   - DATABASE_URL"
        echo -e "   - REDIS_PASSWORD"
        echo -e "   - ALLOWED_HOSTS"
        echo -e "   - CORS_ORIGINS"
        
        read -p "是否现在编辑配置文件? (y/N): " edit_config
        if [[ $edit_config =~ ^[Yy]$ ]]; then
            ${EDITOR:-nano} brzy_medical_export/.env
        fi
    else
        echo -e "${RED}❌ 生产环境配置文件不存在${NC}"
        exit 1
    fi
    
    # 前端环境配置
    if [ -f "brzy-medical-web/.env.production" ]; then
        cp brzy-medical-web/.env.production brzy-medical-web/.env
        echo "✅ 前端生产配置已应用"
    fi
    
    echo -e "${GREEN}✅ 环境配置完成${NC}"
}

# 安装依赖
install_dependencies() {
    echo -e "${YELLOW}📦 安装应用依赖...${NC}"
    
    # 后端依赖
    echo "安装后端依赖..."
    cd "$DEPLOY_PATH/brzy_medical_export"
    
    if [ ! -d "venv" ]; then
        python3 -m venv venv
    fi
    
    source venv/bin/activate
    pip install --upgrade pip
    pip install -r requirements.txt
    deactivate
    
    # 前端依赖 (如果需要重新构建)
    echo "检查前端构建..."
    cd "$DEPLOY_PATH/brzy-medical-web"
    
    if [ ! -d "dist" ] || [ ! -f "dist/index.html" ]; then
        echo "构建前端应用..."
        npm ci
        npm run build
    else
        echo "前端已构建，跳过构建步骤"
    fi
    
    echo -e "${GREEN}✅ 依赖安装完成${NC}"
}

# 初始化数据库
init_database() {
    echo -e "${YELLOW}🗄️ 检查数据库...${NC}"
    
    cd "$DEPLOY_PATH/brzy_medical_export"
    source venv/bin/activate
    
    # 测试数据库连接
    if python -c "from app.database import check_database_connection; check_database_connection()" 2>/dev/null; then
        echo -e "${GREEN}✅ 数据库连接正常${NC}"
    else
        echo -e "${RED}❌ 数据库连接失败${NC}"
        echo -e "${YELLOW}请确保:${NC}"
        echo "1. 1Panel MySQL服务已启动"
        echo "2. 数据库和用户已创建"
        echo "3. .env文件中的DATABASE_URL配置正确"
        echo "4. 已运行初始化脚本: mysql -u root -p < init_database.sql"
        
        read -p "是否继续部署? (y/N): " continue_deploy
        if [[ ! $continue_deploy =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    deactivate
}

# 配置系统服务
configure_systemd_service() {
    echo -e "${YELLOW}🔧 配置系统服务...${NC}"
    
    # 创建systemd服务文件
    $SUDO tee /etc/systemd/system/${SERVICE_NAME}.service > /dev/null <<EOF
[Unit]
Description=BRZY Medical Export Backend
After=network.target mysql.service redis.service

[Service]
Type=exec
User=$USER
Group=$USER
WorkingDirectory=$DEPLOY_PATH/brzy_medical_export
Environment=PATH=$DEPLOY_PATH/brzy_medical_export/venv/bin
ExecStart=$DEPLOY_PATH/brzy_medical_export/venv/bin/uvicorn app.main:app --host 0.0.0.0 --port 8080 --workers 2
Restart=always
RestartSec=3
StandardOutput=append:$DEPLOY_PATH/logs/backend/uvicorn.log
StandardError=append:$DEPLOY_PATH/logs/backend/uvicorn.log

[Install]
WantedBy=multi-user.target
EOF

    # 重新加载systemd配置
    $SUDO systemctl daemon-reload
    $SUDO systemctl enable ${SERVICE_NAME}
    
    echo -e "${GREEN}✅ 系统服务配置完成${NC}"
}

# 启动服务
start_services() {
    echo -e "${YELLOW}🚀 启动服务...${NC}"
    
    # 停止旧服务
    if systemctl is-active --quiet ${SERVICE_NAME}; then
        echo "停止旧服务..."
        $SUDO systemctl stop ${SERVICE_NAME}
    fi
    
    # 启动新服务
    echo "启动服务..."
    $SUDO systemctl start ${SERVICE_NAME}
    
    # 检查服务状态
    sleep 3
    if systemctl is-active --quiet ${SERVICE_NAME}; then
        echo -e "${GREEN}✅ 后端服务启动成功${NC}"
        
        # 测试健康检查
        if curl -s http://localhost:8080/health > /dev/null; then
            echo -e "${GREEN}✅ 健康检查通过${NC}"
        else
            echo -e "${YELLOW}⚠️  健康检查失败，但服务已启动${NC}"
        fi
    else
        echo -e "${RED}❌ 服务启动失败${NC}"
        echo "查看服务日志:"
        $SUDO systemctl status ${SERVICE_NAME}
        exit 1
    fi
}

# 显示部署信息
show_deployment_info() {
    echo "================================================"
    echo -e "${GREEN}🎉 部署完成！${NC}"
    echo ""
    echo -e "${BLUE}📊 系统信息:${NC}"
    echo -e "  📁 部署路径: ${GREEN}$DEPLOY_PATH${NC}"
    echo -e "  🔗 后端端口: ${GREEN}8080${NC}"
    echo -e "  🌐 健康检查: ${GREEN}http://localhost:8080/health${NC}"
    echo ""
    echo -e "${BLUE}📝 管理命令:${NC}"
    echo -e "  🔄 重启服务: ${YELLOW}sudo systemctl restart ${SERVICE_NAME}${NC}"
    echo -e "  📊 服务状态: ${YELLOW}sudo systemctl status ${SERVICE_NAME}${NC}"
    echo -e "  📄 查看日志: ${YELLOW}sudo journalctl -u ${SERVICE_NAME} -f${NC}"
    echo -e "  📄 应用日志: ${YELLOW}tail -f $DEPLOY_PATH/logs/backend/app.log${NC}"
    echo ""
    echo -e "${BLUE}🔧 下一步配置:${NC}"
    echo "1. 在1Panel中配置Nginx反向代理"
    echo "2. 配置SSL证书"
    echo "3. 配置域名解析"
    echo "4. 测试扫码登录功能"
    echo ""
    echo -e "${YELLOW}⚠️  重要提醒:${NC}"
    echo "- 定期备份数据库和生成的PDF文件"
    echo "- 监控服务器资源使用情况"
    echo "- 定期更新系统和依赖包"
    echo "================================================"
}

# 主函数
main() {
    echo "开始部署流程..."
    
    check_permissions
    create_backup
    prepare_deploy_directory
    deploy_application
    configure_production_env
    install_dependencies
    init_database
    configure_systemd_service
    start_services
    show_deployment_info
    
    echo -e "${GREEN}✅ 部署流程全部完成！${NC}"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi