# =====================================================
# CNB 云原生构建配置 - 必然中医医案导出系统
# =====================================================
# 
# 符合 CNB 规范的流水线配置
# 当推送到 main 分支时自动触发构建
# 生成前后端两个独立制品并推送到 CNB 制品库
#

# =====================================================
# 全局变量定义（使用 YAML 锚点）
# =====================================================
.global_env: &global_env
  PROJECT_NAME: "brzy-medical"
  BUILD_VERSION: "${CNB_BRANCH}-${CNB_COMMIT_SHA:0:8}"
  REGISTRY: "docker.cnb.cool"
  NAMESPACE: "jtaoDev"

.common_cache: &common_cache
  key: "${CNB_COMMIT_SHA:0:8}"

# =====================================================
# Main 分支构建配置
# =====================================================
main:
  push:
    # 主构建流水线
    - name: "必然中医医案导出系统构建"
      env: !reference [.global_env]
      stages:
        
        # ===================================================
        # 构建元数据生成阶段
        # ===================================================
        - name: "生成构建元数据"
          jobs:
            - name: generate-metadata
              image: alpine:latest
              script: |
                echo "🏗️ 生成构建元数据..."
                echo "构建版本: ${BUILD_VERSION}"
                echo "构建时间: $(date -u +'%Y-%m-%dT%H:%M:%SZ')"
                echo "Git提交: ${CNB_COMMIT_SHA}"
                echo "分支: ${CNB_BRANCH}"
                echo "项目: ${PROJECT_NAME}"
                echo "制品库: ${REGISTRY}/${NAMESPACE}"

        # ===================================================
        # 前端制品构建阶段
        # ===================================================
        - name: "构建前端制品"
          jobs:
            - name: build-frontend
              image: node:18-alpine
              env:
                NODE_ENV: "production"
                VITE_BUILD_SOURCEMAP: "false"
                VITE_BUILD_MINIFY: "true"
                VITE_DISABLE_ENV_INJECTION: "true"
              cache:
                paths:
                  - brzy-medical-web/node_modules/
                  - brzy-medical-web/dist/
                key: "frontend-${CNB_COMMIT_SHA:0:8}"
              script: |
                echo "🔧 准备前端构建环境..."
                node --version
                npm --version
                
                echo "📂 切换到前端项目目录..."
                cd brzy-medical-web
                pwd
                ls -la
                
                echo "📦 安装前端依赖（包含开发依赖）..."
                npm install --include=dev --silent
                
                echo "🏗️ 构建前端应用..."
                npm run build
                ls -la dist/
                
                echo "⚙️ 准备 Nginx 配置..."
                cat > nginx.conf << 'EOF'
                user nginx;
                worker_processes auto;
                
                events {
                    worker_connections 1024;
                }
                
                http {
                    include /etc/nginx/mime.types;
                    default_type application/octet-stream;
                    
                    # Gzip 压缩
                    gzip on;
                    gzip_types text/plain text/css application/json application/javascript text/xml application/xml;
                    
                    # 安全头
                    add_header X-Frame-Options DENY;
                    add_header X-Content-Type-Options nosniff;
                    add_header X-XSS-Protection "1; mode=block";
                    
                    server {
                        listen 80;
                        server_name _;
                        root /usr/share/nginx/html;
                        index index.html;
                        
                        # SPA 路由支持
                        location / {
                            try_files $uri $uri/ /index.html;
                        }
                        
                        # API 代理（可选）
                        location /api/ {
                            proxy_pass http://backend:8080/api/;
                            proxy_set_header Host $host;
                            proxy_set_header X-Real-IP $remote_addr;
                            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                        }
                        
                        # WebSocket 代理（可选）
                        location /ws/ {
                            proxy_pass http://backend:8080/ws/;
                            proxy_http_version 1.1;
                            proxy_set_header Upgrade $http_upgrade;
                            proxy_set_header Connection "upgrade";
                        }
                        
                        # 健康检查
                        location /health {
                            access_log off;
                            return 200 "healthy\n";
                            add_header Content-Type text/plain;
                        }
                        
                        # 静态资源缓存
                        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
                            expires 1y;
                            add_header Cache-Control "public, immutable";
                        }
                    }
                }
                EOF
                
                echo "🐳 准备 Dockerfile..."
                cat > Dockerfile << 'EOF'
                FROM nginx:alpine
                
                COPY dist/ /usr/share/nginx/html/
                COPY nginx.conf /etc/nginx/nginx.conf
                
                RUN mkdir -p /usr/share/nginx/html/config
                RUN chown -R nginx:nginx /usr/share/nginx/html
                
                HEALTHCHECK --interval=30s --timeout=5s --start-period=10s --retries=3 \
                  CMD wget --no-verbose --tries=1 --spider http://localhost/health || exit 1
                
                EXPOSE 80
                CMD ["nginx", "-g", "daemon off;"]
                EOF
                
                echo "✅ 前端构建准备完成"

            - name: docker-build-frontend
              image: plugins/docker
              settings:
                registry: "docker.cnb.cool"
                repo: "jtaoDev/brzy-medical-frontend"
                username: cnb
                password: "${CNB_TOKEN}"
                tags:
                  - "${BUILD_VERSION}"
                  - "latest"
                context: "brzy-medical-web"
                dockerfile: "Dockerfile"
                auto_tag: true
                auto_tag_suffix: latest
              script: |
                echo "🐳 构建前端 Docker 镜像..."
                echo "📦 镜像: docker.cnb.cool/jtaoDev/brzy-medical-frontend:${BUILD_VERSION}"

        # ===================================================
        # 后端制品构建阶段
        # ===================================================
        - name: "构建后端制品"
          jobs:
            - name: build-backend
              image: python:3.11-slim
              env:
                PYTHON_ENV: "production"
                PYTHONDONTWRITEBYTECODE: "1"
                PYTHONUNBUFFERED: "1"
                PYTHONOPTIMIZE: "2"
                EXCLUDE_ENV_FILES: ".env*,config/*.local.*,secrets/*"
              cache:
                paths:
                  - "/root/.cache/pip"
                  - "brzy_medical_export/venv/"
                key: "backend-${CNB_COMMIT_SHA:0:8}"
              script: |
                echo "🔧 准备后端构建环境..."
                python --version
                pip --version
                
                echo "📂 切换到后端项目目录..."
                cd brzy_medical_export
                pwd
                ls -la
                
                echo "📦 安装系统依赖..."
                apt-get update && apt-get install -y \
                  libmariadb-dev \
                  libffi-dev \
                  libjpeg-dev \
                  libpng-dev \
                  fonts-liberation \
                  fontconfig \
                  curl \
                  gettext-base \
                  && rm -rf /var/lib/apt/lists/*
                
                echo "📦 安装 Python 依赖..."
                pip install --upgrade pip
                pip install --no-cache-dir -r requirements.txt
                
                echo "⚙️ 准备应用文件..."
                # 创建必要目录
                mkdir -p logs uploads temp fonts generated_pdfs config scripts
                
                # 创建启动脚本
                cat > scripts/start-backend.sh << 'EOF'
                #!/bin/bash
                
                # 配置验证和运行时启动脚本
                set -e
                
                echo "🚀 启动必然中医后端服务..."
                
                # 验证必需的环境变量
                required_vars=(
                    "DATABASE_URL"
                    "JWT_SECRET_KEY"
                    "ORIGINAL_API_BASE_URL"
                )
                
                for var in "${required_vars[@]}"; do
                    if [ -z "${!var}" ]; then
                        echo "❌ 错误: 必需环境变量 $var 未设置"
                        exit 1
                    fi
                done
                
                echo "✅ 环境变量验证通过"
                
                # 设置工作目录
                cd /app
                
                # 启动应用
                exec uvicorn app.main:app \
                    --host 0.0.0.0 \
                    --port 8080 \
                    --workers ${BACKEND_WORKERS:-4} \
                    --access-log \
                    --log-level info
                EOF
                
                chmod +x scripts/start-backend.sh
                
                echo "🐳 准备 Dockerfile..."
                cat > Dockerfile << 'EOF'
                FROM python:3.11-slim
                
                WORKDIR /app
                
                RUN apt-get update && apt-get install -y \
                    libmariadb-dev \
                    libffi-dev \
                    libjpeg-dev \
                    libpng-dev \
                    fonts-liberation \
                    fontconfig \
                    curl \
                    gettext-base \
                    && rm -rf /var/lib/apt/lists/*
                
                COPY requirements.txt .
                
                RUN pip install --upgrade pip && \
                    pip install --no-cache-dir -r requirements.txt
                
                COPY app/ ./app/
                COPY fonts/ ./fonts/
                COPY scripts/ ./scripts/
                
                RUN mkdir -p logs uploads temp generated_pdfs config
                RUN chmod +x scripts/start-backend.sh
                
                RUN useradd --create-home --shell /bin/bash app && \
                    chown -R app:app /app
                
                USER app
                
                HEALTHCHECK --interval=30s --timeout=5s --start-period=30s --retries=3 \
                  CMD curl -f http://localhost:8080/health || exit 1
                
                EXPOSE 8080
                CMD ["./scripts/start-backend.sh"]
                EOF
                
                echo "✅ 后端构建准备完成"

            - name: docker-build-backend
              image: plugins/docker
              settings:
                registry: "docker.cnb.cool"
                repo: "jtaoDev/brzy-medical-backend"
                username: cnb
                password: "${CNB_TOKEN}"
                tags:
                  - "${BUILD_VERSION}"
                  - "latest"
                context: "brzy_medical_export"
                dockerfile: "Dockerfile"
                auto_tag: true
                auto_tag_suffix: latest
              script: |
                echo "🐳 构建后端 Docker 镜像..."
                echo "📦 镜像: docker.cnb.cool/jtaoDev/brzy-medical-backend:${BUILD_VERSION}"

        # ===================================================
        # 构建后处理阶段
        # ===================================================
        - name: "生成部署信息"
          jobs:
            - name: create-deployment-info
              image: alpine:latest
              script: |
                echo "📋 生成部署信息..."
                
                cat > deployment-info.json << EOF
                {
                  "project": "必然中医医案导出系统",
                  "version": "${BUILD_VERSION}",
                  "build_date": "$(date -u +'%Y-%m-%dT%H:%M:%SZ')",
                  "git_sha": "${CNB_COMMIT_SHA}",
                  "branch": "${CNB_BRANCH}",
                  "images": {
                    "frontend": {
                      "name": "${NAMESPACE}/brzy-medical-frontend",
                      "tag": "${BUILD_VERSION}",
                      "registry": "${REGISTRY}",
                      "full_name": "${REGISTRY}/${NAMESPACE}/brzy-medical-frontend:${BUILD_VERSION}"
                    },
                    "backend": {
                      "name": "${NAMESPACE}/brzy-medical-backend", 
                      "tag": "${BUILD_VERSION}",
                      "registry": "${REGISTRY}",
                      "full_name": "${REGISTRY}/${NAMESPACE}/brzy-medical-backend:${BUILD_VERSION}"
                    }
                  },
                  "deployment": {
                    "docker_compose_example": "详见项目 deployment/ 目录",
                    "1panel_compatible": true,
                    "config_externalized": true
                  }
                }
                EOF
                
                echo "📋 部署信息已生成:"
                cat deployment-info.json

            - name: build-summary
              image: alpine:latest
              script: |
                echo ""
                echo "🎉 CNB 云原生构建完成！"
                echo "=================================="
                echo "📦 制品信息:"
                echo "  前端: ${REGISTRY}/${NAMESPACE}/brzy-medical-frontend:${BUILD_VERSION}"
                echo "  后端: ${REGISTRY}/${NAMESPACE}/brzy-medical-backend:${BUILD_VERSION}"
                echo ""
                echo "🚀 部署说明:"
                echo "  1. 制品已推送到 CNB 制品库"
                echo "  2. 配置已外置化，支持运行时注入"
                echo "  3. 可在 1Panel 中直接拉取使用"
                echo ""
                echo "💡 使用示例:"
                echo "  docker pull ${REGISTRY}/${NAMESPACE}/brzy-medical-frontend:${BUILD_VERSION}"
                echo "  docker pull ${REGISTRY}/${NAMESPACE}/brzy-medical-backend:${BUILD_VERSION}"
                echo ""
                echo "✅ 构建成功通知已发送"

# =====================================================
# 其他分支配置（可选）
# =====================================================
# 如果需要在其他分支也进行构建，可以在这里添加
# 例如：
# develop:
#   push:
#     - name: "开发分支构建"
#       stages:
#         - name: "测试构建"
#           jobs:
#             - name: test-build
#               image: alpine:latest
#               script: echo "开发分支测试构建"