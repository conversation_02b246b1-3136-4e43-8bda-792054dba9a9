# Docker Compose file for CNB-built images
version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: brzy-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-brzy_root_123}
      MYSQL_DATABASE: brzy_medical
      MYSQL_USER: brzy_user
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-brzy_pass_123}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./brzy_medical_export/init_database.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "3306:3306"
    networks:
      - brzy-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p$$MYSQL_ROOT_PASSWORD"]
      timeout: 20s
      retries: 10

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: brzy-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_pass_123}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - brzy-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend Service (CNB-built)
  backend:
    # This will be replaced with actual CNB-built image
    image: ${BACKEND_IMAGE:-brzy-medical-backend:latest}
    container_name: brzy-backend
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      # Database configuration
      DATABASE_URL: mysql+pymysql://brzy_user:${MYSQL_PASSWORD:-brzy_pass_123}@mysql:3306/brzy_medical
      
      # Redis configuration
      REDIS_URL: redis://:${REDIS_PASSWORD:-redis_pass_123}@redis:6379/0
      
      # Application configuration
      JWT_SECRET_KEY: ${JWT_SECRET_KEY:-your-super-secret-jwt-key-change-in-production}
      ORIGINAL_API_BASE_URL: ${ORIGINAL_API_BASE_URL:-https://api.example.com}
      
      # Environment settings
      PYTHON_ENV: production
      FASTAPI_ENV: production
      PYTHONDONTWRITEBYTECODE: 1
      PYTHONUNBUFFERED: 1
      
      # Logging
      LOG_LEVEL: INFO
      LOG_FILE: /workspace/logs/app.log
    volumes:
      - ./data/uploads:/workspace/uploads
      - ./data/generated_pdfs:/workspace/generated_pdfs
      - ./data/temp:/workspace/temp
      - ./logs/backend:/workspace/logs
    ports:
      - "8080:8080"
    networks:
      - brzy-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend Service (CNB-built)
  frontend:
    # This will be replaced with actual CNB-built image
    image: ${FRONTEND_IMAGE:-brzy-medical-frontend:latest}
    container_name: brzy-frontend
    restart: unless-stopped
    depends_on:
      backend:
        condition: service_healthy
    environment:
      NODE_ENV: production
      VITE_API_BASE_URL: http://localhost:8080/api
      VITE_WS_URL: ws://localhost:8080/ws
    volumes:
      - ./logs/frontend:/var/log/nginx
    ports:
      - "5173:5173"
    networks:
      - brzy-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5173/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Nginx Reverse Proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: brzy-nginx
    restart: unless-stopped
    depends_on:
      - frontend
      - backend
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./logs/nginx:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    networks:
      - brzy-network
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring (Prometheus - optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: brzy-prometheus
    restart: unless-stopped
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - brzy-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local

networks:
  brzy-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16