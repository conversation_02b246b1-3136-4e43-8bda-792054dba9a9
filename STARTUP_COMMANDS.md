# 必然中医医案导出系统 - 启动命令总结

## 🚀 快速启动命令

### 后端服务启动

#### 开发环境
```bash
cd backend
export ENVIRONMENT=development
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

#### 生产环境
```bash
cd backend
export ENVIRONMENT=production
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
```

### 前端应用启动

#### 开发环境
```bash
cd frontend
npm run dev
# 访问: http://localhost:5173
```

#### 生产环境
```bash
cd frontend
npm run build
npm run start
# 访问: http://localhost:3000
```

## 📋 环境配置要求

### 后端环境配置

#### 开发环境需要配置：
- **MySQL数据库**: `brzy_medical_dev`
- **数据库用户**: `brzy_dev` / `brzy_dev_pass`
- **Redis** (可选): 密码 `dev_redis_pass`

#### 生产环境需要修改：
- **数据库连接**: 更新 `DATABASE_URL`
- **Redis连接**: 更新 `REDIS_URL`
- **安全密钥**: 更新 `SECRET_KEY` 和 `JWT_SECRET_KEY`
- **域名配置**: 更新 `CORS_ORIGINS` 和 `ALLOWED_HOSTS`

### 前端环境配置

#### 开发环境：
- **API地址**: `http://localhost:8000`
- **WebSocket**: `ws://localhost:8000`

#### 生产环境需要修改：
- **API地址**: 更新 `VITE_API_URL`
- **WebSocket**: 更新 `VITE_WS_URL`

## 🗄️ 数据库初始化

### MySQL 数据库设置

```sql
-- 开发环境
CREATE DATABASE brzy_medical_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'brzy_dev'@'%' IDENTIFIED BY 'brzy_dev_pass';
GRANT ALL PRIVILEGES ON brzy_medical_dev.* TO 'brzy_dev'@'%';

-- 生产环境
CREATE DATABASE brzy_medical_prod CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'brzy_prod'@'%' IDENTIFIED BY 'CHANGE_PROD_PASSWORD';
GRANT ALL PRIVILEGES ON brzy_medical_prod.* TO 'brzy_prod'@'%';

FLUSH PRIVILEGES;
```

### 初始化数据库表

```bash
cd backend
mysql -u brzy_dev -p brzy_medical_dev < init_database.sql
```

## 🔧 完整部署流程

### 后端部署

1. **准备环境**
```bash
cd backend
python3 -m venv venv
source venv/bin/activate  # Linux/macOS
pip install -r requirements.txt
```

2. **配置环境**
```bash
# 复制并修改生产环境配置
cp .env.production .env.production.local
# 编辑 .env.production.local，修改数据库、Redis、域名等配置
```

3. **启动服务**
```bash
export ENVIRONMENT=production
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
```

### 前端部署

1. **准备环境**
```bash
cd frontend
npm install
```

2. **配置环境**
```bash
# 复制并修改生产环境配置
cp .env.production .env.production.local
# 编辑 .env.production.local，修改API地址等配置
```

3. **构建和启动**
```bash
npm run build
npm run start
```

## 🌐 服务器部署建议

### 使用 PM2 (推荐)

#### 后端
```bash
npm install -g pm2
cd backend
export ENVIRONMENT=production
pm2 start "python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4" --name brzy-backend
```

#### 前端
```bash
cd frontend
npm run build
pm2 serve dist 3000 --name brzy-frontend
```

### 使用 Docker

#### 后端 Dockerfile
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
ENV ENVIRONMENT=production
EXPOSE 8000
CMD ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"]
```

#### 前端 Dockerfile
```dockerfile
FROM node:18-alpine as builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
EXPOSE 80
```

## 📝 日志查看

### 后端日志
```bash
# 实时查看日志
tail -f backend/logs/app.log

# 查看错误日志
grep "ERROR" backend/logs/app.log
```

### 前端日志
```bash
# 浏览器控制台查看
# 或在浏览器中执行：
# import { downloadLogs } from '@/utils/logger'
# downloadLogs()
```

## 🚨 故障排除

### 常见问题

1. **端口占用**
```bash
# 查看端口占用
lsof -i :8000  # 后端
lsof -i :5173  # 前端开发
lsof -i :3000  # 前端生产

# 杀死进程
kill -9 <PID>
```

2. **数据库连接失败**
```bash
# 测试数据库连接
cd backend
python -c "from app.database import check_database_connection; print(check_database_connection())"
```

3. **权限问题**
```bash
# 确保目录权限
chmod 755 backend/logs backend/uploads backend/temp backend/generated_pdfs
chmod 755 frontend/logs
```

## 📊 健康检查

### 服务状态检查
```bash
# 后端健康检查
curl http://localhost:8000/health

# 前端访问检查
curl http://localhost:5173  # 开发环境
curl http://localhost:3000  # 生产环境
```

### 服务监控
```bash
# 查看进程状态
ps aux | grep uvicorn  # 后端
ps aux | grep node     # 前端

# 使用 PM2 监控
pm2 status
pm2 logs brzy-backend
pm2 logs brzy-frontend
```
