# ===========================================
# 必然中医 - 医案导出系统 前端配置  
# ===========================================

# API服务地址 (后端API服务)
# 开发环境: http://localhost:8000
# 生产环境: https://your-api-domain.com
VITE_API_URL=http://localhost:8000

# WebSocket服务地址 (用于实时二维码状态更新)
# 开发环境: ws://localhost:8000  
# 生产环境: wss://your-api-domain.com
VITE_WS_URL=ws://localhost:8000

# 应用标题
VITE_APP_TITLE=必然中医 - 医案导出系统

# 应用版本
VITE_APP_VERSION=1.0.0

# 环境模式 (development/production)
NODE_ENV=development

# ===========================================
# 部署配置说明
# ===========================================
#
# 1. 开发环境配置：
#    - VITE_API_URL: http://localhost:8000 (后端开发服务器)
#    - VITE_WS_URL: ws://localhost:8000 (WebSocket开发服务器)
#    - NODE_ENV: development
#
# 2. 生产环境配置：
#    - VITE_API_URL: https://your-api-domain.com (生产API服务器)
#    - VITE_WS_URL: wss://your-api-domain.com (生产WebSocket服务器) 
#    - NODE_ENV: production
#
# 3. 注意事项：
#    - HTTPS部署时WebSocket必须使用wss://
#    - 确保API服务器配置了正确的CORS策略
#    - 生产环境建议配置CDN加速
#
# 4. 构建命令：
#    - 开发: npm run dev
#    - 构建: npm run build
#    - 预览: npm run preview