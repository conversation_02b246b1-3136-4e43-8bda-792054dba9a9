// Global styles
@use './variables';
@use './mixins';
@use './reset';

// Base styles
* {
  box-sizing: border-box;
}

html {
  height: 100%;
  font-size: 14px;
}

body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f7fa;
}

#app {
  height: 100%;
}

// Scrollbar styles
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background-color: #f5f7fa;
}

::-webkit-scrollbar-thumb {
  background-color: #dcdfe6;
  border-radius: 4px;
  
  &:hover {
    background-color: #c0c4cc;
  }
}

// Common classes
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.float-left {
  float: left;
}

.float-right {
  float: right;
}

.clearfix::after {
  content: '';
  display: table;
  clear: both;
}

// Margin utilities
@for $i from 0 through 10 {
  .mt-#{$i * 5} {
    margin-top: #{$i * 5}px !important;
  }
  .mb-#{$i * 5} {
    margin-bottom: #{$i * 5}px !important;
  }
  .ml-#{$i * 5} {
    margin-left: #{$i * 5}px !important;
  }
  .mr-#{$i * 5} {
    margin-right: #{$i * 5}px !important;
  }
}

// Padding utilities
@for $i from 0 through 10 {
  .pt-#{$i * 5} {
    padding-top: #{$i * 5}px !important;
  }
  .pb-#{$i * 5} {
    padding-bottom: #{$i * 5}px !important;
  }
  .pl-#{$i * 5} {
    padding-left: #{$i * 5}px !important;
  }
  .pr-#{$i * 5} {
    padding-right: #{$i * 5}px !important;
  }
}

// Loading animation
.is-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}