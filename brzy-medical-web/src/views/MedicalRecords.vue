<template>
  <div class="medical-records">
    <!-- Header -->
    <div class="page-header">
      <h3>{{ patientName }} - 医案记录</h3>
      <el-button-group>
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button type="primary" @click="showExportDialog">
          <el-icon><Download /></el-icon>
          导出医案
        </el-button>
      </el-button-group>
    </div>
    
    <!-- Filter Bar -->
    <div class="filter-bar">
      <el-form :inline="true">
        <el-form-item label="记录类型">
          <el-select v-model="filterType" placeholder="全部类型" clearable>
            <el-option label="全部类型" value="" />
            <el-option label="处方" value="prescription" />
            <el-option label="问诊单" value="wzd" />
            <el-option label="复诊单" value="fzd" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            :shortcuts="dateShortcuts"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="fetchRecords">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- Statistics -->
    <div class="statistics" v-if="statistics">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="总处方数" :value="statistics.total_prescriptions || 0" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="问诊单数" :value="statistics.total_wzd || 0" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="聊天消息" :value="statistics.total_messages || 0" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="最后就诊" :value="lastVisitDate" />
        </el-col>
      </el-row>
    </div>
    
    <!-- Records Table -->
    <el-table
      ref="tableRef"
      :data="records"
      v-loading="loading"
      @selection-change="handleSelectionChange"
      style="width: 100%"
      row-key="record_id"
    >
      <el-table-column type="selection" width="55" />
      
      <el-table-column prop="record_date" label="日期" width="180">
        <template #default="scope">
          {{ formatDate(scope.row.record_date) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="record_type" label="类型" width="100">
        <template #default="scope">
          <el-tag :type="getRecordTypeTag(scope.row.record_type)">
            {{ getRecordTypeName(scope.row.record_type) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="title" label="标题/主诉" min-width="200" />
      
      <el-table-column prop="summary" label="摘要" min-width="300" show-overflow-tooltip />
      
      <el-table-column prop="doctor_name" label="医生" width="100" />
      
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.status" size="small">
            {{ scope.row.status }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="scope">
          <el-button type="primary" link @click="viewDetail(scope.row)">
            查看详情
          </el-button>
          <el-button type="primary" link @click="exportSingle(scope.row)">
            导出
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- Export Dialog -->
    <el-dialog
      v-model="exportDialogVisible"
      title="导出医案"
      width="600px"
    >
      <el-form :model="exportForm" label-width="100px">
        <el-form-item label="导出内容">
          <el-checkbox-group v-model="exportForm.recordTypes">
            <el-checkbox label="prescription">处方</el-checkbox>
            <el-checkbox label="wzd">问诊单</el-checkbox>
            <el-checkbox label="fzd">复诊单</el-checkbox>
            <el-checkbox label="message">聊天记录</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="exportForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        
        <el-form-item label="导出选项">
          <el-checkbox v-model="exportForm.includeImages">包含图片</el-checkbox>
          <el-checkbox v-model="exportForm.includePrescriptions">包含处方详情</el-checkbox>
        </el-form-item>
        
        <el-form-item label="导出格式">
          <el-radio-group v-model="exportForm.format">
            <el-radio label="pdf">PDF</el-radio>
            <!-- <el-radio label="word">Word</el-radio> -->
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="选中记录" v-if="selectedRecords.length > 0">
          <el-tag
            v-for="record in selectedRecords"
            :key="record.record_id"
            closable
            @close="removeSelection(record)"
            style="margin-right: 5px; margin-bottom: 5px;"
          >
            {{ formatDate(record.record_date) }} - {{ record.title }}
          </el-tag>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="exportDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmExport" :loading="exporting">
          开始导出
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import { medicalApi } from '@/api/medical'
import { patientApi } from '@/api/patients'
import { useAuthStore } from '@/stores/auth'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// Data
const patientId = computed(() => route.params.patientId)
const patientName = ref('')
const records = ref([])
const loading = ref(false)
const statistics = ref(null)
const selectedRecords = ref([])
const exportDialogVisible = ref(false)
const exporting = ref(false)

// Filter
const filterType = ref('')
const dateRange = ref(null)

// Export form
const exportForm = ref({
  recordTypes: ['prescription', 'wzd'],
  dateRange: null,
  includeImages: true,
  includePrescriptions: true,
  format: 'pdf'
})

// Date shortcuts
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    }
  },
  {
    text: '最近一年',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 365)
      return [start, end]
    }
  }
]

// Computed
const lastVisitDate = computed(() => {
  if (statistics.value?.last_visit) {
    return dayjs(statistics.value.last_visit).format('YYYY-MM-DD')
  }
  return '-'
})

// Methods
const fetchPatientInfo = async () => {
  try {
    const response = await patientApi.getPatientDetail(patientId.value)
    if (response.data) {
      patientName.value = response.data.name || '未知患者'
    } else {
      patientName.value = '未知患者'
    }
  } catch (error) {
    console.error('Failed to fetch patient info:', error)
    // Set fallback name when API fails
    patientName.value = '未知患者'
  }
}

const fetchRecords = async () => {
  loading.value = true
  try {
    const params = {
      record_type: filterType.value || undefined,
      start_date: dateRange.value?.[0],
      end_date: dateRange.value?.[1]
    }
    
    const response = await medicalApi.getMedicalRecords(patientId.value, params)
    records.value = response.data || []
    statistics.value = response.statistics || null
  } catch (error) {
    ElMessage.error('获取医案记录失败')
    console.error('Failed to fetch records:', error)
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  fetchRecords()
}

const resetFilter = () => {
  filterType.value = ''
  dateRange.value = null
  fetchRecords()
}

const formatDate = (date) => {
  return date ? dayjs(date).format('YYYY-MM-DD HH:mm') : '-'
}

const getRecordTypeTag = (type) => {
  const typeMap = {
    prescription: 'success',
    wzd: 'primary',
    fzd: 'warning',
    message: 'info'
  }
  return typeMap[type] || 'info'
}

const getRecordTypeName = (type) => {
  const nameMap = {
    prescription: '处方',
    wzd: '问诊单',
    fzd: '复诊单',
    message: '消息'
  }
  return nameMap[type] || type
}

const handleSelectionChange = (selection) => {
  selectedRecords.value = selection
}

const viewDetail = (record) => {
  // Navigate to prescription detail if it's a prescription
  if (record.record_type === 'prescription' && record.record_id) {
    router.push({
      name: 'prescription-detail',
      params: { orderId: record.record_id }
    })
  } else {
    // TODO: Handle other record types
    ElMessage.info('该类型医案详情功能开发中')
  }
}

const exportSingle = (record) => {
  selectedRecords.value = [record]
  showExportDialog()
}

const showExportDialog = () => {
  // 跳转到导出页面
  router.push('/dashboard/export')
}

const removeSelection = (record) => {
  const index = selectedRecords.value.findIndex(r => r.record_id === record.record_id)
  if (index > -1) {
    selectedRecords.value.splice(index, 1)
  }
}

const confirmExport = async () => {
  exporting.value = true
  try {
    // Get medical records for PDF generation
    const medicalRecords = selectedRecords.value.length > 0 
      ? selectedRecords.value 
      : records.value

    // Start PDF generation
    const response = await fetch('/api/pdf/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Session-Id': authStore.sessionId
      },
      body: JSON.stringify({
        patient_id: patientId.value,
        medical_records: medicalRecords
      })
    })
    
    const result = await response.json()
    
    if (result.code === 200 && result.data?.task_id) {
      ElMessage.success('PDF生成任务已创建，请稍后下载')
      exportDialogVisible.value = false
      
      // Check export status
      checkExportStatus(result.data.task_id)
    } else {
      throw new Error(result.message || 'PDF生成失败')
    }
  } catch (error) {
    ElMessage.error('导出失败：' + (error.message || '请重试'))
    console.error('Export failed:', error)
  } finally {
    exporting.value = false
  }
}

const checkExportStatus = async (taskId) => {
  let attempts = 0
  const maxAttempts = 30 // 30 seconds max wait time
  
  const checkStatus = async () => {
    try {
      const response = await medicalApi.checkExportStatus(taskId)
      
      if (response.code === 200) {
        const status = response.data.status
        
        if (status === 'completed') {
          ElMessage.success('PDF生成完成，开始下载...')
          // Trigger download
          const downloadUrl = medicalApi.downloadExport(taskId)
          const link = document.createElement('a')
          link.href = downloadUrl
          link.target = '_blank'
          link.download = `medical_record_${patientId.value}.pdf`
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          return
        } else if (status === 'failed') {
          ElMessage.error('PDF生成失败：' + (response.data.error || '未知错误'))
          return
        } else if (status === 'processing') {
          // Continue checking
          attempts++
          if (attempts < maxAttempts) {
            setTimeout(checkStatus, 1000) // Check every second
          } else {
            ElMessage.warning('PDF生成超时，请稍后手动检查')
          }
        }
      }
    } catch (error) {
      console.error('Failed to check export status:', error)
      ElMessage.error('检查导出状态失败')
    }
  }
  
  // Start checking
  checkStatus()
}

// Lifecycle
onMounted(() => {
  fetchPatientInfo()
  fetchRecords()
})
</script>

<style lang="scss" scoped>
.medical-records {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h3 {
      margin: 0;
      font-size: 20px;
      color: #303133;
    }
  }
  
  .filter-bar {
    background: white;
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 20px;
  }
  
  .statistics {
    background: white;
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 20px;
    
    .el-statistic {
      text-align: center;
    }
  }
  
  .el-table {
    background: white;
    border-radius: 4px;
  }
}
</style>