<template>
  <div class="prescription-detail">
    <div class="header-section">
      <el-page-header @back="goBack">
        <template #content>
          <span class="header-title">用药详情</span>
        </template>
        <template #extra>
          <el-button 
            type="primary" 
            :disabled="exportLoading"
            @click="exportToPDF"
            v-if="prescriptionData"
          >
            <el-icon v-if="!exportLoading"><Download /></el-icon>
            <el-icon v-else class="loading-icon"><Loading /></el-icon>
            导出PDF
          </el-button>
        </template>
      </el-page-header>
    </div>

    <div v-loading="loading" class="content-section">
      <div v-if="prescriptionData" class="detail-content">
        <!-- 基本信息 -->
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
              <el-tag :type="getStatusType(prescriptionData.orderStatus)">
                {{ prescriptionData.orderStatus }}
              </el-tag>
            </div>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="订单编号">{{ prescriptionData.id }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ prescriptionData.createdTime }}</el-descriptions-item>
            <el-descriptions-item label="患者姓名">{{ prescriptionData.patientName }}</el-descriptions-item>
            <el-descriptions-item label="患者年龄">{{ prescriptionData.patientAge }}岁</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{ prescriptionData.patientMobile }}</el-descriptions-item>
            <el-descriptions-item label="患者类型">{{ prescriptionData.patientType }}</el-descriptions-item>
            <el-descriptions-item label="订单类型">{{ prescriptionData.orderType }}</el-descriptions-item>
            <el-descriptions-item label="支付状态">
              <el-tag :type="prescriptionData.payStatus === 'PAYED' ? 'success' : 'info'">
                {{ prescriptionData.payStatus === 'PAYED' ? '已支付' : '未支付' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item v-if="prescriptionData.payTime" label="支付时间">
              {{ prescriptionData.payTime }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 诊断信息 -->
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <span>诊断信息</span>
            </div>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="辨证">{{ prescriptionData.dialectical }}</el-descriptions-item>
            <el-descriptions-item label="备注">{{ prescriptionData.rosterRemark }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 处方信息 -->
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <span>处方信息</span>
            </div>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="剂型">{{ prescriptionData.drugForm }}</el-descriptions-item>
            <el-descriptions-item label="制法">{{ prescriptionData.mode }}</el-descriptions-item>
            <el-descriptions-item label="处方天数">{{ prescriptionData.preDays }}天</el-descriptions-item>
            <el-descriptions-item label="每日用量">{{ prescriptionData.preUsage }}次</el-descriptions-item>
            <el-descriptions-item label="总剂数">{{ prescriptionData.totalCount }}剂</el-descriptions-item>
            <el-descriptions-item label="总重量">{{ prescriptionData.totalWeight }}g</el-descriptions-item>
            <el-descriptions-item label="服用时间">{{ prescriptionData.eatTime || '遵医嘱' }}</el-descriptions-item>
            <el-descriptions-item label="禁忌">{{ prescriptionData.contraindication || '无' }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 药材明细 -->
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <span>药材明细</span>
              <span class="medicine-count">共 {{ prescriptionData.details.length }} 味药</span>
            </div>
          </template>
          <el-table :data="prescriptionData.details" stripe>
            <el-table-column prop="id" label="药材ID" width="80" />
            <el-table-column prop="name" label="药材名称" />
            <el-table-column prop="amount" label="用量" width="80">
              <template #default="{ row }">
                {{ row.amount }}{{ row.unit }}
              </template>
            </el-table-column>
            <el-table-column prop="totalPrice" label="小计" width="100">
              <template #default="{ row }">
                ¥{{ row.totalPrice }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 费用明细 -->
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <span>费用明细</span>
            </div>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="药材费">¥{{ prescriptionData.baseDrugPrice }}</el-descriptions-item>
            <el-descriptions-item label="制作费">¥{{ prescriptionData.makeCost }}</el-descriptions-item>
            <el-descriptions-item label="医疗服务费">¥{{ prescriptionData.medicalServiceFee }}</el-descriptions-item>
            <el-descriptions-item label="诊察费">¥{{ prescriptionData.consultationFee }}</el-descriptions-item>
            <el-descriptions-item label="运费">¥{{ prescriptionData.carriage }}</el-descriptions-item>
            <el-descriptions-item label="总计">
              <span class="total-amount">¥{{ prescriptionData.amount }}</span>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 配送信息 -->
        <el-card v-if="prescriptionData.reciverAddress" class="info-card">
          <template #header>
            <div class="card-header">
              <span>配送信息</span>
            </div>
          </template>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="收货人">{{ prescriptionData.orderReciver }}</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{ prescriptionData.reciverMobile }}</el-descriptions-item>
            <el-descriptions-item label="收货地址">{{ prescriptionData.reciverAddress }}</el-descriptions-item>
            <el-descriptions-item label="配送中心">{{ prescriptionData.dcName }}</el-descriptions-item>
            <el-descriptions-item label="快递公司">{{ prescriptionData.carrier || '未发货' }}</el-descriptions-item>
            <el-descriptions-item label="快递单号">{{ prescriptionData.deliverySn || '未发货' }}</el-descriptions-item>
            <el-descriptions-item v-if="prescriptionData.logisticsUrl" label="物流查询">
              <el-link :href="prescriptionData.logisticsUrl" target="_blank" type="primary">
                查看物流信息
              </el-link>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 其他信息 -->
        <el-card v-if="prescriptionData.productProviteName" class="info-card">
          <template #header>
            <div class="card-header">
              <span>其他信息</span>
            </div>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="药品提供方">{{ prescriptionData.productProviteName }}</el-descriptions-item>
            <el-descriptions-item label="数据来源">{{ prescriptionData.source }}</el-descriptions-item>
          </el-descriptions>
        </el-card>
      </div>

      <!-- 空状态 -->
      <el-empty v-else-if="!loading" description="暂无数据" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Download, Loading } from '@element-plus/icons-vue'
import { medicalApi } from '@/api/medical'

const route = useRoute()
const router = useRouter()

const loading = ref(false)
const prescriptionData = ref(null)
const exportLoading = ref(false)

// 获取订单状态的标签类型
const getStatusType = (status) => {
  const statusMap = {
    'PENDING': 'warning',
    'PROCESSING': 'primary',
    'COMPLETED': 'success',
    'CANCELLED': 'info',
    'ROLLBACK': 'danger'
  }
  return statusMap[status] || 'info'
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 获取处方详情
const fetchPrescriptionDetail = async () => {
  const orderId = route.params.orderId
  if (!orderId) {
    ElMessage.error('订单ID不存在')
    return
  }

  loading.value = true
  try {
    const res = await medicalApi.getPrescriptionOrderDetail(orderId)
    if (res.code === 200) {
      prescriptionData.value = res.data
    } else {
      ElMessage.error(res.message || '获取处方详情失败')
    }
  } catch (error) {
    console.error('获取处方详情失败:', error)
    ElMessage.error('获取处方详情失败')
  } finally {
    loading.value = false
  }
}

// 导出PDF
const exportToPDF = async () => {
  const orderId = route.params.orderId
  if (!orderId) {
    ElMessage.error('订单ID不存在')
    return
  }

  exportLoading.value = true
  try {
    // 开始生成PDF
    const generateResponse = await medicalApi.generatePrescriptionPDF(orderId)
    if (generateResponse.code !== 200) {
      ElMessage.error(generateResponse.message || 'PDF生成失败')
      return
    }

    const taskId = generateResponse.data.task_id
    ElMessage.success('PDF生成中，请稍候...')

    // 轮询检查生成状态
    const checkStatus = async () => {
      try {
        const statusResponse = await medicalApi.checkExportStatus(taskId)
        if (statusResponse.code !== 200) {
          ElMessage.error('检查PDF状态失败')
          return
        }

        const status = statusResponse.data.status

        if (status === 'completed') {
          // 下载PDF
          try {
            const response = await medicalApi.downloadExport(taskId)
            const blob = new Blob([response.data], { type: 'application/pdf' })
            const downloadUrl = window.URL.createObjectURL(blob)
            const link = document.createElement('a')
            link.href = downloadUrl
            link.download = `prescription_${orderId}.pdf`
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            window.URL.revokeObjectURL(downloadUrl)
            
            ElMessage.success('PDF下载成功')
          } catch (error) {
            console.error('下载PDF失败:', error)
            ElMessage.error('下载PDF失败')
          }
        } else if (status === 'failed') {
          ElMessage.error(statusResponse.data.error || 'PDF生成失败')
        } else {
          // 继续轮询
          setTimeout(checkStatus, 2000)
        }
      } catch (error) {
        console.error('检查PDF状态失败:', error)
        ElMessage.error('检查PDF状态失败')
      }
    }

    // 开始轮询
    setTimeout(checkStatus, 2000)

  } catch (error) {
    console.error('导出PDF失败:', error)
    ElMessage.error('导出PDF失败')
  } finally {
    exportLoading.value = false
  }
}

onMounted(() => {
  fetchPrescriptionDetail()
})
</script>

<style lang="scss" scoped>
.prescription-detail {
  height: 100%;
  display: flex;
  flex-direction: column;

  .header-section {
    padding: 20px;
    background: #fff;
    border-bottom: 1px solid #ebeef5;

    .header-title {
      font-size: 18px;
      font-weight: 500;
    }

    .loading-icon {
      animation: spin 1s linear infinite;
    }
  }

  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  .content-section {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #f5f7fa;

    .detail-content {
      max-width: 1200px;
      margin: 0 auto;
    }

    .info-card {
      margin-bottom: 20px;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .medicine-count {
          font-size: 14px;
          color: #909399;
        }
      }

      .total-amount {
        font-size: 18px;
        font-weight: bold;
        color: #ff6b6b;
      }
    }

    :deep(.el-descriptions) {
      .el-descriptions__label {
        background-color: #fafafa;
      }
    }

    :deep(.el-table) {
      .el-table__header {
        th {
          background-color: #fafafa;
        }
      }
    }
  }
}
</style>