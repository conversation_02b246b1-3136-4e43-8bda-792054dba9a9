/**
 * HTTP request utility based on axios
 */
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import router from '@/router'

// Create axios instance
const service = axios.create({
  baseURL: import.meta.env.VITE_API_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor
service.interceptors.request.use(
  config => {
    const authStore = useAuthStore()
    
    // Add session ID if available
    if (authStore.sessionId) {
      config.headers['X-Session-Id'] = authStore.sessionId
    }
    
    return config
  },
  error => {
    console.error('Request error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor
service.interceptors.response.use(
  response => {
    // Handle blob response (file downloads)
    if (response.config.responseType === 'blob') {
      return response
    }
    
    const res = response.data
    
    // Handle successful response
    if (res.code === 200) {
      return res
    }
    
    // Handle business errors
    ElMessage({
      message: res.message || 'Error',
      type: 'error',
      duration: 5000
    })
    
    return Promise.reject(new Error(res.message || 'Error'))
  },
  error => {
    console.error('Response error:', error)
    
    const authStore = useAuthStore()
    
    // Handle different error status codes
    if (error.response) {
      switch (error.response.status) {
        case 401:
          // Token expired or invalid
          ElMessageBox.confirm(
            '登录已过期，请重新登录',
            '提示',
            {
              confirmButtonText: '重新登录',
              cancelButtonText: '取消',
              type: 'warning'
            }
          ).then(() => {
            authStore.logout()
            router.push('/login')
          })
          break
          
        case 403:
          ElMessage({
            message: '没有权限访问此资源',
            type: 'error',
            duration: 5000
          })
          break
          
        case 404:
          ElMessage({
            message: '请求的资源不存在',
            type: 'error',
            duration: 5000
          })
          break
          
        case 500:
          ElMessage({
            message: '服务器错误，请稍后重试',
            type: 'error',
            duration: 5000
          })
          break
          
        default:
          ElMessage({
            message: error.response.data?.message || '请求失败',
            type: 'error',
            duration: 5000
          })
      }
    } else if (error.request) {
      // Network error
      ElMessage({
        message: '网络错误，请检查网络连接',
        type: 'error',
        duration: 5000
      })
    } else {
      // Other errors
      ElMessage({
        message: '请求失败，请重试',
        type: 'error',
        duration: 5000
      })
    }
    
    return Promise.reject(error)
  }
)

export default service