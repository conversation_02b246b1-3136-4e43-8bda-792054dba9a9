/**
 * WebSocket connection utility
 */
import { createLogger } from './logger'

const logger = createLogger('WebSocket')

export function connectWebSocket(authCode, handlers = {}) {
  const wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:8000'
  const fullUrl = `${wsUrl}/ws/${authCode}`
  
  logger.info('Connecting to WebSocket', { url: fullUrl, authCode })
  
  const ws = new WebSocket(fullUrl)
  
  // Connection opened
  ws.onopen = (event) => {
    logger.info('WebSocket connection opened')
    if (handlers.onOpen) {
      handlers.onOpen(event)
    }
    
    // Send initial auth message
    ws.send(JSON.stringify({
      type: 'auth',
      auth_code: authCode,
      timestamp: new Date().toISOString()
    }))
  }
  
  // Message received
  ws.onmessage = (event) => {
    if (handlers.onMessage) {
      handlers.onMessage(event)
    }
  }
  
  // Error occurred
  ws.onerror = (event) => {
    console.error('WebSocket error:', event)
    if (handlers.onError) {
      handlers.onError(event)
    }
  }
  
  // Connection closed
  ws.onclose = (event) => {
    console.log('WebSocket connection closed:', event.code, event.reason)
    if (handlers.onClose) {
      handlers.onClose(event)
    }
  }
  
  // Add custom methods
  ws.sendJSON = (data) => {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(data))
    } else {
      console.warn('WebSocket is not open. State:', ws.readyState)
    }
  }
  
  return ws
}

export function createReconnectingWebSocket(authCode, handlers = {}, options = {}) {
  const {
    reconnectInterval = 3000,
    maxReconnectAttempts = 5,
    reconnectDecay = 1.5
  } = options
  
  let ws = null
  let reconnectAttempts = 0
  let reconnectTimeout = null
  let forceClosed = false
  
  const connect = () => {
    ws = connectWebSocket(authCode, {
      ...handlers,
      onClose: (event) => {
        if (handlers.onClose) {
          handlers.onClose(event)
        }
        
        // Attempt to reconnect if not forcefully closed
        if (!forceClosed && reconnectAttempts < maxReconnectAttempts) {
          const timeout = reconnectInterval * Math.pow(reconnectDecay, reconnectAttempts)
          console.log(`Reconnecting in ${timeout}ms...`)
          
          reconnectTimeout = setTimeout(() => {
            reconnectAttempts++
            connect()
          }, timeout)
        }
      },
      onOpen: (event) => {
        console.log('WebSocket reconnected')
        reconnectAttempts = 0
        
        if (handlers.onOpen) {
          handlers.onOpen(event)
        }
      }
    })
  }
  
  // Initial connection
  connect()
  
  // Return control object
  return {
    get websocket() {
      return ws
    },
    
    close() {
      forceClosed = true
      if (reconnectTimeout) {
        clearTimeout(reconnectTimeout)
      }
      if (ws) {
        ws.close()
      }
    },
    
    reconnect() {
      forceClosed = false
      reconnectAttempts = 0
      if (ws) {
        ws.close()
      }
      connect()
    },
    
    send(data) {
      if (ws && ws.readyState === WebSocket.OPEN) {
        ws.send(data)
      }
    },
    
    sendJSON(data) {
      if (ws && ws.sendJSON) {
        ws.sendJSON(data)
      }
    }
  }
}