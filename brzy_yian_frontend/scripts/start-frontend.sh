#!/bin/bash
set -e

# Frontend Runtime Configuration Injection Script
# 用于在容器启动时注入环境变量配置

echo "🚀 Starting BRZY Medical Frontend with runtime configuration..."

# 设置默认值
VITE_API_URL="${VITE_API_URL:-http://localhost:8080}"
VITE_WS_URL="${VITE_WS_URL:-ws://localhost:8080}"
VITE_APP_TITLE="${VITE_APP_TITLE:-必然中医 - 医案导出系统}"
VITE_APP_VERSION="${VITE_APP_VERSION:-1.0.0}"
PORT="${PORT:-5173}"

echo "📝 Injecting runtime configuration..."
echo "API URL: ${VITE_API_URL}"
echo "WebSocket URL: ${VITE_WS_URL}"
echo "App Title: ${VITE_APP_TITLE}"
echo "App Version: ${VITE_APP_VERSION}"
echo "Port: ${PORT}"

# 创建运行时配置目录
mkdir -p /workspace/dist/config

# 生成运行时环境配置文件
cat > /workspace/dist/config/env.js << EOF
// Runtime Environment Configuration
// This file is generated at container startup
window.__RUNTIME_CONFIG__ = {
  VITE_API_URL: '${VITE_API_URL}',
  VITE_WS_URL: '${VITE_WS_URL}',
  VITE_APP_TITLE: '${VITE_APP_TITLE}',
  VITE_APP_VERSION: '${VITE_APP_VERSION}',
  GENERATED_AT: '$(date -u +"%Y-%m-%dT%H:%M:%SZ")'
};

// 为兼容性导出到全局
if (typeof window !== 'undefined') {
  window.process = window.process || {};
  window.process.env = window.process.env || {};
  Object.assign(window.process.env, window.__RUNTIME_CONFIG__);
}
EOF

# 生成 Nginx 配置
cat > /etc/nginx/nginx.conf << 'EOF'
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log notice;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    # 基础配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json
        image/svg+xml;

    server {
        listen ${PORT} default_server;
        listen [::]:${PORT} default_server;
        server_name _;
        root /workspace/dist;
        index index.html;

        # 静态资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            try_files $uri =404;
        }

        # 运行时配置文件
        location /config/env.js {
            expires -1;
            add_header Cache-Control "no-store, no-cache, must-revalidate";
            try_files $uri =404;
        }

        # 健康检查端点
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # SPA 路由支持
        location / {
            try_files $uri $uri/ /index.html;
            add_header Cache-Control "no-cache";
        }

        # 安全头部
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    }
}
EOF

# 更新 index.html 以加载运行时配置
if [ -f "/workspace/dist/index.html" ]; then
    # 在 head 标签中注入运行时配置脚本
    sed -i 's|</head>|  <script src="/config/env.js"></script>\n  </head>|' /workspace/dist/index.html
    echo "✅ Runtime configuration script injected into index.html"
fi

echo "🌐 Starting Nginx server on port ${PORT}..."

# 测试 Nginx 配置
nginx -t

# 启动 Nginx
exec nginx -g 'daemon off;'