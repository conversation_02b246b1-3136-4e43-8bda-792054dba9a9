# CNB buildpack configuration for Vue 3 frontend
# 配置外置化 - 构建时不包含环境配置

nodejs:
  # Node.js version specification
  version: "18.*"
  
  # NPM configuration
  npm:
    # Use npm install for compatibility with devDependencies needed for build
    install-command: "install"
    # Include dev dependencies needed for Vite build process
    include-devdependencies: true
    # Cache node_modules for faster builds  
    cache-dependencies: true
  
  # Build optimization
  build:
    # Enable build cache for faster subsequent builds
    cache: true
    # Build command for production (不包含环境变量)
    command: "npm run build"
    # Output directory
    output-dir: "dist"
    # 排除构建时的环境变量注入
    exclude-env-files: [".env*", "config/*.local.*"]
  
  # Runtime configuration template
  runtime:
    # 使用 nginx 作为静态文件服务器
    web-server: "nginx"
    # Port configuration (可运行时覆盖)
    port: 5173
    # Health check endpoint
    health-check: "/index.html"
    # 启用配置模板化
    enable-config-template: true
    # 配置文件模板路径
    config-template: "/workspace/config/nginx.conf.template"

# Package manager settings
package-manager: "npm"

# Build process configuration (移除环境变量相关)
pre-build:
  commands:
    - "echo 'Starting frontend build process...'"
    - "npm --version"
    - "node --version"
    - "echo 'Building for artifact deployment (no env injection)'"

post-build:
  commands:
    - "echo 'Frontend build completed successfully'"
    - "ls -la dist/"
    - "echo 'Creating config template for runtime injection'"
    - "mkdir -p /workspace/config"

# Performance optimizations
optimization:
  # Enable tree shaking
  tree-shaking: true
  # Enable code splitting
  code-splitting: true
  # Asset optimization
  assets:
    # Compress images
    images: true
    # Minify CSS/JS
    minify: true
    # Generate gzip and brotli
    gzip: true
    brotli: true
  # 移除调试信息
  strip-debug: true

# Security settings
security:
  # Remove source maps in production
  source-maps: false
  # Strip debug information
  debug-info: false
  # Remove comments
  remove-comments: true

# 构建时环境变量（仅构建相关，不包含运行时配置）
build-env:
  NODE_ENV: "production"
  # Vite 构建优化
  VITE_BUILD_SOURCEMAP: "false"
  VITE_BUILD_MINIFY: "true"
  # 禁用构建时环境变量注入
  VITE_DISABLE_ENV_INJECTION: "true"

# 运行时配置模板设置
runtime-config:
  # 支持的环境变量列表（运行时注入）
  supported-env-vars:
    - "VITE_API_URL"
    - "VITE_WS_URL" 
    - "VITE_APP_TITLE"
    - "VITE_APP_VERSION"
  
  # 配置模板文件
  templates:
    - source: "nginx.conf.template"
      destination: "/etc/nginx/nginx.conf"
    - source: "env.js.template"  
      destination: "/workspace/dist/config/env.js"

# Web服务器配置
web-server:
  type: "nginx"
  config:
    # 启用gzip压缩
    gzip: true
    # 缓存策略
    cache-control:
      static-assets: "1y"
      html: "no-cache"
    # SPA路由支持
    spa-fallback: true
    # 健康检查
    health-endpoint: "/health"