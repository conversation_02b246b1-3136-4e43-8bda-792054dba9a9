# CNB Build ignore file - 配置外置化优化
# 专为制品库构建优化，严格排除运行时配置

# ===========================================
# 核心配置外置（构建时不包含）
# ===========================================

# 环境配置文件（严格排除）
.env*
!.env.example.template
config/*.local.*
config/*.production.*
config/*.staging.*
config/*.dev.*
config/secrets.*
environment/
env/

# 运行时配置目录
runtime/
secrets/
private/
ssl/
certs/
keys/

# 敏感文件
*.pem
*.key
*.crt
*.p12
*.jks
id_rsa*
ssh_host_*

# ===========================================
# 开发和构建排除
# ===========================================

# Git and version control
.git/
.gitignore
.github/workflows/ci.yml
.github/workflows/deploy.yml
*.orig

# 文档文件（减少构建体积）
README.md
*.md
docs/
CHANGELOG*
LICENSE*
*.rst
*.txt

# 日志和临时文件
logs/
*.log
*.tmp
*.temp
temp/
tmp/
.log

# 运行时数据目录
generated_pdfs/
uploads/
downloads/
data/
storage/

# ===========================================
# 开发工具和IDE
# ===========================================

# IDE配置
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db
.project
.settings/

# 编辑器配置
.editorconfig.local
*.sublime-*

# ===========================================
# 前端构建排除
# ===========================================

# Node.js
brzy-medical-web/node_modules/
brzy-medical-web/dist/
brzy-medical-web/.vite/
brzy-medical-web/coverage/
brzy-medical-web/*.tsbuildinfo
brzy-medical-web/.nuxt/
brzy-medical-web/.next/

# 前端配置文件
brzy-medical-web/.env*
!brzy-medical-web/.env.example.template
brzy-medical-web/config/*.local.*

# 前端缓存和临时文件
brzy-medical-web/.cache/
brzy-medical-web/.parcel-cache/
brzy-medical-web/.eslintcache
brzy-medical-web/.stylelintcache

# ===========================================
# 后端构建排除
# ===========================================

# Python
brzy_medical_export/venv/
brzy_medical_export/__pycache__/
brzy_medical_export/*.pyc
brzy_medical_export/*.pyo
brzy_medical_export/*.pyd
brzy_medical_export/.pytest_cache/
brzy_medical_export/htmlcov/
brzy_medical_export/.coverage
brzy_medical_export/.mypy_cache/
brzy_medical_export/.ruff_cache/
brzy_medical_export/pip-wheel-metadata/

# 后端配置文件
brzy_medical_export/.env*
!brzy_medical_export/.env.example.template
brzy_medical_export/config/*.local.*
brzy_medical_export/alembic.ini.local

# 数据库文件
*.db
*.sqlite
*.sqlite3
*.db-journal

# ===========================================
# 测试和质量检查
# ===========================================

# 测试文件
tests/
test/
*test*
**/*test*
**/*spec*
coverage/
.coverage
.nyc_output/
junit.xml
pytest.ini.local

# 质量检查
.flake8.local
.pylintrc.local
.bandit
.safety

# ===========================================
# 构建产物和缓存
# ===========================================

# 构建产物
build/
dist/
out/
target/

# 压缩文件
*.tar.gz
*.zip
*.rar
*.7z

# 包管理器缓存和锁文件备份
package-lock.json.backup
yarn.lock.backup
poetry.lock.backup
Pipfile.lock.backup

# 系统缓存
.cache/
.npm/
.yarn/
.pip/

# ===========================================
# 媒体和资源文件优化
# ===========================================

# 大型媒体文件
*.mp4
*.avi
*.mov
*.wmv
*.flv
*.webm
*.mkv

# 大型图片文件（保留必要的小图标）
*.psd
*.ai
*.sketch
*@3x.png
*@4x.png

# 非必需字体文件
fonts/*.otf
fonts/*.woff
fonts/*.woff2
!fonts/STHeiti*
!fonts/Arial_Unicode*

# ===========================================
# 备份和版本文件
# ===========================================

# 备份文件
*.backup
*.bak
*.old
*~
*.orig
*.save

# 版本控制
.svn/
.hg/
.bzr/

# ===========================================
# 性能分析和调试
# ===========================================

# 性能分析文件
*.prof
*.cpuprofile
*.heapprofile
*.trace

# 调试文件
*.dmp
*.dump
core.*

# ===========================================
# 部署和运维脚本（保留必要的）
# ===========================================

# 开发脚本（构建时不需要）
start_dev.sh
stop_dev.sh
debug.sh
local_*.sh

# 部署脚本（运行时提供）
deploy.sh
docker-compose.yml
docker-compose.*.yml
!docker-compose.template.yml

# Kubernetes配置
k8s/
kubernetes/
helm/

# ===========================================
# CNB 特定排除
# ===========================================

# CNB 缓存目录
.cnb/
.pack/

# Buildpack 调试文件
buildpack-debug.log
pack-debug.log

# ===========================================
# 特殊标记文件
# ===========================================

# 本地开发标记
.local
.development
.debug
LOCAL_ONLY
DEV_ONLY