# CNB Project Configuration for BRZY Medical Export System
# 专注于构建优化的精简配置

[project]
id = "brzy-medical-system"
name = "必然中医医案导出系统"
version = "1.0.0"
source-url = "https://github.com/your-org/brzy_output_web"
schema-version = "0.2"

[build]
# 构建包含的文件（仅必要文件）
include = [
  "brzy-medical-web/src/**/*",
  "brzy-medical-web/public/**/*", 
  "brzy-medical-web/package*.json",
  "brzy-medical-web/vite.config.js",
  "brzy-medical-web/index.html",
  "brzy-medical-web/buildpack.yml",
  "brzy-medical-web/Procfile",
  "brzy_medical_export/app/**/*",
  "brzy_medical_export/requirements.txt",
  "brzy_medical_export/buildpack.yml",
  "brzy_medical_export/Procfile",
  "brzy_medical_export/runtime.txt",
  "brzy_medical_export/fonts/**/*"
]

# 构建排除文件（优化构建性能）
exclude = [
  "**/.git/**",
  "**/node_modules/**", 
  "**/venv/**",
  "**/__pycache__/**",
  "**/logs/**",
  "**/temp/**",
  "**/generated_pdfs/**",
  "**/uploads/**",
  "**/runtime/**",
  "**/*.log",
  "**/.env*",           # 重要：排除所有环境配置文件
  "**/config/*.local.*", # 排除本地配置
  "**/secrets/**",      # 排除敏感信息
  "**/*.md",
  "**/docs/**",
  "**/.vscode/**",
  "**/.idea/**",
  "**/test/**",
  "**/tests/**",
  "**/*.test.*",
  "**/*.spec.*",
  "**/coverage/**",
  "**/.coverage",
  "**/dist/**",         # 排除旧的构建产物
  "**/build/**"
]

# 构建时环境变量（仅构建相关）
[build.env]
# Node.js 构建配置
NODE_ENV = "production"
BP_NODE_VERSION = "18.*"
BP_NPM_VERSION = "latest"

# Python 构建配置
PYTHON_ENV = "production"
BP_PYTHON_VERSION = "3.11.*"
BP_PIP_VERSION = "23.*"

# 构建优化
PYTHONDONTWRITEBYTECODE = "1"
PYTHONUNBUFFERED = "1"

# CNB 构建器配置
CNB_STACK_ID = "io.buildpacks.stacks.jammy"
CNB_USER_ID = "1000"
CNB_GROUP_ID = "1000"

# 构建缓存优化
BP_INCLUDE_FILES = "package*.json,requirements.txt,buildpack.yml"
BP_EXCLUDE_FILES = ".env*,config/*.local.*"

# 元数据信息
[metadata]
description = "必然中医医案导出系统 - 云原生制品构建"
keywords = ["医疗", "中医", "CNB", "制品库"]
maintainer = "BRZY Medical Team"
build-strategy = "artifacts-only"

# 构建输出配置
[metadata.artifacts]
frontend-port = 5173
backend-port = 8080
registry-namespace = "brzy-medical"

# 构建性能优化
[metadata.optimization]
enable-layer-cache = true
enable-launch-cache = true
strip-debug-symbols = true
compress-layers = true