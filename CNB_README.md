# 🚀 CNB 云原生部署方案

必然中医医案导出系统现已支持 **Cloud Native Buildpacks (CNB)** 部署方案，实现真正的云原生化。

## ✨ 特性

- 🏗️ **无 Dockerfile**: 自动检测技术栈并构建优化镜像
- 🔒 **安全至上**: 自动漏洞扫描和安全补丁
- ⚡ **极速部署**: 智能缓存和增量构建
- 📊 **完整监控**: 健康检查、指标收集、日志聚合
- 🎯 **一键部署**: GitHub Actions 自动化 CI/CD

## 🚀 快速开始

### 本地开发测试

```bash
# 构建并启动完整环境
./scripts/cnb_local_build.sh build
./scripts/cnb_local_build.sh start

# 检查健康状态
./scripts/cnb_local_build.sh health

# 查看服务
# 前端: http://localhost:5173
# 后端: http://localhost:8080
# 健康检查: http://localhost:8080/health
```

### 生产部署

1. **配置 GitHub Secrets** (详见 [部署文档](docs/deployment/CNB_1PANEL_DEPLOYMENT.md))
2. **推送到 release 分支** 自动触发 CNB 构建和部署
3. **1Panel 自动管理** 容器生命周期

## 📁 新增文件结构

```
├── project.toml                    # CNB 项目配置
├── .cnbignore                      # 构建忽略文件
├── docker-compose.cnb.yml         # 容器编排配置
├── .github/workflows/cnb-deploy.yml # CNB 自动部署流程
├── brzy-medical-web/
│   ├── buildpack.yml              # 前端 CNB 配置
│   └── Procfile                    # 进程定义
├── brzy_medical_export/
│   ├── buildpack.yml              # 后端 CNB 配置
│   ├── Procfile                    # 进程定义
│   └── runtime.txt                 # Python 版本
├── scripts/
│   └── cnb_local_build.sh         # 本地构建脚本
└── docs/deployment/
    └── CNB_1PANEL_DEPLOYMENT.md   # 详细部署文档
```

## 🔧 配置说明

### CNB Buildpacks

- **前端**: Paketo Node.js Buildpack
  - 自动检测 Vue 3 + Vite
  - 优化生产构建
  - 内置静态文件服务

- **后端**: Paketo Python Buildpack  
  - 自动检测 FastAPI
  - 智能依赖管理
  - 多进程 uvicorn 配置

### 健康检查端点

- `GET /health` - 完整健康状态
- `GET /ready` - 就绪检查 (Kubernetes Ready Probe)
- `GET /live` - 存活检查 (Kubernetes Liveness Probe)  
- `GET /metrics` - 系统指标

## 🛠️ 开发工具

```bash
# 安装 Pack CLI (macOS)
brew install buildpacks/tap/pack

# 安装 Pack CLI (Linux)
curl -sSL "https://github.com/buildpacks/pack/releases/download/v0.32.1/pack-v0.32.1-linux.tgz" | sudo tar -C /usr/local/bin/ --no-same-owner -xzv pack

# 本地构建前端
pack build brzy-frontend:local --builder paketobuildpacks/builder-jammy-base --path brzy-medical-web

# 本地构建后端  
pack build brzy-backend:local --builder paketobuildpacks/builder-jammy-base --path brzy_medical_export
```

## 🌟 部署优势对比

| 特性 | 传统部署 | CNB 部署 |
|------|----------|----------|
| 配置复杂度 | 高 | 低 |
| 安全性 | 手动管理 | 自动扫描 |
| 镜像大小 | 较大 | 优化 |
| 构建速度 | 慢 | 快速 |
| 标准化 | 低 | 高 |
| 维护成本 | 高 | 低 |

## 📚 相关文档

- [详细部署指南](docs/deployment/CNB_1PANEL_DEPLOYMENT.md)
- [传统部署方式](docs/deployment/1PANEL_DEPLOYMENT_GUIDE.md)  
- [故障排查指南](docs/troubleshooting/)
- [API 文档](docs/api/)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进 CNB 部署方案！

## 📄 许可证

[项目许可证信息]

---

💡 **提示**: CNB 方案完全向后兼容，您可以随时切换回传统部署方式。