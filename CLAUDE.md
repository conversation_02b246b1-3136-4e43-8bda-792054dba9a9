# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

always response in 中文。 

## 项目概述

这是一个**必然中医医案导出系统**，采用前后端分离架构，为必然中医iOS应用提供Web端的医案数据导出功能。

**部署架构**: 使用 CNB (Cloud Native Build) 云原生构建，生成前后端独立制品，支持配置外置化部署。

## 技术架构

### 前端 (brzy-medical-web/)
- Vue 3 + Composition API
- Vite 5 构建工具
- Element Plus UI框架
- Pinia 状态管理
- Vue Router 4 路由管理

### 后端 (brzy_medical_export/)
- FastAPI 异步Web框架
- SQLAlchemy 2.0 ORM
- MySQL + Redis 数据存储
- WebSocket 实时通信
- ReportLab PDF生成

## 常用开发命令

### 前端开发
```bash
cd brzy-medical-web
npm install           # 安装依赖
npm run dev          # 启动开发服务器
npm run build        # 构建生产版本
npm run lint         # 代码检查
npm run lint:fix     # 自动修复代码问题
```

### 后端开发
```bash
cd brzy_medical_export
pip install -r requirements.txt  # 安装依赖
python app/main.py               # 启动开发服务器
alembic upgrade head             # 运行数据库迁移
```

### 一键启动
```bash
./scripts/start_dev.sh           # 同时启动前后端开发环境
```

## 核心业务流程

### 扫码登录流程
1. Web端生成二维码（包含sessionId）
2. iOS App扫码获取sessionId
3. WebSocket建立连接进行实时通信
4. JWT Token认证完成登录

### 医案导出流程
1. 用户筛选医案数据
2. 异步生成PDF文件
3. ReportLab处理中文字体渲染
4. 提供下载链接

## 数据库结构

### 主要表结构
- `sessions`: 扫码登录会话
- `users`: 用户信息
- `medical_records`: 医案记录
- `patients`: 患者信息

### 数据库迁移
使用Alembic进行版本管理，迁移文件位于 `alembic/versions/`

## API设计规范

### RESTful API结构
- `/api/auth/` - 认证相关接口
- `/api/medical/` - 医案数据接口
- `/api/export/` - 导出功能接口
- `/ws/` - WebSocket连接端点

### 响应格式
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "error_code": null
}
```

## 前端状态管理

### Pinia Store结构
- `useAuthStore`: 用户认证状态
- `useMedicalStore`: 医案数据状态
- `useExportStore`: 导出功能状态

### 路由权限控制
在 `router/index.js` 中配置路由守卫，未登录用户自动重定向到登录页面

## 部署配置

### CNB 云原生构建部署（推荐）

#### 制品构建
- **触发方式**: 推送代码到 `release` 分支
- **构建平台**: CNB (Cloud Native Build)
- **制品库**: docker.cnb.cool
- **前端制品**: `docker.cnb.cool/{namespace}/brzy-medical-frontend:{version}`
- **后端制品**: `docker.cnb.cool/{namespace}/brzy-medical-backend:{version}`

#### 配置外置化
项目采用配置外置化设计，制品不包含敏感配置：
- 构建时排除所有 `.env*` 文件
- 运行时通过环境变量注入配置
- 支持多环境部署（开发/测试/生产）

#### 环境变量
必需的运行时环境变量：
- `DATABASE_URL`: 数据库连接字符串
- `REDIS_URL`: Redis连接字符串
- `JWT_SECRET_KEY`: JWT密钥
- `ORIGINAL_API_BASE_URL`: 原始API地址

#### 部署方式
1. **1Panel 部署（推荐）**: 从 CNB 制品库拉取镜像直接部署
2. **Docker Compose**: 使用 `deployment/` 目录下的配置
3. **Kubernetes**: 标准容器化部署

### 监控端点
- `/health` - 系统健康检查
- `/metrics` - 性能指标（如果配置）

## 中文处理特性

### PDF中文字体
使用 `STSong-Light` 和 `STHeiti` 字体支持中文渲染

### 前端国际化
Element Plus默认使用中文本地化配置

## 开发注意事项

### 代码规范
- 前端使用ESLint进行代码检查
- 后端遵循FastAPI和Python PEP 8规范
- 提交前运行lint命令检查代码

### WebSocket连接管理
WebSocket连接需要正确处理断线重连和错误处理

### 异步操作
后端大量使用async/await，注意异步上下文的正确处理

### 数据库事务
涉及多表操作时使用数据库事务确保数据一致性

### CNB 云原生构建
- 使用 `.cnb.yml` 配置文件定义构建流程
- 前后端分离构建，生成独立制品
- 配置完全外置化，不打包敏感信息
- 支持缓存优化，提升构建性能
- 集成安全扫描和健康检查